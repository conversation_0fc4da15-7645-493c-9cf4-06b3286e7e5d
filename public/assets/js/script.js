$(document).ready(function () {


    // **** search dropdown ****


    // Toggle the search tooltip on icon click
    $('#search-icon').click(function (e) {
        e.stopPropagation(); // Prevents the click event from bubbling up
        $('#mobile-search-tooltip').toggleClass('hidden'); // Toggle tooltip visibility
    });

    // Close the tooltip when clicking outside
    // $(document).click(function (e) {
    //     if (!$(e.target).closest('#mobile-search-tooltip, #search-icon').length) {
    //         $('#mobile-search-tooltip').addClass('hidden');
    //     }
    // });


    // **** language dropdown ****


    // **** modal ****

    // Toggle the modal when the notification button is clicked
    $(document).on('click', '.modal-button', function (e) {
        e.stopPropagation(); // Prevent the event from bubbling up
        $('.modal').addClass('hidden');

        var targetModal = $(this).data('target'); // Get the target modal ID from data attribute
        $(targetModal).toggleClass('hidden'); // Toggle visibility of the modal
    });

    // Close the modal when the close button is clicked
    $(document).on('click', '.modal-close', function (e) {
        e.stopPropagation(); // Prevent the event from bubbling up
        $(this).closest('.modal').addClass('hidden'); // Hide the modal
    });

    // Hide the modal when clicking outside of it
    $(document).click(function (e) {
        var target = $(e.target);
        if (!target.closest('.modal-body').length && !target.closest('.modal-button').length) {
            $('.modal').addClass('hidden'); // Hide the modal if clicking outside
        }
    });
});
