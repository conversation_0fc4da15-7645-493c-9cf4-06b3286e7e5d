
body,
html {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
}

div#content {
    display: flex;
    justify-content: center;
}

/* #worldMap {
    height: 80vh;
    width: 100%;
} */

#player {
    padding-top: 1rem !important;
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    background: #ffffffde;
    padding: 20px;
    padding-bottom: 10px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 999;
    border-radius: 10px;
    backdrop-filter: blur(5px);
    width: 60%;
    height: fit-content;
    min-height: 200px;
    align-items: center;
    justify-content: center;
    margin: auto;
    flex-direction: column;
}

#deviceInfo {
    font-weight: bold;
    margin-bottom: 5px;
}

#dateDropdown {
    margin: 10px 0;
}

#seek-container {
    width: 100%;
    background: #450099;
    padding: 10px;
    display: flex;
    align-items: center;
}

#seekbar {
    width: 100%;
}

#seekbar {
    flex-grow: 1;
    margin: 0 10px;
    -webkit-appearance: none;
    height: 5px;
    background: #d3d3d3;
    outline: none;
    opacity: 0.7;
    transition: opacity .2s;
    width: 100%;
}

#seekbar:hover {
    opacity: 1;
}

#seekbar::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 15px;
    height: 15px;
    background: #fff;
    cursor: pointer;
    border-radius: 50%;
}

#seekbar::-moz-range-thumb {
    width: 15px;
    height: 15px;
    background: #450099;
    cursor: pointer;
    border-radius: 50%;
}

#currentTime {
    color: white;
    font-size: 14px;
}


#map-btn,
#close-geofence-btn {
    cursor: pointer;
    background: #f44336;
    color: white;
    border: none;
 
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1111;

    right: 50px;
    width: fit-content;
    display: flex;
    align-items: center;
    border-radius: 6px;
    font-weight: bold;
    font-size: 11px;
    padding: 4px 5px;
    gap: 3px;

}

.close-btn {
    right: 15px;
    position: absolute;
    top: 15px;
}

#map-btn {
    right: 50px;
    background: #2196F3;
}

#close-geofence-btn {
    background: #f44336;
    display: none;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 25px;
    cursor: pointer;
    margin: 0px 30px;
}

.toggle-switch input[type="checkbox"] {
    display: none;
}

.toggle-switch-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ddd;
    border-radius: 20px;
    box-shadow: inset 0 0 0 2px #ccc;
    transition: background-color 0.3s ease-in-out;
}

.toggle-switch-handle {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 25px;
    height: 25px;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease-in-out;
}

.toggle-switch::before {
    content: "";
    position: absolute;
    top: -25px;
    right: -35px;
    font-size: 12px;
    font-weight: bold;
    color: #aaa;
    text-shadow: 1px 1px #fff;
    transition: color 0.3s ease-in-out;
}

.toggle-switch input[type="checkbox"]:checked+.toggle-switch-handle {
    transform: translateX(30px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2), 0 0 0 3px #450099;
}

.toggle-switch input[type="checkbox"]:checked+.toggle-switch-background {
    background-color: #450099;
    box-shadow: inset 0 0 0 2px #450099;
}

.toggle-switch input[type="checkbox"]:checked+.toggle-switch:before {
    content: "On";
    color: #450099;
    right: -15px;
}

.toggle-switch input[type="checkbox"]:checked+.toggle-switch-background .toggle-switch-handle {
    transform: translateX(30px);
}

/* Geofence */
.leaflet-control-button {
    width: 25px;
    height: 25px;
    line-height: 30px;
    text-align: center;
    background-color: white;
    border-radius: 4px;
    color: #333;
    text-decoration: none;
}

#toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    background-color: #333;
    color: white;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 10px;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    z-index: 9999;
}

.toast.show {
    opacity: 1;
}

@media (max-width:500px) {
    #player {
        width: 90%;
        padding-top: 1.5rem !important;

    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 65px;
        height: 30px;
        cursor: pointer;
        margin: 0px 10px;
    }


    .toggle-switch-handle {
        width: 25px;
        height: 25px;
        top: 2px;
    }


    .toggle-switch input[type="checkbox"]:checked+.toggle-switch-handle {
        transform: translateX(35px);
    }


    .toggle-switch input[type="checkbox"]:checked+.toggle-switch-background .toggle-switch-handle {
        transform: translateX(38px);
    }
}
