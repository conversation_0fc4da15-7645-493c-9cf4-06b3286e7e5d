<svg width="52" height="52" viewBox="0 0 52 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_94_797)">
<rect x="38" y="36" width="24" height="24" rx="12" transform="rotate(-180 38 36)" fill="#450099" shape-rendering="crispEdges"/>
<rect x="38.5" y="36.5" width="25" height="25" rx="12.5" transform="rotate(-180 38.5 36.5)" stroke="white" shape-rendering="crispEdges"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M23.8343 27.7657C23.5219 27.4533 23.5219 26.9467 23.8343 26.6343L26.4687 24L23.8343 21.3657C23.5219 21.0533 23.5219 20.5467 23.8343 20.2343C24.1468 19.9219 24.6533 19.9219 24.9657 20.2343L28.1657 23.4343C28.4781 23.7467 28.4781 24.2533 28.1657 24.5657L24.9657 27.7657C24.6533 28.0781 24.1468 28.0781 23.8343 27.7657Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_94_797" x="0" y="0" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_94_797"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_94_797"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_94_797" result="shape"/>
</filter>
</defs>
</svg>
