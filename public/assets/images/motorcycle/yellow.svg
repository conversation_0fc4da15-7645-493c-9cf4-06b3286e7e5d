<svg width="62" height="75" viewBox="0 0 62 75" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_70_356)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31 6C42.598 6 52 15.402 52 27C52 28.4157 51.8599 29.7986 51.5929 31.1358C49.7394 45.1032 31.1104 61 31.1104 61C31.1104 61 15.2569 47.4717 11.3446 34.4096C10.4755 32.1054 10 29.6083 10 27C10 15.402 19.402 6 31 6Z" fill="#F4BD50"/>
<path d="M48 27C48 17.6112 40.3888 10 31 10C21.6112 10 14 17.6112 14 27C14 36.3888 21.6112 44 31 44C40.3888 44 48 36.3888 48 27Z" fill="white"/>
<g clip-path="url(#clip0_70_356)">
<path d="M36.2 24.525L34.425 22.75L33.0917 21.4167C32.9333 21.2583 32.7167 21.1667 32.5 21.1667H30C29.5417 21.1667 29.1667 21.5417 29.1667 22C29.1667 22.4583 29.5417 22.8333 30 22.8333H32.1583L33.825 24.5H24.1667C21.8333 24.5 20 26.3333 20 28.6667C20 31 21.8333 32.8333 24.1667 32.8333C26.2167 32.8333 27.875 31.425 28.25 29.5H28.9333C29.375 29.5 29.8 29.325 30.1083 29.0083L31.925 27.1917C31.7583 27.6417 31.6667 28.1417 31.6667 28.6667C31.6667 31 33.5 32.8333 35.8333 32.8333C38.1667 32.8333 40 31 40 28.6667C40 26.4583 38.3583 24.6917 36.2 24.525ZM24.1667 29.5H26.5167C26.1667 30.4583 25.2333 31.1667 24.1667 31.1667C22.8083 31.1667 21.6667 30.025 21.6667 28.6667C21.6667 27.3083 22.8083 26.1667 24.1667 26.1667C25.2333 26.1667 26.1667 26.875 26.5167 27.8333H24.1667C23.7083 27.8333 23.3333 28.2083 23.3333 28.6667C23.3333 29.125 23.7083 29.5 24.1667 29.5ZM35.8333 31.1667C34.45 31.1667 33.3333 30.05 33.3333 28.6667C33.3333 27.2833 34.45 26.1667 35.8333 26.1667C37.2167 26.1667 38.3333 27.2833 38.3333 28.6667C38.3333 30.05 37.2167 31.1667 35.8333 31.1667Z" fill="#F4BD50"/>
</g>
</g>
<defs>
<filter id="filter0_d_70_356" x="0" y="0" width="62" height="75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_70_356"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_70_356" result="shape"/>
</filter>
<clipPath id="clip0_70_356">
<rect width="20" height="20" fill="white" transform="translate(20 17)"/>
</clipPath>
</defs>
</svg>
