<svg width="62" height="75" viewBox="0 0 62 75" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_70_350)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31 6C42.598 6 52 15.402 52 27C52 28.4157 51.8599 29.7986 51.5929 31.1358C49.7394 45.1032 31.1104 61 31.1104 61C31.1104 61 15.2569 47.4717 11.3446 34.4096C10.4755 32.1054 10 29.6083 10 27C10 15.402 19.402 6 31 6Z" fill="#61C454"/>
<path d="M48 27C48 17.6112 40.3888 10 31 10C21.6112 10 14 17.6112 14 27C14 36.3888 21.6112 44 31 44C40.3888 44 48 36.3888 48 27Z" fill="white"/>
<g clip-path="url(#clip0_70_350)">
<path d="M37.2 24.525L35.425 22.75L34.0917 21.4167C33.9333 21.2583 33.7167 21.1667 33.5 21.1667H31C30.5417 21.1667 30.1667 21.5417 30.1667 22C30.1667 22.4583 30.5417 22.8333 31 22.8333H33.1583L34.825 24.5H25.1667C22.8333 24.5 21 26.3333 21 28.6667C21 31 22.8333 32.8333 25.1667 32.8333C27.2167 32.8333 28.875 31.425 29.25 29.5H29.9333C30.375 29.5 30.8 29.325 31.1083 29.0083L32.925 27.1917C32.7583 27.6417 32.6667 28.1417 32.6667 28.6667C32.6667 31 34.5 32.8333 36.8333 32.8333C39.1667 32.8333 41 31 41 28.6667C41 26.4583 39.3583 24.6917 37.2 24.525ZM25.1667 29.5H27.5167C27.1667 30.4583 26.2333 31.1667 25.1667 31.1667C23.8083 31.1667 22.6667 30.025 22.6667 28.6667C22.6667 27.3083 23.8083 26.1667 25.1667 26.1667C26.2333 26.1667 27.1667 26.875 27.5167 27.8333H25.1667C24.7083 27.8333 24.3333 28.2083 24.3333 28.6667C24.3333 29.125 24.7083 29.5 25.1667 29.5ZM36.8333 31.1667C35.45 31.1667 34.3333 30.05 34.3333 28.6667C34.3333 27.2833 35.45 26.1667 36.8333 26.1667C38.2167 26.1667 39.3333 27.2833 39.3333 28.6667C39.3333 30.05 38.2167 31.1667 36.8333 31.1667Z" fill="#61C454"/>
</g>
</g>
<defs>
<filter id="filter0_d_70_350" x="0" y="0" width="62" height="75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_70_350"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_70_350" result="shape"/>
</filter>
<clipPath id="clip0_70_350">
<rect width="20" height="20" fill="white" transform="translate(21 17)"/>
</clipPath>
</defs>
</svg>
