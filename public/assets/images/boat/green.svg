<svg width="62" height="75" viewBox="0 0 62 75" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_70_350)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31 6C42.598 6 52 15.402 52 27C52 28.4157 51.8599 29.7986 51.5929 31.1358C49.7394 45.1032 31.1104 61 31.1104 61C31.1104 61 15.2569 47.4717 11.3446 34.4096C10.4755 32.1054 10 29.6083 10 27C10 15.402 19.402 6 31 6Z" fill="#61C454"/>
<path d="M48 27C48 17.6112 40.3888 10 31 10C21.6112 10 14 17.6112 14 27C14 36.3888 21.6112 44 31 44C40.3888 44 48 36.3888 48 27Z" fill="white"/>
<g clip-path="url(#clip0_70_350)">
<path d="M31.0675 17L25.8608 31.7383L35.2917 28.6083L31.0675 17ZM22.5675 32.1858L25.1342 37H36.95L39.4333 32.1858H22.5675Z" fill="#61C454"/>
</g>
</g>
<defs>
<filter id="filter0_d_70_350" x="0" y="0" width="62" height="75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_70_350"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_70_350" result="shape"/>
</filter>
<clipPath id="clip0_70_350">
<rect width="20" height="20" fill="white" transform="translate(21 17)"/>
</clipPath>
</defs>
</svg>
