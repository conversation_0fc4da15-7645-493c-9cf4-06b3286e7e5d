<?php

include('../vendor/autoload.php');

use Uro\TeltonikaFmParser\FmParser;
use Uro\TeltonikaFmParser\Protocol\Tcp\Reply;

use React\Socket\Server;
use React\EventLoop\Factory;

function formatTimestamp($timestamp)
{
    // Get the timestamp from AVL data
    $timestampInMilliseconds = $timestamp;

    // Convert to seconds
    $timestampInSeconds = $timestampInMilliseconds / 1000;

    // Format the date (Example: Y-m-d H:i:s format)
    $formattedDate = date('d-m-Y H:i:s', $timestampInSeconds);

    return $formattedDate;
}




$loop = Factory::create(); // Create an event loop

$parser = new FmParser('tcp');

$socket = new Server('0.0.0.0:2020', $loop);  // Create a TCP server that listens on port 2020

echo "Server started on 0.0.0.0:2020\n";

// Handle incoming connections asynchronously
$socket->on('connection', function ($conn) use ($parser) {

    // Read IMEI asynchronously
    $conn->on('data', function ($data) use ($conn, $parser) {

        // Decode IMEI from the received data
        $imeiObject = $parser->decodeImei($data);

        // Accept packet
        $conn->write(Reply::accept());


        // Read Data (non-blocking)
        $conn->on('data', function ($data) use ($conn, $parser, $imeiObject) {

            // Decode Data Packet
            $payload = fread($conn, 1500);

            $packet = $parser->decodeData($payload);


            // Acknowledge the packet
            $conn->write($parser->encodeAcknowledge($packet));

            // Get IMEI from the object (as string)
            $imei = $imeiObject->getImei();

            // You can now store the decoded data or process it
            echo "Received data from IMEI: " . $imei . "\n";



            // Assuming the class has methods like getPreamble(), getAvlDataArrayLength(), etc.
            $packetArray = [
                $imei => [
                    'avlData' => []
                ]

            ];



            // Loop through the avlData array and structure it for JSON
            foreach ($packet->getAvlDataCollection()->getAvlData() as $avlData) {
                $avlDataArray = [
                    'timestamp' => formatTimestamp($avlData->getTimestamp()),
                    'priority' => $avlData->getPriority(),
                    'gpsElement' => [
                        'longitude' => $avlData->getGpsElement()->getLongitude(),
                        'latitude' => $avlData->getGpsElement()->getLatitude(),
                        'altitude' => $avlData->getGpsElement()->getAltitude(),
                        'angle' => $avlData->getGpsElement()->getAngle(),
                        'satellites' => $avlData->getGpsElement()->getSatellites(),
                        'speed' => $avlData->getGpsElement()->getSpeed()
                    ]
                ];

                foreach ($avlData->getIoElement()->getProperties() as $propertyKey => $ioProperty) {
                    $binaryValue = $ioProperty->getValue()->toUnsigned();

                    // Save to JSON structure
                    $packetArray[$imei]['ioElement'][$propertyKey] = $binaryValue;
                }


                // Add this avlData entry to the collection
                $packetArray[$imei]['avlData'] = $avlDataArray;
            }

            // Convert to JSON
            $json = json_encode($packetArray, JSON_PRETTY_PRINT);

            // Define the file path
            $filePath = 'packet_data.json';

            // Write JSON to file
            if (file_put_contents($filePath, $json)) {
                echo "Data successfully written to {$filePath}";
            } else {
                echo "Error writing data to {$filePath}";
            }
        });
    });
});

$loop->run();  // Start the event loop to listen for connections
