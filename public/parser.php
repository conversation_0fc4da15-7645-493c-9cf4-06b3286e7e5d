<?php

include('../vendor/autoload.php');
// use Illuminate\Support\Facades\Route;
use Uro\TeltonikaFmParser\FmParser;
use Uro\TeltonikaFmParser\Protocol\Tcp\Reply;


$parser = new FmParser('tcp');

// $payload = fread($conn, 1024);

// Decode IMEI
$imei = 'Default_IMEI';
try {
    $imei = $parser->decodeImei('0000000000000044080100000192579b98e00008dc51e8183b08d2001000bc0f0036000c05ef01f0011504c800450105b5000eb600084235fa43101544000002f1000056c2100055ba2300010000dcff');
} catch (\Exception $e) {
    echo ($e->getMessage());
}

// Decode Data Packet
$packet = $parser->decodeData('0000000000000044080100000192579b98e00008dc51e8183b08d2001000bc0f0036000c05ef01f0011504c800450105b5000eb600084235fa43101544000002f1000056c2100055ba2300010000dcff');

// Assuming the class has methods like getPreamble(), getAvlDataArrayLength(), etc.
$packetArray = [
    $imei => [
        'avlData' => []
    ]

];

function formatTimestamp($timestamp)
{
    // Get the timestamp from AVL data
    $timestampInMilliseconds = $timestamp;

    // Convert to seconds
    $timestampInSeconds = $timestampInMilliseconds / 1000;

    // Format the date (Example: Y-m-d H:i:s format)
    $formattedDate = date('d-m-Y H:i:s', $timestampInSeconds);

    return $formattedDate;
}

// Loop through the avlData array and structure it for JSON
foreach ($packet->getAvlDataCollection()->getAvlData() as $avlData) {
    $avlDataArray = [
        'timestamp' => formatTimestamp($avlData->getTimestamp()),
        'priority' => $avlData->getPriority(),
        'gpsElement' => [
            'longitude' => $avlData->getGpsElement()->getLongitude(),
            'latitude' => $avlData->getGpsElement()->getLatitude(),
            'altitude' => $avlData->getGpsElement()->getAltitude(),
            'angle' => $avlData->getGpsElement()->getAngle(),
            'satellites' => $avlData->getGpsElement()->getSatellites(),
            'speed' => $avlData->getGpsElement()->getSpeed()
        ]
    ];

    foreach ($avlData->getIoElement()->getProperties() as $propertyKey => $ioProperty) {
        $binaryValue = $ioProperty->getValue()->toUnsigned();

        // Save to JSON structure
        $packetArray[$imei]['ioElement'][$propertyKey] = $binaryValue;
    }


    // Add this avlData entry to the collection
    $packetArray[$imei]['avlData'] = $avlDataArray;
}

// Convert to JSON
$json = json_encode($packetArray, JSON_PRETTY_PRINT);

// Define the file path
$filePath = __DIR__ . '/packet_data.json';

// Write JSON to file
if (file_put_contents($filePath, $json)) {
    echo "Data successfully written to {$filePath}";
} else {
    echo "Error writing data to {$filePath}";
}


dd($packet);

// $parser = new FmParser('tcp');

// // Replace this with your actual raw data for testing
// $rawData = "000000000000004408010000019257be20b80008e4ad8e18381974003a0071100098000c05ef01f0011504c800450105b5000eb6000842356943100544000002f1000056c2100056003700010000e821"; // Insert your FMB920 raw data here

// // Assuming the raw data contains IMEI info first
// $imei = $parser->decodeImei($rawData);

// // Decode data packet
// $packet = $parser->decodeData($rawData);

// // If you want to test encoding an acknowledgment, you could also do:
// $acknowledgment = $parser->encodeAcknowledge($packet);
// dd( " Parsed Packet:", $packet , " Acknowledgment Data:", $acknowledgment);


// $parser = new FmParser('tcp');
// $socket = stream_socket_server("tcp://0.0.0.0:8043", $errno, $errstr);
// if (!$socket) {
//     throw new \Exception("$errstr ($errno)");
// } else {
//     while ($conn = stream_socket_accept($socket)) {

//         // Read IMEI
//         $payload = fread($conn, 1024);
//         $imei = $parser->decodeImei($payload);

//         // Accept packet
//         fwrite($conn, Reply::accept());

//         // Decline packet
//         // fwrite($conn, Reply::reject());

//         // Read Data
//         $payload = fread($conn, 1024);
//         $packet = $parser->decodeData($payload);

//         // Send acknowledge
//         fwrite($conn, $parser->encodeAcknowledge($packet));

//         // Close connection
//         fclose($conn);
//     }

//     fclose($socket);
// }






// other working code

// function isAuthTeltonika(string $hex)
// {
//     $firstByte = substr($hex, 0, 8);
//     return hexdec($firstByte) !== 0;
// }

// function parseImeiTeltonika(string $hex)
// {
//     $hexImei = substr($hex, 4);
//     $imei = hex2bin($hexImei);
//     $str = '';
//     foreach (str_split(strrev((string)$imei)) as $i => $d) {
//         $str .= $i % 2 !== 0 ? $d * 2 : $d;
//     }
//     if (array_sum(str_split($str)) % 10 === 0 && strlen($imei) == 15) return $imei;
//     return false;
// }


// function parseTeltonika(string $text, &$success)
// {
//     $response = ['info' => []];
//     $responsep['info']['crc'] = substr($text, strlen($text) - 8);
//     $dl = hexdec(substr($text, 8, 8)) * 2;
//     $avl = substr($text, 16, $dl);
//     $pos = 0;
//     $responsep['info']['codec'] = substr($avl, $pos, 2);
//     $pos += 2;
//     $success = $responsep['info']['avlCount'] = hexdec(substr($avl, $pos, 2));
//     $pos += 2;

//     for ($r = 0; $r < $responsep['info']['avlCount']; $r++) {
//         $response[$r] = ['gps' => [], 'io' => [], 'ioAll' => [], 'ioAllHex' => []];
//         /** timestamp: string|ISO Date Time */
//         $response[$r]['dateHex'] = hexdec(substr($avl, $pos, 16));
//         $response[$r]['date'] = date('Y-m-d H:i:s', (int)(hexdec(substr($avl, $pos, 16)) / 1000));
//         $pos += 16;

//         /** priority: integer  */
//         $response[$r]['priority'] = hexdec(substr($avl, $pos, 2));
//         $pos += 2;

//         /** longitude: float */
//         $response[$r]['gps']['longitude'] = hexdec(substr($avl, $pos, 8)) / 10000000;
//         $pos += 8;

//         /** latitude: float */
//         $response[$r]['gps']['latitude'] = hexdec(substr($avl, $pos, 8)) / 10000000;
//         $pos += 8;

//         /** altitude: integer|smallIntager */
//         $response[$r]['gps']['altitude'] = hexdec(substr($avl, $pos, 4));
//         $pos += 4;

//         /** angle integer|smallIntager */
//         $response[$r]['gps']['angle'] = hexdec(substr($avl, $pos, 4));
//         $pos += 4;

//         /** satellites integer|tynyIntager */
//         $response[$r]['gps']['satellites'] = hexdec(substr($avl, $pos, 2));
//         $pos += 2;

//         /** speed: integer|smallIntager */
//         $response[$r]['gps']['speed'] = hexdec(substr($avl, $pos, 4));
//         $pos += 4;

//         /** eventId: integer|tynyIntager - Event IO ID */
//         $response[$r]['eventId'] = hexdec(substr($avl, $pos, 2));
//         $pos += 2;

//         /** ioCount integer|tynyIntager - count of elements */
//         $response[$r]['ioCount'] = hexdec(substr($avl, $pos, 2));
//         $pos += 2;

//         /** oneBytesCount integer|tynyIntager - count of elements with one byte */
//         $response[$r]['io']['oneBytesCount'] = hexdec(substr($avl, $pos, 2));
//         $pos += 2;

//         $response[$r]['io']['oneBytes'] = [];
//         if ($response[$r]['io']['oneBytesCount']) {
//             for ($bi = 0; $bi < $response[$r]['io']['oneBytesCount']; $bi++) {
//                 $id = substr($avl, $pos, 2);
//                 $did = hexdec($id);
//                 $pos += 2;

//                 $val = substr($avl, $pos, 2);
//                 $dval = hexdec($val);
//                 $pos += 2;
//                 $response[$r]['io']['oneBytes'][$did] = $dval;
//                 $response[$r]['ioAllHex'][$did] = $val;
//                 $response[$r]['ioAll'][$did] = $dval;
//             }
//         }

//         /** twoBytesCount integer|tynyIntager - count of elements with two byte */
//         $response[$r]['io']['twoBytesCount'] = hexdec(substr($avl, $pos, 2));
//         $pos += 2;

//         $response[$r]['io']['twoBytes'] = [];
//         if ($response[$r]['io']['twoBytesCount']) {
//             for ($bi = 0; $bi < $response[$r]['io']['twoBytesCount']; $bi++) {
//                 $id = substr($avl, $pos, 2);
//                 $did = hexdec($id);
//                 $pos += 2;

//                 $val = substr($avl, $pos, 4);
//                 $dval = hexdec($val);
//                 $pos += 4;
//                 $response[$r]['io']['twoBytes'][$did] = $dval;
//                 $response[$r]['ioAllHex'][$did] = $val;
//                 $response[$r]['ioAll'][$did] = $dval;
//             }
//         }

//         /** fourBytesCount integer|tynyIntager - count of elements with four byte */
//         $response[$r]['io']['fourBytesCount'] = hexdec(substr($avl, $pos, 2));
//         $pos += 2;

//         $response[$r]['io']['fourBytes'] = [];
//         if ($response[$r]['io']['fourBytesCount']) {
//             for ($bi = 0; $bi < $response[$r]['io']['fourBytesCount']; $bi++) {
//                 $id = substr($avl, $pos, 2);
//                 $did = hexdec($id);
//                 $pos += 2;

//                 $val = substr($avl, $pos, 8);
//                 $dval = hexdec($val);
//                 $pos += 8;
//                 $response[$r]['io']['fourBytes'][$did] = $dval;
//                 $response[$r]['ioAllHex'][$did] = $val;
//                 $response[$r]['ioAll'][$did] = $dval;
//             }
//         }

//         /** eightBytesCount integer|tynyIntager - count of elements with eight byte */
//         $response[$r]['io']['eightBytesCount'] = hexdec(substr($avl, $pos, 2));
//         $pos += 2;

//         $response[$r]['io']['eightBytes'] = [];
//         if ($response[$r]['io']['eightBytesCount']) {
//             for ($bi = 0; $bi < $response[$r]['io']['eightBytesCount']; $bi++) {
//                 $id = substr($avl, $pos, 2);
//                 $did = hexdec($id);
//                 $pos += 2;

//                 $val = substr($avl, $pos, 16);
//                 $dval = hexdec($val);
//                 $pos += 16;
//                 $response[$r]['io']['eightBytes'][$did] = $dval;
//                 $response[$r]['ioAllHex'][$did] = $val;
//                 $response[$r]['ioAll'][$did] = $dval;
//             }
//         }
//     }
//     return $response;
// }

// // Test with raw data
// $rawData = "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";

// $success = false;
// $parsedData = parseTeltonika($rawData, $success);

// if ($success) {
//     // Print parsed data
//     dd($parsedData);
// } else {
//     echo "Parsing failed!";
// }

// die();
