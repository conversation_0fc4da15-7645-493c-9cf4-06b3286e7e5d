import json
import requests

LARAVEL_API_URL = "https://csm.memove.it/api/log-device-event"
NOMINATIM_URL = "http://147.79.114.133/nominatim/reverse"
LARAVEL_COMMAND_API_URL = "https://csm.memove.it/api/save-command-response"

def send_event_to_api(imei, io_data, event_id=None):
    """
    Sends event data to the Laravel API.
    :param imei: Device IMEI
    :param io_data: Parsed IO data
    """
    try:
        payload = {
            "imei": imei,
            "data": io_data,
			"event_id": event_id
        }
        headers = {"Content-Type": "application/json"}
        response = requests.post(LARAVEL_API_URL, data=json.dumps(payload), headers=headers)

        if response.status_code == 200:
            print("Event successfully sent to Laravel API.")
        else:
            print(f"Failed to send event. Status Code: {response.status_code}, Response: {response.text}")
    except Exception as e:
        print(f"Error sending event to Laravel API: {e}")

def update_geofence_state(imei, new_state):
    print("Getting old geofence")
    file_path = "/var/www/csm/public/device_geofence/device_geofence.json"
    try:
        with open(file_path, "r") as file:
            content = file.read()
            if not content.strip():  # Handle empty file
                geofence_data = {}
            else:
                geofence_data = json.loads(content)
    except FileNotFoundError:
        geofence_data = {}
    except json.JSONDecodeError:
        print("Invalid JSON in the geofence file. Reinitializing.")
        geofence_data = {}

    # Get the previous state, default to "out"
    previous_state = geofence_data.get(imei, {}).get("state", "out")
    
    # Always update the geofence data, even if the state hasn't changed
    geofence_data[imei] = {"state": new_state}

    # Write the updated state back to the file
    with open(file_path, "w") as file:
        json.dump(geofence_data, file, indent=4)

    return previous_state, new_state

def log_geofence_event_to_backend(imei, event_data):
    """Send geofence exit event to Laravel backend."""
    api_url = "https://csm.memove.it/api/log-geofence-event"
    payload = {"imei": imei, **event_data}
    response = requests.post(api_url, json=payload)
    print(f"Logged event for IMEI {imei}, Response: {response.status_code}")

def send_to_laravel_api(imei, command, response):
    """
    Sends the decoded response to the Laravel API.
    """
    # Handle both string and bytes input
    if isinstance(response, bytes):
        response_text = response.decode('utf-8', errors='ignore')
    else:
        response_text = response  # Already a string

    payload = {
        "imei": imei,
        "command": command,
        "response": response_text
    }
    try:
        api_response = requests.post(LARAVEL_COMMAND_API_URL, json=payload)
        if api_response.status_code == 200:
            print("Data successfully sent to Laravel API.")
        else:
            print(f"Failed to send data to Laravel API: {api_response.text}")
    except Exception as e:
        print(f"Error sending data to Laravel API: {e}")

def get_address(lat, lon):
    """Fetch address from Nominatim API with caching."""
    try:
        params = {
            "lat": lat,
            "lon": lon,
            "format": "json",
            "addressdetails": 1,
            "accept-language": "it"
        }
        response = requests.get(NOMINATIM_URL, params=params, timeout=3)
        data = response.json()
        return data.get("display_name", None)  # Return full address or None
    except Exception as e:
        print(f"Address lookup failed: {e}")
        return None  # Keep address null if API fails
