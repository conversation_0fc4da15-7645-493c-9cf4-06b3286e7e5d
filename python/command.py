import crcmod.predefined
import binascii
from struct import unpack

def calculate_crc(data: bytes) -> bytes:
    # Use CRC-16/IBM
    crc16_func = crcmod.predefined.mkPredefinedCrcFun('crc-16')
    crc_value = crc16_func(data)
    return crc_value.to_bytes(2, byteorder='big')  # Convert to 2-byte big-endian

def create_codec12_command(command: str) -> bytes:
    # Preamble (4 bytes)
    preamble = b'\x00\x00\x00\x00'
    # Codec ID (1 byte)
    codec_id = b'\x0C'
    # Command Quantity 1 (1 byte)
    command_quantity_1 = b'\x01'
    # Command Type (1 byte)
    command_type = b'\x05'
    # Command in ASCII
    command_bytes = command.encode('ascii')
    # Command Size (4 bytes)
    command_size = len(command_bytes).to_bytes(4, byteorder='big')
    # Command Quantity 2 (1 byte)
    command_quantity_2 = b'\x01'

    # Data to calculate CRC
    data_without_crc = (
        codec_id +
        command_quantity_1 +
        command_type +
        command_size +
        command_bytes +
        command_quantity_2
    )

    # Calculate CRC for `data_without_crc`
    crc = calculate_crc(data_without_crc)

    # Teltonika's requirement: Add two leading zero bytes to the CRC
    crc_with_padding = b'\x00\x00' + crc

    # Data Size (4 bytes, big-endian)
    data_size = len(data_without_crc).to_bytes(4, byteorder='big')

    # Final packet structure
    packet = (
        preamble +
        data_size +
        data_without_crc +
        crc_with_padding
    )
    return packet




class CommandResponse:
    def __init__(self, preamble, data_size, codec_id, response_quantity_1, response_type, response_size, response, response_quantity_2, crc):
        self.preamble = preamble
        self.data_size = data_size
        self.codec_id = codec_id
        self.response_quantity_1 = response_quantity_1
        self.response_type = response_type
        self.response_size = response_size
        self.response = response
        self.response_quantity_2 = response_quantity_2
        self.crc = crc

    def __repr__(self):
        return (
            f"CommandResponse(\n"
            f"  Preamble: {self.preamble}\n"
            f"  Data Size: {self.data_size}\n"
            f"  Codec ID: {self.codec_id}\n"
            f"  Response Quantity 1: {self.response_quantity_1}\n"
            f"  Response Type: {self.response_type}\n"
            f"  Response Size: {self.response_size}\n"
            f"  Response: {self.response.decode('ascii', errors='ignore')}\n"
            f"  Response Quantity 2: {self.response_quantity_2}\n"
            f"  CRC: {hex(self.crc)}\n"
            f")"
        )


def is_codec12_response(hex_response: str) -> bool:
    """Check if the response is a valid Codec12 response format"""
    try:
        # Clean and validate the hex string
        hex_response = ''.join(c for c in hex_response if c in '0123456789abcdefABCDEF')

        # Need at least 15 bytes for basic Codec12 structure
        if len(hex_response) < 30:  # 15 bytes * 2 hex chars
            return False

        response_bytes = binascii.unhexlify(hex_response)

        # Check minimum length
        if len(response_bytes) < 15:
            return False

        # Check preamble (should be 0x00000000)
        preamble = unpack(">I", response_bytes[:4])[0]
        if preamble != 0:
            return False

        # Check codec ID (should be 0x0C for Codec12)
        codec_id = response_bytes[8]
        if codec_id != 0x0C:
            return False

        # Check response type (should be 0x06 for response)
        response_type = response_bytes[10]
        if response_type != 0x06:
            return False

        # Check data size consistency
        # Data size is calculated from Codec ID field to the second command/response quantity field
        data_size = unpack(">I", response_bytes[4:8])[0]
        # Total length should be: preamble (4) + data_size field (4) + data_size content
        expected_total_length = 8 + data_size
        # Allow some tolerance for CRC field variations
        if abs(expected_total_length - len(response_bytes)) > 4:
            return False

        return True

    except (binascii.Error, struct.error, IndexError):
        return False


def decode_command_response(hex_response: str) -> CommandResponse:
    # Clean and validate the hex string
    hex_response = ''.join(c for c in hex_response if c in '0123456789abcdefABCDEF')

    # Convert the hex string to raw bytes
    try:
        response_bytes = binascii.unhexlify(hex_response)
    except binascii.Error as e:
        raise ValueError(f"Invalid hex string: {hex_response}") from e

    # Debugging: Check response length
    print(f"Hex response length: {len(hex_response)}")
    print(f"Response bytes length: {len(response_bytes)}")

    # Validate minimum length for Codec12 response
    if len(response_bytes) < 15:
        print(f"Error: Response too short for Codec12 format (need at least 15 bytes, got {len(response_bytes)})")
        return None

    # Parse the response
    preamble = unpack(">I", response_bytes[:4])[0]
    data_size = unpack(">I", response_bytes[4:8])[0]
    codec_id = response_bytes[8]
    response_quantity_1 = response_bytes[9]
    response_type = response_bytes[10]
    response_size = unpack(">I", response_bytes[11:15])[0]

    # Debugging: Check response_size
    print(f"response_size: {response_size}")

    # Validate Codec12 format
    if codec_id != 0x0C:
        print(f"Error: Not a Codec12 response (codec_id: 0x{codec_id:02X})")
        return None

    if response_type != 0x06:
        print(f"Error: Not a response type (response_type: 0x{response_type:02X})")
        return None

    # Handle case where response_size is larger than available data length
    if response_size > len(response_bytes) - 15:
        print(f"Error: response_size ({response_size}) is larger than available data length ({len(response_bytes) - 15})")
        return None

    response_start = 15
    response_end = response_start + response_size

    # Ensure response_end is within bounds
    if response_end > len(response_bytes):
        print(f"Error: response_end ({response_end}) exceeds response_bytes length ({len(response_bytes)})")
        return None

    # The response quantity 2 and CRC are at the very end of the packet
    # Last 5 bytes: response_quantity_2 (1 byte) + CRC (4 bytes)
    if len(response_bytes) < 5:
        print(f"Error: Not enough bytes for response quantity and CRC")
        return None

    response_quantity_2 = response_bytes[-5]
    crc = unpack(">I", response_bytes[-4:])[0]

    # Adjust response content to exclude the quantity and CRC that might be included
    # The actual response content should be everything between response_start and the last 5 bytes
    actual_response_end = len(response_bytes) - 5
    if actual_response_end > response_start:
        response = response_bytes[response_start:actual_response_end]
    else:
        response = response_bytes[response_start:response_end]

    return CommandResponse(
        preamble=preamble,
        data_size=data_size,
        codec_id=codec_id,
        response_quantity_1=response_quantity_1,
        response_type=response_type,
        response_size=response_size,
        response=response,
        response_quantity_2=response_quantity_2,
        crc=crc,
    )


# Example usage:
# command = "getinfo"
# encoded_command = create_codec12_command(command)
# print("Encoded Command (hex):", encoded_command.hex())

# Example Codec12 response decoding:
# hex_response = "00000000000000900C010600000088494E493A323031392F372F323220373A3232205254433A323031392F372F323220373A3533205253543A32204552523A312053523A302042523A302043463A302046473A3020464C3A302054553A302F302055543A3020534D533A30204E4F4750533A303A3330204750533A312053415430205253333A52463A36352053463A31204D443A30010000C78F"
# if is_codec12_response(hex_response):
#     decoded_response = decode_command_response(hex_response)
#     if decoded_response:
#         print(f"Response: {decoded_response.response.decode('ascii', errors='ignore')}")
