import datetime
import decimal
import struct
import pytz
from shapely.geometry import Point, Polygon
from geopy.distance import geodesic

####################################################
###############_Coordinates_Function_###############
####################################################

def coordinate_formater(hex_coordinate): #may return too large longitude need to test this more
	coordinate = int(hex_coordinate, 16)
	dec_coordinate = coordinate / 10000000
	if coordinate & (1 << 31):
		dec_coordinate *= -1
	else:
		pass
	return dec_coordinate


####################################################
###############____JSON_Functions____###############
####################################################

def parse_custom_datetime(date_str):
    try:
        return datetime.datetime.strptime(date_str.split(" (")[0], "%H:%M:%S %d-%m-%Y")
    except ValueError:
        return None

# geofence checker
def is_point_in_geofence(current_point, coordinates, geofence_type):
    """
    Check if the point is inside the geofence.
    Handles both polygon and circular geofences.
    """
    if geofence_type == "polygon":  # Polygon Geofence
        polygon = Polygon([(lon, lat) for lat, lon in coordinates])  # Ensure correct lat/lon order
        return polygon.contains(current_point)

    elif geofence_type == "circle":  # Circle Geofence
        center_lat, center_lon, radius = coordinates  # Extract circle center and radius
        center_point = (center_lat, center_lon)
        distance = geodesic((current_point.y, current_point.x), center_point).meters
        return distance <= radius

    return False  # Invalid geofence type



####################################################
###############____TIME_FUNCTIONS____###############
####################################################

def time_stamper():
	current_server_time = datetime.datetime.now()	
	server_time_stamp = current_server_time.strftime('%H:%M:%S %d-%m-%Y')
	return server_time_stamp

def device_time_stamper(timestamp):
    # Convert the hex timestamp to milliseconds and then to seconds
    timestamp_ms = int(timestamp, 16) / 1000
    
    # Use timezone-aware datetime for UTC
    timestamp_utc = datetime.datetime.fromtimestamp(timestamp_ms, datetime.timezone.utc)
    
    # Define the timezone for Italy
    italy_timezone = pytz.timezone('Europe/Rome')
    
    # Convert UTC to Italy timezone
    timestamp_italy = timestamp_utc.astimezone(italy_timezone)
    
    # Format both timestamps
    formatted_timestamp_italy = timestamp_italy.strftime("%d/%m/%Y %H:%M")  # Italian format
    formatted_timestamp_utc = timestamp_utc.strftime("%d/%m/%Y %H:%M")  # UTC format
    
    # Combine both formatted timestamps
    formatted_timestamp = f"{formatted_timestamp_italy} (Italy) / {formatted_timestamp_utc} (UTC)"
    
    return formatted_timestamp

def time_stamper_for_json():
    # Define the timezone for Italy
    italy_timezone = pytz.timezone('Europe/Rome')
    
    # Get the current time in Italy's timezone
    current_italy_time = datetime.datetime.now(italy_timezone)
    
    # Format the time in the desired Italian format
    italy_time_stamp = current_italy_time.strftime('%d/%m/%Y %H:%M:%S')
    
    return italy_time_stamp


def record_delay_counter(timestamp):
	timestamp_ms = int(timestamp, 16) / 1000
	current_server_time = datetime.datetime.now().timestamp()
	return f"{int(current_server_time - timestamp_ms)} seconds"

####################################################
###############_PARSE_FUNCTIONS_CODE_###############
####################################################

def parse_data_integer(data):
	return int(data, 16)

def int_multiply_01(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.1'))

def int_multiply_001(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.01')) 

def int_multiply_0001(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.001'))

def signed_no_multiply(data): #need more testing of this function
	try:
		binary = bytes.fromhex(data.zfill(8))
		value = struct.unpack(">i", binary)[0]
		return value
	except Exception as e:
		print(f"unexpected value received in function '{data}' error: '{e}' will leave unparsed value!")
		return f"0x{data}"

parse_functions_dictionary = { #this must simply be updated with new AVL IDs and their functions
	240: parse_data_integer,
	239: parse_data_integer,
	80: parse_data_integer,
	21: parse_data_integer,
	200: parse_data_integer,
	69: parse_data_integer,
	181: int_multiply_01,
	182: int_multiply_01,
	66: int_multiply_0001,
	24: parse_data_integer,
	205: parse_data_integer,
	206: parse_data_integer,
	67: int_multiply_0001,
	68: int_multiply_0001,
	241: parse_data_integer,
	299: parse_data_integer,
	16: parse_data_integer,
	1: parse_data_integer,
	9: int_multiply_0001,
	179: parse_data_integer,
	12: int_multiply_0001,
	13: int_multiply_001,
	17: signed_no_multiply,
	18: signed_no_multiply,
	19: signed_no_multiply,
	11: parse_data_integer,
	10: parse_data_integer,
	2: parse_data_integer,
	3: parse_data_integer,
	6: int_multiply_0001,
	180: parse_data_integer,
	113: parse_data_integer,
	48: parse_data_integer,
	246: parse_data_integer,
	247: parse_data_integer,
	252: parse_data_integer,
	318: parse_data_integer,
	255: parse_data_integer,
	249: parse_data_integer,
}

def sorting_hat(key, value):
	if key in parse_functions_dictionary:
		parse_function = parse_functions_dictionary[key]
		return parse_function(value)
	else:
		return f"0x{value}"
	