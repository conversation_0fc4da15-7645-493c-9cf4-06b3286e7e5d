import socket
import json
import os
import datetime
import struct
import threading
from shapely.geometry import Point, Polygon
from geopy.distance import geodesic
from command import create_codec12_command, decode_command_response, is_codec12_response  # Import your command script
import time
import pytz
from helpers import coordinate_formater, parse_custom_datetime, is_point_in_geofence, time_stamper, device_time_stamper, time_stamper_for_json, record_delay_counter, parse_data_integer, sorting_hat
from backendFunctions import send_event_to_api, update_geofence_state, log_geofence_event_to_backend, get_address, send_to_laravel_api



HOST = '0.0.0.0'  #function may not work in Linux systems, change to string with IP adress example: "***********"
PORT = 2020  #change this to your port

COMMAND_QUEUE_PATH = "/var/www/csm/public/command/queue.json"

queue_lock = threading.Lock()
COMMAND_COOLDOWN = 5  # seconds

# Dictionary to cache last known location per IMEI
last_known_location = {}

# handles the connection with each client individually in a thread
def handle_client(conn, addr):
    """
    Handles the connection with each client individually in a thread.
    """
    print(f"Connected by {addr}")
    device_imei = "default_IMEI"
    conn.settimeout(5)
    last_command_time = 0

    while True:
        try:
            data = conn.recv(10240)  # receive data from the client
            print(f"Data received from {addr}: {data.hex()}")
			

            if not data:  # if no data, break out of the loop
                break

            elif imei_checker(data.hex()) != False:
                device_imei = ascii_imei_converter(data.hex())
                conn.sendall((1).to_bytes(1, byteorder="big"))
                print(f"IMEI received {device_imei}")
                print(f"IMEI response sent to {addr}")

            elif is_codec12_response(data.hex()):
                # This is a Codec12 command response
                print("Received Codec12 command response")
                decoded_response = decode_command_response(data.hex())
                if decoded_response and decoded_response.response:
                    response_text = decoded_response.response.decode('ascii', errors='ignore')
                    print(f"Decoded Response: {response_text}")
                    # Send the payload to Laravel API (we'll need to track which command this responds to)
                    # For now, we'll use a generic command identifier
                    send_to_laravel_api(device_imei, "command_response", response_text)
                else:
                    print("Failed to decode Codec12 response")

            elif codec_8e_checker(data.hex().replace(" ", "")) != False:
                record_number = codec_parser_trigger(data.hex(), device_imei, "SERVER")
                record_response = (record_number).to_bytes(4, byteorder="big")
                conn.sendall(record_response)
                print(f"Record response sent to {addr}")

                # After processing data packet, check for pending commands
                current_time = time.time()
                if current_time - last_command_time < COMMAND_COOLDOWN:
                    time.sleep(COMMAND_COOLDOWN - (current_time - last_command_time))

                # Fetch the next command for the IMEI
                command_data, queue_data = fetch_next_command(device_imei)
                if command_data:
                    command = command_data["command"]
                    if command:
                        encoded_command = create_codec12_command(command)
                        print(f"Sending command to device: {command}")
                        conn.sendall(encoded_command)
                        # Save updated queue data (remove the sent command)
                        save_queue(queue_data)
						
						conn.settimeout(5)

						try:
							response_data = conn.recv(10240)
							print(f"Response received: {response_data.hex()}")

							decoded_response = decode_command_response(response_data.hex())
							
							send_to_laravel_api(device_imei, command, decoded_response.response.decode('ascii', errors='ignore'))
						except Exception as e:
							print(f"Error sending data to Laravel API: {e}")

                        last_command_time = time.time()
                    else:
                        print(f"Command data empty for IMEI {device_imei}")
                else:
                    print(f"No commands in queue for IMEI {device_imei}")


            else:
                print(f"No expected data received from {addr} - dropping connection")
                break

        except socket.timeout:
            print(f"Connection timed out with {addr}")
            break

    conn.close()  # close the connection after finishing with the client


def input_trigger():
	print("Paste full 'Codec 8' packet to parse it or:")
	print("Type SERVER to start the server or:")
	print("Type EXIT to stop the program")
	device_imei = "default_IMEI"
	user_input = input("waiting for input: ")
	if user_input.upper() == "EXIT":
		print(f"exiting program............")
		exit()	

	elif user_input.upper() == "SERVER":
		start_server_trigger()
	else:		
		try:
			if codec_8e_checker(user_input.replace(" ","")) == False:
				print("Wrong input or invalid Codec8 packet")
				print()
				input_trigger()
			else:
				codec_parser_trigger(user_input, device_imei, "USER")
		except Exception as e:
			print(f"error occured: {e} enter proper Codec8 packet or EXIT!!!")
			input_trigger()		

####################################################
###############__CRC16/ARC Checker__################
####################################################

def crc16_arc(data):    
	data_part_length_crc = int(data[8:16], 16)
	data_part_for_crc = bytes.fromhex(data[16:16+2*data_part_length_crc])
	crc16_arc_from_record = data[16+len(data_part_for_crc.hex()):24+len(data_part_for_crc.hex())]  
	
	crc = 0
	
	for byte in data_part_for_crc:
		crc ^= byte
		for _ in range(8):
			if crc & 1:
				crc = (crc >> 1) ^ 0xA001
			else:
				crc >>= 1
	
	if crc16_arc_from_record.upper() == crc.to_bytes(4, byteorder='big').hex().upper():
		print ("CRC check passed!")
		print (f"Record length: {len(data)} characters // {int(len(data)/2)} bytes")
		return True
	else:
		print("CRC check Failed!")
		return False

####################################################

def codec_8e_checker(codec8_packet):
	if str(codec8_packet[16:16+2]).upper() != "8E" and str(codec8_packet[16:16+2]).upper() != "08":	
		print()	
		print(f"Invalid packet!!!!!!!!!!!!!!!!!!!")		
		return False
	else:
		return crc16_arc(codec8_packet)

def codec_parser_trigger(codec8_packet, device_imei, props):
		try:			
			return codec_8e_parser(codec8_packet.replace(" ",""), device_imei, props)

		except Exception as e:
			print(f"Error occured: {e} enter proper Codec8 packet or EXIT!!!")
			input_trigger()

def imei_checker(hex_imei):
    try:
        ascii_imei = bytes.fromhex(hex_imei[4:]).decode()
        return len(ascii_imei) == 15
    except:
        return False

def ascii_imei_converter(hex_imei):
	return bytes.fromhex(hex_imei[4:]).decode()

def start_server_trigger():
    print("Starting server!")
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind((HOST, PORT))
        s.listen()  # listen for incoming connections
        print(f"Server listening on {HOST}:{PORT}")

        while True:
            conn, addr = s.accept()  # accept a new client connection
            # Start a new thread to handle each client
            client_thread = threading.Thread(target=handle_client, args=(conn, addr))
            client_thread.start()  # start the thread for handling the client
							
# gprs command queue & response handling
def fetch_next_command(imei):
    """
    Fetches the next command for the given IMEI from the queue.json file.
    """
    with queue_lock:  # Ensure thread-safe access
        try:
            with open(COMMAND_QUEUE_PATH, "r") as file:
                command_data = json.load(file)

            if imei in command_data and command_data[imei]:
                # Pop the first command from the queue for the given IMEI
                command_to_send = command_data[imei].pop(0)
                return command_to_send, command_data
            return None, command_data
        except FileNotFoundError:
            print("Command queue file not found.")
            return None, {}
        except json.JSONDecodeError:
            print("Error decoding JSON file.")
            return None, {}

def save_queue(command_data):
    """
    Saves the updated queue back to the JSON file.
    """
    with queue_lock:  # Ensure thread-safe access
        with open(COMMAND_QUEUE_PATH, "w") as file:
            json.dump(command_data, file, indent=4)



####################################################
###############_Codec8E_parser_code_################
####################################################

def codec_8e_parser(codec_8E_packet, device_imei, props): #think a lot before modifying  this function
	print()
#	print (str("codec 8 string entered - " + codec_8E_packet))

	io_dict_raw = {}
#	timestamp = codec_8E_packet[20:36]	
	io_dict_raw["device_IMEI"] = device_imei
	io_dict_raw["last_update"] = time_stamper_for_json()
#	io_dict_raw["_timestamp_"] = device_time_stamper(timestamp)
#	io_dict_raw["_rec_delay_"] = record_delay_counter(timestamp)
	io_dict_raw["data_length"] = "Record length: " + str(int(len(codec_8E_packet))) + " characters" + " // " + str(int(len(codec_8E_packet) // 2)) + " bytes"
	io_dict_raw["_raw_data__"] = codec_8E_packet

	# try: #writing raw DATA dictionary to ./data/data.json
	# 	json_printer_rawDATA(io_dict_raw, device_imei)
	# except Exception as e:
	# 	print(f"JSON raw data writing error occured = {e}")

	zero_bytes = codec_8E_packet[:8]
	print()
	print (str("zero bytes = " + zero_bytes))

	data_field_length = int(codec_8E_packet[8:8+8], 16)
	print (f"data field lenght = {data_field_length} bytes")
	codec_type = str(codec_8E_packet[16:16+2])
	print (f"codec type = {codec_type}")

	data_step = 4
	if codec_type == "08":
		data_step = 2
	else:
		pass

	number_of_records = int(codec_8E_packet[18:18+2], 16)
	print (f"number of records = {number_of_records}")

	record_number = 1
	avl_data_start = codec_8E_packet[20:]
	data_field_position = 0
	while data_field_position < (2*data_field_length-6):				
		io_dict = {}
		io_dict["device_IMEI"] = device_imei		
		io_dict["last_update"] = time_stamper_for_json()
		print()
		print (f"data from record {record_number}")	
		print (f"########################################")

		timestamp = avl_data_start[data_field_position:data_field_position+16]
		io_dict["_timestamp_"] = device_time_stamper(timestamp)		
		print (f"timestamp = {device_time_stamper(timestamp)}")	
		io_dict["_rec_delay_"] = record_delay_counter(timestamp)		
		data_field_position += len(timestamp)

		priority = avl_data_start[data_field_position:data_field_position+2]
		io_dict["priority"] = int(priority, 16)
		print (f"record priority = {int(priority, 16)}")
		data_field_position += len(priority)

		longitude = avl_data_start[data_field_position:data_field_position+8]
	#	io_dict["longitude"] = struct.unpack('>i', bytes.fromhex(longitude))[0]
	#	print (f"longitude = {struct.unpack('>i', bytes.fromhex(longitude))[0]}")
		io_dict["longitude"] = coordinate_formater(longitude)
		print (f"longitude = {coordinate_formater(longitude)}")
		data_field_position += len(longitude)

		latitude = avl_data_start[data_field_position:data_field_position+8]
	#	print (f"latitude = {struct.unpack('>i', bytes.fromhex(latitude))[0]}")
	#	io_dict["latitude"] = struct.unpack('>i', bytes.fromhex(latitude))[0]
		io_dict["latitude"] = coordinate_formater(latitude)
		print (f"latitude = {coordinate_formater(latitude)}")
		data_field_position += len(latitude)

		altitude = avl_data_start[data_field_position:data_field_position+4]
		print(f"altitude = {int(altitude, 16)}")
		io_dict["altitude"] = int(altitude, 16)
		data_field_position += len(altitude)

		angle = avl_data_start[data_field_position:data_field_position+4]
		print(f"angle = {int(angle, 16)}")
		io_dict["angle"] = int(angle, 16)
		data_field_position += len(angle)

		satelites = avl_data_start[data_field_position:data_field_position+2]
		print(f"satelites = {int(satelites, 16)}")
		io_dict["satelites"] = int(satelites, 16)
		data_field_position += len(satelites)

		speed = avl_data_start[data_field_position:data_field_position+4]
		io_dict["speed"] = int(speed, 16)
		print(f"speed = {int(speed, 16)}")
		data_field_position += len(speed)

		# Parse the event ID
		event_io_id = avl_data_start[data_field_position:data_field_position+data_step]
		event_id = int(event_io_id, 16)
		io_dict["eventID"] = int(event_io_id, 16)
		print(f"event ID = {int(event_io_id, 16)}")
		data_field_position += len(event_io_id)

		total_io_elements = avl_data_start[data_field_position:data_field_position+data_step]
		total_io_elements_parsed = int(total_io_elements, 16)
		print(f"total I/O elements in record {record_number} = {total_io_elements_parsed}")
		data_field_position += len(total_io_elements)

		byte1_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte1_io_number_parsed = int(byte1_io_number, 16)
		print(f"1 byte io count = {byte1_io_number_parsed}")
		data_field_position += len(byte1_io_number)		

		if byte1_io_number_parsed > 0:
			i = 1				
			while i <= byte1_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)
				value = avl_data_start[data_field_position:data_field_position+2]

				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				print (f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		byte2_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte2_io_number_parsed = int(byte2_io_number, 16)
		print(f"2 byte io count = {byte2_io_number_parsed}")
		data_field_position += len(byte2_io_number)

		if byte2_io_number_parsed > 0:
			i = 1
			while i <= byte2_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)

				value = avl_data_start[data_field_position:data_field_position+4]
				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				print (f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		byte4_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte4_io_number_parsed = int(byte4_io_number, 16)
		print(f"4 byte io count = {byte4_io_number_parsed}")
		data_field_position += len(byte4_io_number)

		if byte4_io_number_parsed > 0:
			i = 1
			while i <= byte4_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)

				value = avl_data_start[data_field_position:data_field_position+8]
				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		byte8_io_number = avl_data_start[data_field_position:data_field_position+data_step]
		byte8_io_number_parsed = int(byte8_io_number, 16)
		print(f"8 byte io count = {byte8_io_number_parsed}")
		data_field_position += len(byte8_io_number)

		if byte8_io_number_parsed > 0:
			i = 1
			while i <= byte8_io_number_parsed:
				key = avl_data_start[data_field_position:data_field_position+data_step]
				data_field_position += len(key)

				value = avl_data_start[data_field_position:data_field_position+16]
				io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)
				data_field_position += len(value)
				print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				i += 1
		else:
			pass

		if codec_type.upper() == "8E":

			byteX_io_number = avl_data_start[data_field_position:data_field_position+4]
			byteX_io_number_parsed = int(byteX_io_number, 16)
			print(f"X byte io count = {byteX_io_number_parsed}")
			data_field_position += len(byteX_io_number)

			if byteX_io_number_parsed > 0:
				i = 1
				while i <= byteX_io_number_parsed:
					key = avl_data_start[data_field_position:data_field_position+4]
					data_field_position += len(key)

					value_length = avl_data_start[data_field_position:data_field_position+4]
					data_field_position += 4
					value = avl_data_start[data_field_position:data_field_position+(2*(int(value_length, 16)))]
					io_dict[int(key, 16)] = sorting_hat(int(key, 16), value)		
					data_field_position += len(value)
					print(f"avl_ID: {int(key, 16)} : {io_dict[int(key, 16)]}")
				#	print (f"data field postition = {data_field_position}")
				#	print (f"data_field_length = {2*data_field_length}")
					i += 1
			else:
				pass
		else:
			pass

		record_number += 1
		
		try: #writing dictionary to ./data/data.json
			json_printer(io_dict, device_imei)
		except Exception as e:
			print(f"JSON writing error occured = {e}")

		# if event_id != 0 and (event_id == 249 or event_id == 247 or event_id == 246 or event_id == 255 or  event_id == 252 or event_id == 318):
		if event_id in [249, 246, 252, 318]:
			send_event_to_api(device_imei, io_dict)

		# Check if key "9" exists in io_dict and has a value
		if io_dict.get("9"):
			send_event_to_api(device_imei, io_dict, event_id=9)

		# Format coordinates
		latitude = coordinate_formater(latitude)
		longitude = coordinate_formater(longitude)
		current_point = Point(float(longitude), float(latitude))

		# Construct the geofence file path
		geofence_file = f"/var/www/csm/public/geofences/{device_imei}.json"

		# Check if the geofence file exists
		if not os.path.exists(geofence_file):
			print(f"No geofence file found for device {device_imei}. Skipping geofence processing.")
		else:
			try:
				# Load geofence data
				with open(geofence_file, "r") as file:
					geofences = json.load(file)

				print(f"Checking geofences {geofences}")

				# Process each geofence
				for geofence in geofences:
					coordinates = json.loads(geofence["coordinates"])
					geofence_type = geofence["type"]

					inside_geofence = is_point_in_geofence(current_point, coordinates, geofence_type)

					new_state = "in" if inside_geofence else "out"
					prev_state, new_state = update_geofence_state(device_imei, new_state)

					print(f"Checking states {new_state}, {prev_state}")

					if prev_state == "in" and new_state == "out":
						# Log geofence exit event
						log_geofence_event_to_backend(device_imei, {
							"longitude": longitude,
							"latitude": latitude,
							"geofence_id": geofence["id"]
						})
			except json.JSONDecodeError:
				print(f"Invalid JSON in geofence file for device {device_imei}. Skipping geofence processing.")
			except Exception as e:
				print(f"Unexpected error while processing geofence for device {device_imei}: {e}")			



	if props == "SERVER":	

		total_records_parsed = int(avl_data_start[data_field_position:data_field_position+2], 16)
		print()
		print(f"total parsed records = {total_records_parsed}")
		print()
		return int(number_of_records)
	
	else:
		total_records_parsed = int(avl_data_start[data_field_position:data_field_position+2], 16)
		print()
		print(f"total parsed records = {total_records_parsed}")
		print()


		input_trigger()

####################################################
###############_End_of_MAIN_Parser_Code#############
####################################################





# printing device data to JSON file
def json_printer(io_dict, device_imei):
    """Update live & historical data, with address caching to avoid redundant lookups."""
    lat = io_dict.get("latitude")
    lon = io_dict.get("longitude")

    if lat and lon:
        # Check if IMEI exists in cache
        last_data = last_known_location.get(device_imei, {})
        last_lat = last_data.get("latitude")
        last_lon = last_data.get("longitude")
        last_address = last_data.get("address")

        # Only fetch address if location has changed
        if lat != last_lat or lon != last_lon:
            address = get_address(lat, lon)
            last_known_location[device_imei] = {"latitude": lat, "longitude": lon, "address": address}
        else:
            address = last_address  # Use cached address if location unchanged

        io_dict["address"] = address
    else:
        io_dict["address"] = None

    # Update live data
    update_live_data(io_dict, device_imei)
    
    # Store historical data
    store_historical_data(io_dict, device_imei)
    

def sanitize_dict(input_dict):
    """Sanitize the input dictionary to remove duplicate keys."""
    try:
        # Convert dict to JSON string and back to remove any underlying issues
        sanitized_json = json.dumps(input_dict)
        return json.loads(sanitized_json)
    except (TypeError, json.JSONDecodeError) as e:
        print(f"Error sanitizing input data: {e}")
        return {}

# Create a global lock for live_data file access
live_data_lock = threading.Lock()


def update_live_data(io_dict, device_imei):
    live_data_path = "/var/www/csm/public/data/live"
    live_data_file = "live_data.json"
    file_path = os.path.join(live_data_path, live_data_file)
    temp_file_path = file_path + ".tmp"  # Temporary file for atomic write

    # Ensure the directory exists
    os.makedirs(live_data_path, exist_ok=True)

    with live_data_lock:
        # Read existing data safely
        live_data = {}
        if os.path.exists(file_path):
            try:
                with open(file_path, "r") as file:
                    live_data = json.load(file)
            except json.JSONDecodeError:
                print(f"Error decoding JSON in {file_path}, resetting data.")
                live_data = {}

        # Sanitize input dictionary
        io_dict = sanitize_dict(io_dict)

        # Skip update if no new data is provided
        if not io_dict:
            print(f"No new data to update for device {device_imei}. Skipping update.")
            return

        # Ensure the device IMEI exists in live_data
        if device_imei not in live_data:
            live_data[device_imei] = {}

        # Update data from io_dict
        live_data[device_imei].update(io_dict)

        # Write data atomically using a temporary file
        try:
            with open(temp_file_path, "w") as temp_file:
                json.dump(live_data, temp_file, ensure_ascii=False, separators=(',', ':'))

            # Replace old file with the new file (atomic operation)
            os.replace(temp_file_path, file_path)
        except (TypeError, IOError) as e:
            print(f"Error writing JSON data: {e}")


def store_historical_data(io_dict, device_imei):
    history_path = f"/var/www/csm/public/data/history/{device_imei}"
    date_file = "dates.json"
    
    # Define the Italy timezone
    italy_timezone = pytz.timezone('Europe/Rome')
    
    # Get the current date in Italy timezone
    current_date = datetime.datetime.now(italy_timezone).strftime('%d-%m-%Y')
    data_file = f"{current_date}.json"
    
    if not os.path.exists(history_path):
        os.makedirs(history_path)
    
    # Update dates.json
    dates = set()
    date_path = os.path.join(history_path, date_file)
    
    if os.path.exists(date_path):
        with open(date_path, "r") as file:
            dates = set(json.load(file))
    
    dates.add(current_date)
    
    # Sort dates in descending order (latest date first)
    sorted_dates = sorted(dates, key=lambda d: datetime.datetime.strptime(d, '%d-%m-%Y'), reverse=True)
    
    with open(date_path, "w") as file:
        json.dump(sorted_dates, file, separators=(',', ':'))
    
    # Update daily data file
    daily_data = []
    data_path = os.path.join(history_path, data_file)
    
    if os.path.exists(data_path):
        with open(data_path, "r") as file:
            daily_data = json.load(file)
    
    # Check for duplicate data
    if not daily_data or daily_data[-1] != io_dict:
        daily_data.append(io_dict)
        with open(data_path, "w") as file:
            json.dump(daily_data, file, separators=(',', ':'))



####################################################

def fileAccessTest(): #check if script can create files and folders
	try:
		testDict = {}
		testDict["_Writing_Test_"] = "Writing_Test"
		testDict["Script_Started"] = time_stamper_for_json()

		# json_printer(testDict, "file_Write_Test")

		print (f"---### File access test passed! ###---")
		input_trigger()

	except Exception as e:
		print ()
		print (f"---### File access error occured ###---")
		print (f"'{e}'")
		print (f"---### Try running terminal with Administrator rights! ###---")
		print (f"---### Nothing will be saved if you decide to continue! ###---")
		print ()
		input_trigger()


def main():
	fileAccessTest()

if __name__ == "__main__":
	main()
