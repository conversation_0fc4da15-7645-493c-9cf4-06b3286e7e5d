<?php

use Illuminate\Console\Scheduling\Schedule;
// use Illuminate\Foundation\Inspiring;
// use Illuminate\Support\Facades\Artisan;

// Artisan::command('inspire', function () {
//     $this->comment(Inspiring::quote());
// })->purpose('Display an inspiring quote')->hourly();


// app(Schedule::class)->command('check:geofence-events')->everyMinute();
app(Schedule::class)->command('contracts:check-expired')->daily();
app(Schedule::class)->command('cleanup:history')->daily();