<?php

use App\Http\Controllers\API\AppDeviceController;
use App\Http\Controllers\API\AuthController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\DeviceController;
use App\Http\Controllers\API\GeofenceController;
use App\Http\Middleware\AppAuthMiddleware;
use Illuminate\Support\Facades\Log;


Route::controller(AuthController::class)->group(function () {
    Route::post('login', 'login');
});

Route::middleware(['auth:sanctum', AppAuthMiddleware::class])->group(function () {
    Route::controller(AuthController::class)->group(function () {
        Route::get('profile', 'getProfile');
        Route::get('logout', 'logout');
        Route::post('update-fcm-token', 'updateFcmToken');
        Route::post('update-language', 'updateLanguage');
        Route::post('update-profile', 'updateProfile');
        Route::post('update-alerts', 'updateAlerts');
    });

    Route::controller(AppDeviceController::class)->group(function () {
        Route::get('fetch-user-devices', 'fetchUserDevices');
        Route::post('fetch-events', 'fetchEvents');
        Route::post('fetch-notifications', 'fetchNotifications');
        Route::post('notification-response', 'notificationResponse');
        Route::post('lock-unlock-device', 'lockUnlock');
        Route::post('export-history', 'exportPdf');
    });
});


// web apis for device and geofence
Route::controller(DeviceController::class)->group(function () {
    Route::any('fetch-device-type', 'fetchDeviceType');
    Route::any('log-device-event', 'logDeviceEvent');
    Route::any('log-geofence-event', 'logDeviceGeofenceEvent');
    Route::any('voice-response', 'handleResponse')->name('voice-response');
    Route::any('process-response', 'processResponse')->name('process-response');
    Route::any('save-command-response', 'saveCommandResponse');
    Route::any('motor-block-state/{imei?}', 'motorBlockState');
    Route::post('export-history-web', 'exportPdf');
});

// geofence routes
Route::controller(GeofenceController::class)->group(function () {
    Route::any('save-geofence', 'save');
    Route::any('save-automatic-geofence', 'saveCircle');
    Route::any('get-geofence', 'fetch');
    Route::any('get-automatic-geofence', 'fetchAutomaticGeofence');
    Route::any('delete-geofence', 'delete');
});

Route::post('log-geofence-call', function (Request $request) {

    Log::find($request->all());
    $event = $request->input('event');
    $vehicleId = $request->input('vehicle_id');

    if ($event == "1") {
        Log::info("Geofence event TRUE for vehicle: $vehicleId");
        // Process geofence exit
    } elseif ($event == "0") {
        Log::info("Geofence event FALSE for vehicle: $vehicleId");
        // Process geofence not exited
    }

    return response()->json(['status' => 'success']);
});