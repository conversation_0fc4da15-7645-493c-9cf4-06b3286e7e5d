<?php

use App\Http\Middleware\AdminDealerMiddleware;
use App\Http\Middleware\AdminDealerTechnicianMiddleware;
use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\AdminTechnicianMiddleware;
use App\Http\Middleware\AuthMiddleware;
use App\Http\Middleware\AdminOperatorTechnicianMiddleware;
use App\Http\Middleware\OperatorMiddleware;
use App\Http\Middleware\SetLocale;
use App\Livewire\Auth\Login;
use App\Livewire\Auth\ResetPassword;
use App\Livewire\Panel\Admins;
use App\Livewire\Panel\Clients;
use App\Livewire\Panel\CommandQueue;
use App\Livewire\Panel\ContractResponse;
use App\Livewire\Panel\Contracts;
use App\Livewire\Panel\Dashboard;
use App\Livewire\Panel\Ddts;
use App\Livewire\Panel\Dealers;
use App\Livewire\Panel\Devices;
use App\Livewire\Panel\Events;
use App\Livewire\Panel\History;
use App\Livewire\Panel\OperationsCenter;
use Illuminate\Support\Facades\Route;


Route::middleware(SetLocale::class)->group(function () {
    Route::get('/', Login::class)->name('login');
    Route::get('logout', function () {
        auth()->logout();
        return redirect()->route('login');
    })->name('logout');

    Route::get('contract-response/{contract_number}', ContractResponse::class)->name('contract-response');

    Route::get('password-reset/{token}/{email}', ResetPassword::class)->name('password.reset');

    Route::middleware(AuthMiddleware::class)->group(function () {
        Route::get('dashboard', Dashboard::class)->name('dashboard');
        Route::get('devices', Devices::class)->name('devices');
        Route::get('contracts', Contracts::class)->name('contracts');
        Route::get('events', Events::class)->name('events');

        Route::middleware(AdminDealerMiddleware::class)->group(function () {
            Route::get('clients', Clients::class)->name('clients');
        });
        Route::middleware(AdminDealerTechnicianMiddleware::class)->group(function () {
            Route::get('ddts', Ddts::class)->name('ddts');
        });

        Route::middleware(AdminOperatorTechnicianMiddleware::class)->group(function () {
            Route::get('device-history/{deviceImei}', History::class)->name('device_history');
        });
        Route::middleware(OperatorMiddleware::class)->group(function () {
            Route::get('coa', OperationsCenter::class)->name('operations-center');
        });

        Route::middleware(AdminTechnicianMiddleware::class)->group(function () {
            Route::get('dealers', Dealers::class)->name('dealers');
        });

        Route::get('admins', Admins::class)->name('admins')->middleware(AdminMiddleware::class);
        Route::middleware(AdminTechnicianMiddleware::class)->group(function () {
            Route::get('device-command/{imei?}', CommandQueue::class)->name('device-command');
        });
    });
});
