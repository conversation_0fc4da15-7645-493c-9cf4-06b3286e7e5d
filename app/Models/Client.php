<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Client extends Model
{
    protected $fillable = [
        'user_id',
        'company',
        'dealer_id',
        'address',
        'municipality',
        'zip_code',
        'province',
        'tax_code',
        'phone_number',
        'passkey',
        'type',
        'passkey',
        'last_name',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }


    public function devices()
    {
        return $this->hasMany(Device::class);
    }

    public function dealer()
    {
        return $this->belongsTo(Dealer::class);
    }

    public function contracts()
    {
        return $this->hasMany(Contract::class);
    }
}
