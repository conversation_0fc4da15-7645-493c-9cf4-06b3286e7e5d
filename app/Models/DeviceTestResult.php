<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DeviceTestResult extends Model
{
    protected $fillable = [
        'device_id',
        'gps_status',
        'movement_status',
        'ignition_status',
        'engine_status',
        'battery_voltage',
        'ignition_tested_at',
        'location',
        'latitude',
        'longitude',
        'speed',
        'last_update',
        'signal',
        'battery_level',
    ];

    public function device()
    {
        return $this->belongsTo(Device::class);
    }
}
