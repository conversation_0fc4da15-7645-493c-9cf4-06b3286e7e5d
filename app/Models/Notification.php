<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    protected $fillable = ['title', 'content', 'type', 'user_id', 'imei', 'is_geofence_event', 'response_status'];


    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function toArray()
    {
        $array = parent::toArray();
        $array['created_at'] = $this->created_at->format('d/m/Y H:i:s');
        return $array;
    }
}
