<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DeviceLog extends Model
{
    protected $fillable = [
        'device_id',
        'operator_id',
        'log',
        'is_new',

        'contacted_owner',
        'contact_the_force_of_order',
        'false_alaram',
        'other',
        'free_notes',
        'status',
    ];

    /**
     * Relationship with Device.
     */
    public function device()
    {
        return $this->belongsTo(Device::class);
    }

    /**
     * Relationship with User (Operator).
     */
    public function operator()
    {
        return $this->belongsTo(User::class, 'operator_id');
    }
}
