<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Contract extends Model
{
    protected $fillable = [
        'device_id',
        'client_id',
        'dealer_id',
        'duration',
        'signed',
        'start_date',
        'end_date',
        'status',


        'verification_vehicle_type',
        'vehicle_brand',
        'vehicle_model',
        'vehicle_color',
        'vehicle_registration_number',
        'vehicle_registration_date',
        'vehicle_number_plate',
        'vehicle_chassis',
        'frame',
        'vehicle_km',
        'starter_motor_block',
        'contract_number',
        'responded_at',
        'service_details',
        'installation_image',

    ];

    public function dealer()
    {
        return $this->belongsTo(Dealer::class);
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function device()
    {
        return $this->belongsTo(Device::class);
    }
}
