<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'password',
        'role',
        'is_active',
        'language',
        'fcm_token',

        'tampering_alert',
        'speed_limit_exceeded',
        'power_off',
        'geofence_exit',
        'signal_loss',
        'low_battery',
        'contract_expiry',
        'other_alerts',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function dealer()
    {
        return $this->hasOne(Dealer::class);
    }

    public function client()
    {
        return $this->hasOne(Client::class);
    }

    public function logs()
    {
        return $this->hasMany(DeviceLog::class, 'operator_id');
    }


    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }
}
