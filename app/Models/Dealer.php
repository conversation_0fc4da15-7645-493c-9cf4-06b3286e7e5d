<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Dealer extends Model
{

    protected $fillable = [
        'user_id',
        'tax_code',
        'vat',
        'company',
        'address',
        'municipality',
        'zip_code',
        'province',
        'last_name',
        'type',
        'phone_number'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function devices()
    {
        return $this->hasMany(Device::class);
    }

    public function clients()
    {
        return $this->hasMany(Client::class);
    }
    public function ddts()
    {
        return $this->hasMany(Ddt::class);
    }
}
