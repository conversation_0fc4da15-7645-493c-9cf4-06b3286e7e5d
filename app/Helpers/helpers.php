<?php

// json response helper

use GuzzleHttp\Client;

function jsonResponse($status, $data = [], $statusCode = 200)
{
    return response()->json(array_merge([
        'status' => $status,
    ], $data), $statusCode);
}

// validation error response helper
function validationError($errors = [], $message = 'Validation Error', $statusCode = 200)
{
    // If errors are provided, get the first error message.
    if (!empty($errors) && is_object($errors) && $errors->first()) {
        $message = $errors->first(); // Set the message to the first error
    }

    return response()->json([
        'status' => false,
        'message' => $message,
        'errors' => $errors
    ], $statusCode);
}

// Define Aruba API Base URL
define("BASEURL", "https://smspanel.aruba.it/API/v1.0/REST/");
define("MESSAGE_HIGH_QUALITY", "N"); // High-quality message type

/**
 * Authenticate the user and get user_key and session_key
 *
 * @param string $username
 * @param string $password
 * @return array|null Returns array [user_key, session_key] or null on failure
 */
function authenticateUser($username, $password) {
    $url = BASEURL . "login?username=" . urlencode($username) . "&password=" . urlencode($password);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200 || empty($response)) {
        return null;
    }
    
    return explode(";", $response);
}

/**
 * Sends an SMS using the Aruba API
 *
 * @param array $auth Authentication array [user_key, session_key]
 * @param string $message Message content
 * @param array $recipients List of recipients (phone numbers)
 * @param string|null $sender Custom sender name (optional)
 * @param string|null $scheduleTime Scheduled delivery time (optional, format: YYYYMMDDHHMM)
 * @return mixed Returns API response or null on failure
 */
function sendSMS($auth, $message, $recipients, $sender = null, $scheduleTime = null) {
    if (!$auth || count($auth) < 2) {
        return null;
    }

    $payload = [
        "message" => $message,
        "message_type" => MESSAGE_HIGH_QUALITY,
        "returnCredits" => false,
        "recipient" => $recipients,
        "sender" => $sender,
        "scheduled_delivery_time" => $scheduleTime,
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, BASEURL . "sms");
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: application/json",
        "user_key: " . $auth[0],
        "Session_key: " . $auth[1],
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    \Log::info($response);

    if ($httpCode !== 201) {
        return null;
    }

    return json_decode($response, true);
}
