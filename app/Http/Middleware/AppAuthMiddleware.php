<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AppAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated and active
        if (auth()->check() && auth()->user()) {
            $user = auth()->user();
            if ($user && $user->is_active == 1) {
                return $next($request);
            }
            // If any condition fails, return unauthorized JSON response
            return response()->json([
                'status' => false,
                'message' => __('messages.unauthorized_account_deactivated'),
            ], 401);
        } else {
            // Token is invalid or has expired
            return response()->json([
                'status' => false,
                'message' => 'Unauthorized!',
            ], 401);
        }
    }
}
