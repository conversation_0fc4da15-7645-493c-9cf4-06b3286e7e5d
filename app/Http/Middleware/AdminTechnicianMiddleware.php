<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminTechnicianMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check() && (auth()->user()->role == 'admin' || auth()->user()->role == 'technician' || auth()->user()->role == 'warehouse_operator')) {
            return $next($request);
        } else {
            auth()->logout();
            return redirect()->route('login');
        }
    }
}
