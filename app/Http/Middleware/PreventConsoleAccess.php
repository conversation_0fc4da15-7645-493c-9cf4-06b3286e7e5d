<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PreventConsoleAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // // Inject the JavaScript into the response body
        // if ($response->isSuccessful() && strpos($response->headers->get('content-type'), 'text/html') !== false) {
        //     $script = "<script>
        //         document.addEventListener('contextmenu', event => event.preventDefault());
        //         document.onkeydown = function (e) {
        //             if (e.keyCode === 123 || (e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74)) || (e.ctrl<PERSON><PERSON> && e.keyCode === 85)) {
        //                 return false;
        //             }
        //         };
        //     </script>";
        //     $content = str_replace("</body>", $script . "</body>", $response->getContent());
        //     $response->setContent($content);
        // }

        return $response;
    }
}
