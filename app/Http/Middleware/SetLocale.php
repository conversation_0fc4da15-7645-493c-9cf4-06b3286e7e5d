<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Determine which guard is active (staff or company)
        if (Auth::check()) {
            $userLanguage = Auth::user()->language ?? "it";
        } else {
            $userLanguage = session('language', 'it');
            // Set the application locale
        }
        App::setLocale($userLanguage);
        return $next($request);
    }
}
