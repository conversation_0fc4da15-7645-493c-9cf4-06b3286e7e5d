<?php

namespace App\Http\Controllers;

use App\Services\VoipService;
use Illuminate\Http\Request;

class VoipController extends Controller
{
    protected $voipService;

    public function __construct(VoipService $voipService)
    {
        $this->voipService = $voipService;
    }

    public function makeCall(Request $request)
    {
        $request->validate([
            'number' => 'required|string|min:3|max:20'
        ]);

        $result = $this->voipService->makeCall($request->number);

        return response()->json($result);
    }
}
