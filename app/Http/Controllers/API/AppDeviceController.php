<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Command;
use App\Models\Contract;
use App\Models\Device;
use App\Models\DeviceGeofence;
use App\Models\DeviceLog;
use App\Models\Event;
use App\Models\Notification;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AppDeviceController extends Controller
{
    public function fetchUserDevices(Request $request)
    {
        $userDevices = $request->user();
        $devices = [];

        if ($userDevices->role == 'dealer') {
            $userDevices->load('dealer:id,user_id', 'dealer.devices:dealer_id,vehicle_type,model,imei,id,number_plate,motor_block_enabled,vehicle_model,vehicle_brand');
            $devices = $userDevices->dealer?->devices;
        } elseif ($userDevices->role == 'client') {
            $userDevices->load('client:id,user_id', 'client.devices:client_id,vehicle_type,model,imei,id,number_plate,motor_block_enabled,vehicle_model,vehicle_brand');
            $devices = $userDevices->client?->devices;
        }

        // Map devices to include logic for model value
        $devices = $devices?->map(function ($device) {
            $hasGeofence = DeviceGeofence::where('imei', $device->imei)
                ->where('geofence_type', 1)
                ->exists();

            $contract_expiry = Contract::where('device_id', $device->id)->where('client_id', $device->client_id)->latest()->first('end_date')?->end_date;


            return [
                'imei' => $device->imei,
                'vehicle_type' => $device->vehicle_type ?? 'default',
                'model' => $device->model,
                'number_plate' => $device->number_plate,
                'vehicle_model' => $device->vehicle_model,
                'vehicle_brand' => $device->vehicle_brand,
                'motor_block_enabled' => $device->motor_block_enabled,
                'automatic_geofence_enabled' => $hasGeofence,
                'contract_expiry' => $contract_expiry ? Carbon::parse($contract_expiry)->format('d-m-Y') : null,
            ];
        });

        return jsonResponse(true, [
            'devices' => $devices,
        ]);
    }


    public function fetchEvents(Request $request)
    {
        $user = $request->user();
        $devices = $user->client?->devices->pluck('imei')->toArray();

        $events = [];
        $addressCache = []; // Cache for fetched addresses

        if ($devices) {
            $query = Event::select('imei', 'event_message', 'longitude', 'latitude', 'created_at')
                ->whereIn('imei', $devices);

            if ($request->filled('imei')) {
                $query->where('imei', $request->imei);
            }

            if ($request->filled('filter')) {
                $filter = $request->filter;

                if ($filter === 'Today') {
                    $query->whereDate('created_at', now()->toDateString());
                } elseif ($filter === 'Yesterday') {
                    $query->whereDate('created_at', now()->subDay()->toDateString());
                }
            }

            $events = $query->latest()->paginate(10);

            foreach ($events as $event) {
                if (!$event->address) {
                    $key = "{$event->latitude},{$event->longitude}";
                    if (!isset($addressCache[$key])) {
                        $addressCache[$key] = $this->getAddress($event->latitude, $event->longitude);
                    }

                    $event->address = $addressCache[$key];
                }
            }
        }

        return jsonResponse(true, [
            'events' => $events->items(),
            'pagination' => [
                'total' => $events->total(),
                'per_page' => $events->perPage(),
                'current_page' => $events->currentPage(),
                'last_page' => $events->lastPage(),
            ],
        ]);
    }

    public function fetchNotifications(Request $request)
    {
        $user = $request->user();

        $query = Notification::select('id', 'title', 'content', 'type', 'created_at', 'is_geofence_event', 'response_status')
            ->where('user_id', $user->id);

        // Apply date filters
        if ($request->filled('filter')) {
            $filter = $request->filter;

            if ($filter == 'Today' || $filter == 'Oggi') {
                $query->whereDate('created_at', now()->toDateString());
            } elseif ($filter == 'Yesterday' || $filter == 'leri') {
                $query->whereDate('created_at', now()->subDay()->toDateString());
            }
        }

        $notifications = $query->latest()->paginate(15);


        return jsonResponse(true, [
            'notifications' => $notifications->items(),
            'pagination' => [
                'total' => $notifications->total(),
                'per_page' => $notifications->perPage(),
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
            ],
        ]);
    }


    public function notificationResponse(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'response' => 'required|integer|in:1,0',
            'notification_id' => 'required|exists:notifications,id',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $notification = Notification::find($request->notification_id);
        if ($request->response == 1) {
            $notification->update(['response_status' => 'active']);

            $device = Device::where('imei', $notification->imei)->with('client:id,phone_number')->first();

            $event = Event::where('imei', $notification->imei)->where('event_message', 'like', '%Dispositivo%')->latest()->first();

            // Log the event and trigger automated call
            $this->logAction($device->id, $event->event_message);

            if ($device->client?->phone_number) {
                $this->triggerAutomatedCall($device->client);
            }
        } else {
            $notification->update(['response_status' => 'expired']);
        }
        return jsonResponse(true, [
            'message' => __('messages.geofence_reponse_submitted', [], auth()->user()->language ?? 'it'),
        ]);
    }


    public function getAddress($latitude, $longitude)
    {
        $key = "address_cache:{$latitude},{$longitude}";
        $cachedAddress = cache($key);

        if ($cachedAddress) {
            return $cachedAddress;
        }

        $url = "https://nominatim.openstreetmap.org/reverse?format=json&lat={$latitude}&lon={$longitude}";
        $options = [
            "http" => [
                "header" => "User-Agent: MeMove/1.0 (<EMAIL>)\r\n"
            ]
        ];

        $context = stream_context_create($options);
        $response = @file_get_contents($url, false, $context);

        if ($response) {
            $json = json_decode($response, true);
            $address = $json['display_name'] ?? null;

            // Cache the result for 7 days
            cache([$key => $address], now()->addDays(7));

            return $address;
        }

        return null;
    }



    public function lockUnlock(Request $request)
    {
        $validator = validator($request->all(), [
            'imei' => 'required',
            'type' => 'required|in:lock,unlock',
        ]);

        // if validation fails
        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $imei = $request->imei;
        $commandType = $request->type;

        // Check if user IMEI device is authenticated or not
        $user = $request->user();

        $devices = false;

        if ($user->role == 'dealer') {
            $devices = $user->dealer?->devices()?->where('imei', $imei)->exists();
        } elseif ($user->role == 'client') {
            $devices = $user->client?->devices()?->where('imei', $imei)->exists();
        }

        if (!$devices) {
            return jsonResponse(false, [
                'message' => 'Device not found!',
            ]);
        }

        $command = $commandType == 'lock' ? 'setdigout 0' : 'setdigout 1';
        $name = $commandType == 'lock' ? 'Device Locked' : 'Device Unlocked';

        // Check if the command already exists in the queue for the given IMEI
        // $existingCommand = Command::where('imei', $imei)
        //     ->where('command', $command)
        //     ->where('added_in_queue', 1)
        //     ->whereNull('response')
        //     ->exists();

        // if ($existingCommand) {
        //     return jsonResponse(false, [
        //         'message' => __('messages.command_already_in_queue'),
        //     ]);
        // }

        // // Save command to the database
        // Command::create([
        //     'imei' => $imei,
        //     'command' => $command,
        //     'name' => $name,
        // ]);

        // Update the command queue JSON file
        // $this->updateCommandQueue();

        return jsonResponse(true, [
            'message' => $commandType == 'lock' ? __('messages.block_motor_command_sent', [], $user->language ?? 'it') : __('messages.unblock_motor_command_sent', [], $user->language ?? 'it'),
        ]);
    }

    private function updateCommandQueue()
    {
        // Fetch commands that should be added to the queue
        $commands = Command::whereNull('response')
            ->where('added_in_queue', '!=', 2) // Exclude commands marked as deleted
            ->get();

        $queue = [];
        foreach ($commands as $cmd) {
            $queue[$cmd->imei][] = [
                'command' => $cmd->command,
            ];
        }

        $path = public_path('command/queue.json');
        File::ensureDirectoryExists(dirname($path));
        File::put($path, json_encode($queue, JSON_PRETTY_PRINT));

        // Mark commands as added to queue (only those not already deleted)
        Command::whereNull('response')
            ->where('added_in_queue', 0)
            ->update(['added_in_queue' => 1]);
    }

    public function exportPdf(Request $request)
    {
        ini_set('memory_limit', '512M');

        $validator = Validator::make($request->all(), [
            'imei' => 'required|string|exists:devices,imei',
            'date' => 'required|date_format:d-m-Y',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $imei = $request->imei;
        $date = $request->date;
        $filePath = public_path("data/history/{$imei}/{$date}.json");

        if (!File::exists($filePath)) {
            return jsonResponse(false, ['message' => 'No history found for this date']);
        }

        $device = Device::where('imei', $imei)->first();
        $deviceHistory = json_decode(File::get($filePath), true);

        if (empty($deviceHistory)) {
            return jsonResponse(false, ['message' => 'No data available']);
        }

        // Calculate total distance
        $firstOdometer = $deviceHistory[0]['16'] ?? null;
        $lastOdometer = end($deviceHistory)['16'] ?? null;
        $totalDistanceMeters = ($lastOdometer !== null && $firstOdometer !== null)
            ? max(0, $lastOdometer - $firstOdometer)
            : 0;
        $totalDistanceKm = $totalDistanceMeters / 1000;

        $processedHistory = [];
        foreach ($deviceHistory as $entry) {
            $processedHistory[] = [
                'timestamp' => $entry['last_update'] ?? 'N/A',
                'movement' => $entry['240'] ? 'Moving' : 'Stopped',
                'ignition' => $entry['239'] ? 'On' : 'Off',
                'speed' => $entry['speed'] ?? 'N/A',
                'odometer' => $entry['16'] ?? 'N/A',
            ];
            if (count($processedHistory) >= 1000) { // Adjust batch size as needed
                break;
            }
        }

        $pdf = Pdf::loadView('pdf.device-history', [
            'device_history' => $processedHistory,
            'plate' => $device->number_plate ?? 'N/A',
            'total_distance' => number_format($totalDistanceKm, 2) . ' km'
        ])->setPaper('A4', 'portrait'); // Set A4 size



        // return response()->streamDownload(function () use ($pdf) {
        //     echo $pdf->stream();
        // }, "{$imei}_history_{$date}.pdf", [
        //     'Content-Type' => 'application/pdf',
        //     'Content-Disposition' => 'attachment; filename="' . $imei . '_history_' . $date . '.pdf"',
        // ]);

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->output(); // Use output() instead of stream()
        }, "{$imei}_history_{$date}.pdf", [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="' . $imei . '_history_' . $date . '.pdf"',
        ]);
    }

    public function logAction($deviceId, $action)
    {
        if ($deviceId) {
            DeviceLog::create([
                'device_id' => $deviceId,
                'log' => $action,
            ]);
        }
    }

    public function triggerAutomatedCall($customer)
    {
        // AMI connection settings
        $ipServer = "127.0.0.1"; // e.g., "*********" or public IP
        $port = "5038";
        $protocolServer = "tcp";
        $username = "admin"; // From manager.conf
        $password = "_-m4Q1vFOs"; // From manager.conf
        // Internal phone line (SIP extension)
        $internalPhoneline = "convergenze";

        $ws = false; // Set to true if using WebSocket with Asterisk
        // Outbound context defined in extensions.conf

        $context = 'geofence';


        // Define target phone number to call (modify as needed)
        $target = $customer->phone_number; // e.g., "1006" or any external number

        if ($ws) {
            $port .= "/ws";
        }

        $socket = stream_socket_client("$protocolServer://$ipServer:$port");

        if ($target && $socket) {
            // echo "Connected to socket, sending authentication request.\n";
            // Prepare authentication request
            $authenticationRequest = "Action: Login\r\n";
            $authenticationRequest .= "Username: $username\r\n";
            $authenticationRequest .= "Secret: $password\r\n";
            $authenticationRequest .= "Events: off\r\n\r\n";
            // Send authentication request
            $authenticate = stream_socket_sendto($socket, $authenticationRequest);
            if ($authenticate > 0) {
                // Wait for server response
                usleep(200000);
                // Read server response
                $authenticateResponse = fread($socket, 4096);
                if (strpos($authenticateResponse, 'Success') !== false) {
                    // echo "Authenticated to Asterisk Manager Interface. Initiating call.\n";
                    // Prepare originate request
                    $originateRequest = "Action: Originate\r\n";
                    $originateRequest .= "Channel: SIP/$internalPhoneline\r\n";
                    $originateRequest .= "Callerid: AutomatedCall\r\n";
                    $originateRequest .= "Exten: $target\r\n";
                    $originateRequest .= "Context: $context\r\n";
                    $originateRequest .= "Priority: 0\r\n"; // Ensure priority exists in dialplan
                    $originateRequest .= "Async: yes\r\n\r\n";
                    // Send originate request
                    $originate = stream_socket_sendto($socket, $originateRequest);
                    if ($originate > 0) {
                        usleep(200000);
                        $originateResponse = fread($socket, 4096);
                        // \Log::info($originateResponse);
                        if (strpos($originateResponse, 'Success') !== false) {
                            // echo "Call initiated, dialing\n";
                            Log::info('Call initiated, dialing... ' . $target);

                            echo $originateResponse;
                        } else {
                            Log::info("Could not initiate call.\n " . $originateResponse);
                        }
                    } else {
                        Log::info("Could not write call initiation request to socket.\n");
                    }
                } else {
                    Log::info("Could not authenticate to Asterisk Manager Interface.\n");
                }
            } else {
                Log::info("Could not write authentication request to socket.\n");
            }
        } else {
            Log::info('Unable to connect to socket.');
        }
    }
}
