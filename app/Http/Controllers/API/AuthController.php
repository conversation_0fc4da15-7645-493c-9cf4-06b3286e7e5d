<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        // validation
        $validator = Validator::make($request->all(), [
            'email' => 'required|exists:users,email|max:255',
            'password' => 'required',
        ]);

        // if validation fails
        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = User::where('role', 'client')->where('email', $request->email)->first();

            if (!$user) {
                return jsonResponse(false, [
                    'message' => __('messages.account_not_exist', [], 'it')
                ]);
            }

            if ($user->is_active == 0) {
                return response()->json([
                    'status' => false,
                    'message' => __('messages.account_not_exist', [], $user->language ?? 'it'),
                ]);
            }

            $credentials = $request->only(['email', 'password']);

            $login = auth()->attempt($credentials);
            if ($login) {

                if ($user->role == 'dealer') {
                    $user->dealer;
                } elseif ($user->role == 'client') {
                    $user->client;
                }

                return jsonResponse(true, [
                    'message' => 'Logged in successfully!',
                    'token' => $user->createToken($user->name)->plainTextToken,
                    'user' => $user,
                ]);
            }

            return jsonResponse(false, [
                'message' => __('messages.incorrect_password', [], $user->language ?? 'it'),
            ]);
        } catch (\Exception $e) {
            // Handle the authentication exception

            return jsonResponse(false, [
                'message' => __('messages.auth_failed', [], $user?->language ?? 'it'),
            ]);
        }
    }

    public function getProfile()
    {
        $user = auth()->user();
        if ($user->role == 'dealer') {
            $user->dealer;
        } elseif ($user->role == 'client') {
            $user->client;
        }

        return jsonResponse(true, [
            'user' => $user,
        ]);
    }

    public function logout()
    {
        $language = auth()->user()->language ?? 'it';
        auth()->user()->tokens()->delete();
        return jsonResponse(true, [
            'message' => __('messages.logout_success', [], $language ?? 'it'),
        ]);
    }

    public function updateFcmToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'fcm_token' => 'required',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $user = auth()->user();
        $user->fcm_token = $request->fcm_token;
        $user->save();

        return jsonResponse(true, [
            'message' => 'FCM token updated successfully!',
        ]);
    }
    public function updateLanguage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'language' => 'required|in:en,it',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $user = auth()->user();
        $user->language = $request->language;
        $user->save();

        return jsonResponse(true, [
            'message' => __('messages.language_updated', [], $user->language ?? 'it'),
        ]);
    }

    public function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'username' => 'required|unique:users,username,' . auth()->id(),
            'phone_number' => 'nullable|max:255',
            'address' => 'nullable|max:255',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $user = auth()->user();
        $user->name = $request->name;
        $user->username = $request->username;
        $user->save();

        $user->client?->update([
            'phone_number' => $request->phone_number,
            'address' => $request->address,
        ]);

        return jsonResponse(true, [
            'message' => __('messages.profile_updated', [], $user->language ?? 'it'),
        ]);
    }

    public function updateAlerts(Request $request)
    {
        // Validation: Only allow boolean values for the alert columns
        $validator = Validator::make($request->all(), [
            'tampering_alert' => 'nullable',
            'speed_limit_exceeded' => 'nullable',
            'power_off' => 'nullable',
            'geofence_exit' => 'nullable',
            'signal_loss' => 'nullable',
            'low_battery' => 'nullable',
            'contract_expiry' => 'nullable',
            'other_alerts' => 'nullable',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        // Get the authenticated user
        $user = auth()->user();

        // Update only the passed columns
        $updateData = $request->only([
            'tampering_alert',
            'speed_limit_exceeded',
            'power_off',
            'geofence_exit',
            'signal_loss',
            'low_battery',
            'contract_expiry',
            'other_alerts',
        ]);

        // Only update the fields that are passed
        foreach ($updateData as $key => $value) {
            if ($value !== null) {
                $user->$key = $value;
            }
        }

        $user->save();

        return jsonResponse(true, [
            'message' => __('messages.alert_settings_updated', [], $user->language ?? 'it'),
        ]);
    }
}
