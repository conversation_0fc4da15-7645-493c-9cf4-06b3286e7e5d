<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\DeviceGeofence;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Validator;

class GeofenceController extends Controller
{
    // Save geofence
    public function save(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'imei' => 'required|string',
            'type' => 'required|string',
            'coordinates' => 'required|string',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $geofenceExists = DeviceGeofence::where('imei', $request->imei)->where('geofence_type', 0)->exists();

        if ($geofenceExists) {
            return response()->json(['status' => false, 'message' => __('messages.delete_exiting_geofence', [], 'it') . ' ' . $request->imei]);
        }

        $geofence = DeviceGeofence::updateOrCreate(
            ['id' => $request->id], // Update if ID is provided
            $request->all()
        );

        // Save all geofences for the specified IMEI in a JSON file
        $this->saveGeofencesToFile($request->imei);

        return response()->json([
            'status' => true,
            'message' => __('messages.geofence_saved_success', [], 'it'),
            'geofence' => $geofence
        ], 200);
    }
    public function saveCircle(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'imei' => 'required|string',
            'coordinates' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $geofenceExists = DeviceGeofence::where('imei', $request->imei)->where('geofence_type', 1)->first();
        if ($geofenceExists) {
            $geofenceExists->delete();

            $this->saveGeofencesToFile($request->imei);

            // Path to the geofence JSON file
            $jsonFilePath = public_path('device_geofence/device_geofence.json');

            // Read existing JSON file if it exists, otherwise initialize an empty array
            $geofenceData = File::exists($jsonFilePath) ? json_decode(File::get($jsonFilePath), true) : [];

            // Update or add the IMEI state to "in"
            $geofenceData[$request->imei] = ['state' => 'out'];

            // Save the updated JSON data back to the file
            File::put($jsonFilePath, json_encode($geofenceData, JSON_PRETTY_PRINT));

            return response()->json(['status' => true, 'message' => __('messages.geofence_deleted', [], 'it')], 200);
        } else {
            $geofence = DeviceGeofence::create(
                [
                    'imei' => $request->imei,
                    'type' => 'circle',
                    'geofence_type' => 1,
                    'coordinates' => $request->coordinates
                ]
            );

            // Save all geofences for the specified IMEI in a JSON file
            $this->saveGeofencesToFile($request->imei);

            // Path to the geofence JSON file
            $jsonFilePath = public_path('device_geofence/device_geofence.json');

            // Read existing JSON file if it exists, otherwise initialize an empty array
            $geofenceData = File::exists($jsonFilePath) ? json_decode(File::get($jsonFilePath), true) : [];

            // Update or add the IMEI state to "in"
            $geofenceData[$request->imei] = ['state' => 'in'];

            // Save the updated JSON data back to the file
            File::put($jsonFilePath, json_encode($geofenceData, JSON_PRETTY_PRINT));


            return response()->json(
                [
                    'status' => true,
                    'message' => __('messages.geofence_saved_success', [], 'it'),
                    'id' => $geofence->id,
                    'coordinates' => $request->coordinates
                ],
                200
            );
        }
    }

    // Helper method to save all geofences for an IMEI to a JSON file
    protected function saveGeofencesToFile($imei)
    {
        $geofences = DeviceGeofence::where('imei', $imei)->get();
        $path = public_path("geofences/{$imei}.json");

        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0777, true); // Create the folder if it doesn't exist
        }

        file_put_contents($path, $geofences->toJson(JSON_PRETTY_PRINT));
    }
    // Fetch geofences by IMEI
    public function fetch(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'imei' => 'required|string',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $geofences = DeviceGeofence::where('imei', $request->imei)->where('geofence_type', 0)->get();

        return response()->json(
            [
                'status' => true,
                'geofences' => $geofences
            ],
            200
        );
    }

    public function fetchAutomaticGeofence(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'imei' => 'required|string',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $geofences = DeviceGeofence::where('imei', $request->imei)->where('geofence_type', 1)->get();

        return response()->json([
            'status' => true,
            'geofences' => $geofences
        ], 200);
    }

    public function delete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'nullable',
            'imei' => 'required|string',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        // Find and delete the geofence
        $geofence = DeviceGeofence::where('imei', $request->imei)->where('geofence_type', 0)
            ->first();

        if (!$geofence) {
            return response()->json(['status' => false, 'message' => __('messages.geofence_not_found', [], 'it')]);
        }

        $geofence->delete();

        // Update the JSON file for the specified IMEI
        $this->saveGeofencesToFile($request->imei);


        return response()->json(['status' => true, 'message' => __('messages.geofence_deleted', [], 'it')], 200);
    }
}
