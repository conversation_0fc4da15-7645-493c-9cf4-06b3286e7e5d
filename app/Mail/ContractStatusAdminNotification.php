<?php

namespace App\Mail;

use App\Models\Contract;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContractStatusAdminNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $contract;
    public $client;
    public $device;
    public $status;

    /**
     * Create a new message instance.
     */
    public function __construct(Contract $contract, $status)
    {
        $this->contract = $contract;
        $this->client = $contract->client;
        $this->device = $contract->device;
        $this->status = $status;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "Contratto {$this->contract->contract_number} - " . __('messages.' . strtolower($this->status), [], 'it'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'mails.contract_status_notification',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $pdf = Pdf::loadView('pdf.contract', ['contract' => $this->contract])
            ->setOption('defaultFont', 'Roboto');

        return [
            Attachment::fromData(fn() => $pdf->output(), "Contratto_{$this->contract->contract_number}.pdf")
                ->withMime('application/pdf'),
        ];
    }
}
