<?php

namespace App\Services;

use App\Models\User;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;
use Kreait\Laravel\Firebase\Facades\Firebase;

class FCMService
{
    public static function sendNotification($token, $title, $body, $route = null)
    {
        try {
            $messaging = Firebase::messaging();

            // Create notification payload
            $notification = Notification::create($title, $body);

            // Define message structure
            $message = CloudMessage::withTarget('token', $token)
                ->withNotification($notification)
                ->withApnsConfig([
                    'payload' => [
                        'aps' => [
                            'sound' => 'default', // Enables sound in iOS
                            'alert' => [
                                'title' => $title,
                                'body' => $body
                            ]
                        ]
                    ]
                ]);

            // Include route data if available
            if ($route) {
                $message = $message->withData([
                    'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                    'route' => $route,
                ]);
            }

            \Log::info('notification sent');
            // Send the message
            $response = $messaging->send($message);
            \Log::info('notification sent: ' . json_encode($response));
        } catch (Kreait\Firebase\Exception\Messaging\NotFound $e) {
            \Log::error('Invalid FCM token: ' . $token);
            User::where('fcm_token', $token)->update(['fcm_token' => null]);
        } catch (\Exception $e) {
            \Log::error('FCM error: ' . $e->getMessage());
        }
    }
}
