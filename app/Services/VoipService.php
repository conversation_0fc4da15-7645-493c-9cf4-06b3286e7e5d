<?php

namespace App\Services;

use Exception;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\OriginateAction;

class VoipService
{
    private $client;
    private $config;

    public function __construct()
    {
        $this->config = [
            'host' => env('ASTERISK_HOST', '127.0.0.1'),
            'port' => env('ASTERISK_PORT', 5038),
            'username' => env('ASTERISK_USERNAME', 'admin'),
            'password' => env('ASTERISK_PASSWORD', '_-m4Q1vFOs'),
            'secret' => env('ASTERISK_PASSWORD', '_-m4Q1vFOs'),
            'connect_timeout' => 10,
            'read_timeout' => 10
        ];
    }

    private function connect()
    {
        try {
            $this->client = new ClientImpl($this->config);
            $this->client->open();
        } catch (Exception $e) {
            \Log::error('Failed to connect to Asterisk: ' . $e->getMessage());
            throw $e;
        }
    }

    public function makeCall($number)
    {
        try {
            $this->connect();

            $originateAction = new OriginateAction(
                'SIP/convergenze/**********', // Channel (source number)
                'from-internal',                    // Context
                $number,                       // Extension (destination number)
                1,                             // Priority
                null,                          // Application
                null,                          // Data
                30000,                         // Timeout (ms)
                env('CALLER_ID', '**********'), // Caller ID
                null,                          // Variables
                null,                          // Account
                true,                          // Async
                null                           // Action ID
            );

            $response = $this->client->send($originateAction);
            $this->client->close();

            if ($response->isSuccess()) {
                return ['status' => 'success', 'message' => 'Call initiated successfully'];
            } else {
                return ['status' => 'error', 'message' => $response->getMessage()];
            }
        } catch (Exception $e) {
            if ($this->client) {
                $this->client->close();
            }
            return ['status' => 'error', 'message' => $e->getMessage(), 'code' => $e->getCode(), 'trace' => $e->getTraceAsString(), 'file' => $e->getFile(), 'line' => $e->getLine()];
        }
    }
}
