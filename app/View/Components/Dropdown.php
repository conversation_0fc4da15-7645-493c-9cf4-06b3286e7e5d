<?php

namespace App\View\Components;

use Illuminate\View\Component;

class Dropdown extends Component
{
    public $options;
    public $name;
    public $placeholder;
    public $emptyOptionsMessage;

    public function __construct($name, $options = [], $placeholder = 'Select an option', $emptyOptionsMessage = 'No options match your search.')
    {
        $this->options = $options;
        $this->name = $name;
        $this->placeholder = $placeholder;
        $this->emptyOptionsMessage = $emptyOptionsMessage;
    }

    public function render()
    {
        return view('components.dropdown');
    }
}
