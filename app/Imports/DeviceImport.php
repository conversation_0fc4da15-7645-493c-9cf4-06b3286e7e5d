<?php

namespace App\Imports;

use App\Models\Device;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class DeviceImport implements ToModel, WithHeadingRow
{
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        return new Device([
            'imei' => $row['imei'],
            'model' => $row['model'],
            'iccid' => $row['iccid'],
            'imsi' => $row['imsi'],
            'is_active' => $this->convertActivityStatus($row['activity_status']),
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at'],
        ]);
    }

    private function convertActivityStatus($status)
    {
        switch (strtolower($status)) {
            case 'online':
                return 1;
            case 'offline':
                return 0;
            case 'inactive':
            default:
                return 0;
        }
    }
}
