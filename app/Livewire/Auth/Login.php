<?php

namespace App\Livewire\Auth;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;

class Login extends Component
{
    #[Title('Login - Memove')]
    #[Layout('layout.auth')]

    #[Validate('required|max:255')]
    public $username_or_email;

    #[Validate('required|max:100')]
    public $password;
    public $remember_me = true;

    public function mount()
    {
        if (Auth::check() && Auth::user()) {
            return redirect()->route('dashboard');
        }
    }

    public function login()
    {
        // Validate input
        $this->validate();

        // Check if the user exists
        $user = User::where('email', $this->username_or_email)
            ->orWhere('username', $this->username_or_email)
            ->first();

        // Handle user not found
        if (!$user) {
            $this->addError('username_or_email', __('messages.incorrect_username_or_email'));
            $this->dispatch('notice', type: 'error', text: __('messages.user_not_found'));
            return;
        }

        // Check if the account is active
        if ($user->is_active == 0) {
            $this->dispatch('notice', type: 'error', text: __('messages.account_deactivated'));
            return;
        }

        // Attempt to log in the user
        if (Auth::attempt([
            'email' => $user->email,
            'password' => $this->password
        ], $this->remember_me)) {
            if (auth()->check() && auth()->user()->role == 'dealer') {
                return redirect()->route('devices');
            }
            // Redirect to intended or default route on successful login
            return redirect()->intended(route('dashboard'));
        } else {
            $this->addError('password', __('messages.incorrect_password'));
            $this->dispatch('notice', type: 'error', text: __('messages.incorrect_password'));
        }
    }


    public function render()
    {
        return view('livewire.auth.login');
    }
}
