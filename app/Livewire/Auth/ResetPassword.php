<?php

namespace App\Livewire\Auth;

use App\Models\PasswordResetToken;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Component;

class ResetPassword extends Component
{
    #[Layout('layout.auth')]
    #[Title('Password Reset - Memove')]

    public $email;
    public $token;
    public $password;
    public $password_confirmation;

    public function mount($token, $email)
    {
        $this->token = $token;
        $this->email = $email;
    }

    public function resetPassword()
    {
        // Validate the new passwords
        $this->validate([
            'password' => 'required|min:6|confirmed',
        ]);

        // Check the token
        $tokenData = PasswordResetToken::where('email', $this->email)->where('token', $this->token)->first();

        if (!$tokenData || Carbon::parse($tokenData->created_at)->addMinutes(60)->isPast()) {
            $this->dispatch('notice', type: 'success', text: __('messages.expired_token'));
            return;
        }

        // Update the user's password
        $user = User::where('email', $this->email)->firstOrFail();
        $user->password = Hash::make($this->password);
        $user->save();

        // Delete the token after successful reset
        $tokenData->delete();

        $this->dispatch('notice', type: 'success', text: __('messages.password_reset_success'));

        return redirect()->route('login'); // Redirect to login page
    }
    public function render()
    {
        return view('livewire.auth.reset-password');
    }
}
