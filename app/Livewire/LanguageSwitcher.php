<?php

namespace App\Livewire;

use Illuminate\Support\Facades\App;
use Livewire\Component;

class LanguageSwitcher extends Component
{
    public $languages = [
        'en' => ['name' => '🇺🇸 English'],
        'it' => ['name' => '🇮🇹 Italian'],
    ];

    public $currentUrl;
    public $currentLanguage;
    public $color;
    public $position;

    public function mount($color = 'white', $position = 'bottom')
    {
        $this->color = $color;
        $this->position = $position;

        $this->currentUrl = url()->current();

        // Update the user's language preference
        if (auth()->check()) {
            $this->currentLanguage = auth()->user()->language;
        } else {
            $this->currentLanguage =  session('language', 'it');
        }
    }

    public function changeLanguage($shortCode)
    {
        // Update the user's language preference
        if (auth()->check()) {
            $user = auth()->user();
            $user->language = $shortCode; // Make sure you have a 'language' column in the users table
            $user->save();
        } else {
            session()->put('language', $shortCode);
        }
        // Set the application locale
        App::setLocale($shortCode);

        $this->currentLanguage = $shortCode;

        // Emit an event to notify other components if needed
        $this->redirect($this->currentUrl ?? route('dashboard'));
    }

    public function render()
    {
        return view('livewire.language-switcher');
    }
}
