<?php

namespace App\Http\Livewire;

use App\Models\Device;
use Illuminate\Support\Facades\Auth;

use Livewire\Component;

class DeviceSearch extends Component
{
    public $search = '';
    public $devices = [];

    public function updatedSearch()
    {
        if (strlen($this->search) > 0) {
            $query = Device::query();

            // Filter devices based on user role
            if (Auth::user()->role == 'dealer') {
                $query->where('dealer_id', Auth::user()->dealer->id ?? 0);
            } elseif (Auth::user()->role == 'client') {
                $query->where('client_id', Auth::user()->client->id ?? 0);
            }

            // Search by IMEI or Number Plate
            $this->devices = $query
                ->where(function ($q) {
                    $q->where('imei', 'LIKE', '%' . $this->search . '%')
                        ->orWhere('number_plate', 'LIKE', '%' . $this->search . '%');
                })
                ->select('imei', 'number_plate')
                ->limit(10) // Limit results for optimization
                ->get();
        } else {
            $this->devices = [];
        }
    }

    public function render()
    {
        return view('livewire.device-search');
    }
}
