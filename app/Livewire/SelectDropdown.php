<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Attributes\On;
use Livewire\Component;

class SelectDropdown extends Component
{
    public $label;
    public $placeholder;
    public $fieldName;
    public $options = [];
    public $filteredOptions = [];
    public $selectedOption;
    public $search = ''; // For search functionality
    public $open = false;
    public $fetchMethod;

    // Dynamically call the provided fetch method
    public function fetchOptions()
    {
        if ($this->fetchMethod && method_exists($this, $this->fetchMethod)) {
            $this->options = call_user_func([$this, $this->fetchMethod]);
            $this->filteredOptions = $this->options; // Initialize filtered options
            $this->open = true;
        } else {
            throw new \Exception("Fetch method '{$this->fetchMethod}' not defined or does not exist.");
        }
    }

    #[On('valueUpdated')]
    public function valueUpdated($option = null, $type = null)
    {
        if ($type == $this->fetchMethod) {
            $this->selectedOption = $option;
            $this->fetchOptions();
            $this->open = false;
        }
    }

    // Filter options based on the search input
    public function updatedSearch()
    {
        if (is_array($this->options)) {
            $this->filteredOptions = array_filter($this->options, function ($option) {
                return str_contains(strtolower($option), strtolower($this->search));
            });
        } else {
            $this->filteredOptions = $this->options->filter(function ($option) {
                return str_contains(strtolower($option), strtolower($this->search));
            })->all();
        }
    }

    // Toggle the selected state of an option
    public function toggleOption($option)
    {
        if ($option == $this->selectedOption) {
            $this->selectedOption = null;
        } else {
            $this->selectedOption = $option;
            $this->open = false;
        }

        // Dispatch event to reflect changes
        $this->dispatch($this->fieldName . 'Updated', $this->selectedOption);
    }

    public function getClients()
    {
        // let's also contact the last name from user's client relation
        return User::where('role', 'client')->with('client:id,user_id,last_name')->latest()->get(['id', 'name'])->mapWithKeys(function ($user) {
            return [$user->client?->id => $user->name . ' ' . $user->client?->last_name];
        });
    }

    public function getDealers()
    {
        return User::where('role', 'dealer')->latest()->pluck('name', 'id');
    }

    public function render()
    {
        return view('livewire.select-dropdown');
    }
}
