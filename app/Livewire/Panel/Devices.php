<?php

namespace App\Livewire\Panel;

use App\Exports\DevicesExport;
use App\Imports\DeviceImport;
use App\Models\Client;
use App\Models\Command;
use App\Models\Contract;
use App\Models\Ddt;
use App\Models\Dealer;
use App\Models\Device;
use App\Models\DeviceTestResult;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;

class Devices extends Component
{
    use WithPagination, WithFileUploads;



    public $imei, $model, $iccid, $imsi, $is_active = true, $dealer_id, $client_id, $is_tested = 0, $is_verified = 0, $in_maintenance = false;

    public $recordId, $deleteRecordId, $search, $status, $tested, $verified, $dealer, $client;
    public $vehicle_type = 'car';

    public  $dealers, $clients;

    public $selectedDevices = [];
    public $selectAllDevices = false;
    public $selected_dealer;
    public $selected_records;

    public $return_devices = [];
    public $return_reason;



    public $deviceTestData;
    public $testResult = [];
    public $deviceTestResults = [];
    public $deviceVerifyData;

    public $verification_client, $verification_vehicle_type = 'car', $service_details, $vehicle_brand, $vehicle_model, $vehicle_color, $vehicle_registration_number, $vehicle_chassis, $vehicle_km, $starter_motor_block = "No", $vehicle_number_plate, $vehicle_registration_date, $duration, $start_date, $end_date, $phone_number, $frame, $installation_image;

    public $username, $name, $type = 'private', $last_name, $email, $password, $address, $company, $municipality, $zip_code, $province, $tax_code, $is_client_active = true;

    public $csvFile;

    public $incharge_of_transport, $transport_reason, $delivery_terms, $parcels, $weight;
    public $transportOfficerSignature;
    public $recipientSignature;

    public $commandTestedId;

    public $ignitionCommandResponse = false;



    public function mount()
    {
        $this->dealers = User::where('role', 'dealer')
            ->where('is_active', 1)
            ->with('dealer:id,user_id,last_name')
            ->latest()
            ->get()
            ->mapWithKeys(function ($user) {
                return [$user->dealer->id ?? null => $user->name . ' ' . $user->dealer?->last_name];
            })
            ->filter() // This will remove any null keys if a user does not have a dealer.
            ->all();

        $this->clients = User::where('role', 'client')
            ->when(auth()->user()->role == 'dealer', function ($query) {
                $query->whereHas('client', function ($q) {
                    $q->where('dealer_id', auth()->user()->dealer?->id ?? null);
                });
            })
            ->with('client:id,user_id,last_name')
            ->latest()
            ->get()
            ->mapWithKeys(function ($user) {
                return [$user->client->id ?? null => $user->name . ' ' . $user->client?->last_name];
            })
            ->filter() // This will remove any null keys if a user does not have a client.
            ->all();
    }

    public function importDevices()
    {
        $this->validate([
            'csvFile' => 'required|mimes:csv,txt|max:10000',
        ]);

        try {
            Excel::import(new DeviceImport, $this->csvFile->getRealPath());
            $this->dispatch('notice', type: 'error', text: __('messages.devices_imported'));
            $this->dispatch('close-modal'); // Reset the file input after import
            $this->reset('csvFile'); // Reset the file input after import

        } catch (\Exception $e) {
            $this->dispatch('notice', type: 'error', text: __('messages.devices_import_error') . ' ' . $e->getMessage());
        }
    }


    public function editRecord($recordId)
    {
        $this->recordId = $recordId;

        if ($recordId) {
            $this->recordId = $recordId;
            $device = Device::find($this->recordId);

            if ($device) {
                // Set existing device data when editing
                $this->imei = $device->imei;
                $this->model = $device->model;
                $this->iccid = $device->iccid;
                $this->imsi = $device->imsi;
                $this->vehicle_type = $device->vehicle_type;
                $this->dealer_id = $device->dealer_id;
                $this->client_id = $device->client_id;
                $this->is_active = $device->is_active ? true : false;
                $this->is_tested = $device->is_tested ? 1 : 0;
                $this->is_verified = $device->is_verified ? 1 : 0;
                $this->in_maintenance = $device->in_maintenance ? true : false;

                $this->dispatch('open-modal', name: 'add-device-modal');
            }
        }
    }

    // Add or Update Device
    public function addUpdateDevice()
    {
        $this->validate([
            'imei' => 'required|max:255|unique:devices,imei,' . $this->recordId,
            'model' => 'nullable|max:255',
            'iccid' => 'nullable|max:255',
            'imsi' => 'nullable|max:255',
            'vehicle_type' => 'nullable|max:255',
            'dealer_id' => 'nullable',
            'client_id' => 'nullable',
            'is_active' => 'nullable',
            'is_tested' => 'nullable',
            'is_verified' => 'nullable',
            'in_maintenance' => 'nullable',
        ]);


        if ($this->recordId) {


            // Update existing device
            $device = Device::find($this->recordId);
            $oldDealer = $device->dealer_id ?? null;

            if ($this->dealer_id && $this->is_tested == false && $this->dealer_id != $oldDealer) {
                $this->dispatch('notice', type: 'error', text: __('messages.assign_not_tested_device_error'));
                return;
            }
            if ($device) {
                $device->model = $this->model;
                $device->imei = $this->imei;
                $device->iccid = $this->iccid;
                $device->imsi = $this->imsi;
                $device->vehicle_type = $this->vehicle_type;
                $device->dealer_id = $this->dealer_id;
                $device->client_id = $this->client_id;
                $device->is_active = $this->is_active ? true : false;
                $device->is_tested = $this->is_tested ? true : false;
                $device->is_verified = $this->is_verified ? true : false;
                $device->in_maintenance = $this->in_maintenance ? true : false;

                $device->save();

                if (!$oldDealer && $this->dealer_id && $this->is_tested == true) {
                    $this->sendDdtToDealer([0 => $device]);
                }
            }
        } else {
            $device = Device::create([
                'model' => $this->model,
                'imei' => $this->imei,
                'iccid' => $this->iccid,
                'imsi' => $this->imsi,
                'vehicle_type' => $this->vehicle_type,
                'dealer_id' => $this->dealer_id,
                'client_id' => $this->client_id,
                'is_active' => $this->is_active ? true : false,
                'is_tested' => $this->is_tested ? true : false,
                'is_verified' => $this->is_verified ? true : false,
                'in_maintenance' => $this->in_maintenance ? true : false,
            ]);
        }

        $this->dispatch('notice', type: 'error', text: $this->recordId ? __('messages.device_updated') : __('messages.device_created'));

        $this->dispatch('close-modal');

        $this->reset();
    }

    protected function sendDdtToDealer($device)
    {
        // Generate DDT Number
        $ddtNumber = $this->generateDdtNumber();

        // Fetch Dealer Email
        $dealer = Dealer::find($device->dealer_id);
        if ($dealer && $dealer->user->email) {
            // Generate PDF
            $pdf = Pdf::loadView('pdf.ddt', [
                'devices' => $device,
                'ddtNumber' => $ddtNumber,
                'dealer' => $dealer,
            ]);

            // Send Email with PDF Attachment
            Mail::send([], [], function ($message) use ($dealer, $pdf, $ddtNumber) {
                $message->to($dealer->user->email)
                    ->subject("DDT Document #$ddtNumber")
                    ->attachData($pdf->output(), "DDT_$ddtNumber.pdf");
            });
            // $this->dispatch('notice', type: 'success', text: 'DDT sent to dealer successfully.');
        }
    }

    protected function generateDdtNumber()
    {
        return 'W-' . strtoupper(Str::random(10));
    }

    public function addUpdateClient()
    {
        $this->validate([
            'name' => ['required', 'max:255'],
            'username' => ['required', 'string', 'max:255', Rule::unique('users')->ignore($this->recordId ? Client::find($this->recordId)->user->id : null)],
            'email' => ['required', 'email', 'max:255', Rule::unique('users')->ignore($this->recordId ? Client::find($this->recordId)->user->id : null)],
            'password' => $this->recordId ? ['nullable', 'min:6'] : ['required', 'min:6'],
            'address' => ['required', 'string', 'max:255'],
            'company' => ['nullable', 'string', 'max:255'],
            'municipality' => ['nullable', 'string', 'max:255'],
            'zip_code' => ['nullable', 'string', 'max:15'],
            'province' => ['nullable', 'string', 'max:255'],
            'tax_code' => ['nullable', 'string', 'max:100'],
            'type' => ['required', 'string', 'max:100'],
            'phone_number' => ['required', 'string', 'max:100'],

        ]);


        $passkey = $this->generateUniquePasskey();


        // Create new client
        $user = User::create([
            'name' => $this->name,
            'username' => $this->username,
            'email' => $this->email,
            'password' => Hash::make($this->password), // Password hashing for new user
            'role' => 'client',
            'is_active' => false
        ]);

        $client = new Client([
            'company' => $this->company,
            'address' => $this->address,
            'municipality' => $this->municipality,
            'zip_code' => $this->zip_code,
            'province' => $this->province,
            'tax_code' => $this->tax_code,
            'type' => $this->type,
            'last_name' => $this->last_name,
            'phone_number' => $this->phone_number,
            'dealer_id' => auth()->user()->dealer->id,
            'passkey' => $passkey,

        ]);
        $client->user()->associate($user); // Associate user with client
        $client->save();

        $this->dispatch('notice', type: 'error', text: __('messages.client_created'));

        $this->dispatch('close-modal');
        $this->dispatch('open-modal', name: 'verify-device-modal');

        $this->resetExcept('deviceVerifyData');

        $this->clients = User::where('role', 'client')
            ->when(auth()->user()->role == 'dealer', function ($query) {
                $query->whereHas('client', function ($q) {
                    $q->whereNotNull('dealer_id')->where('dealer_id', auth()->user()->dealer->id ?? null);
                });
            })
            ->with('client:id,user_id,last_name')
            ->latest()
            ->get()
            ->mapWithKeys(function ($user) {
                return [$user->client->id ?? null => $user->name . ' ' . $user->client?->last_name];
            })
            ->filter() // This will remove any null keys if a user does not have a client.
            ->all();
        $this->dispatch('dropdown-updated', data: $this->clients);
    }

    public function generateUniquePasskey()
    {
        do {
            // Generate a random 16-character passkey
            $passkey = Str::random(10);
        } while (\App\Models\Client::where('passkey', $passkey)->exists());

        return $passkey;
    }

    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            $device = Device::find($this->deleteRecordId);

            $device->delete();


            $this->dispatch('notice', type: 'error', text: __('messages.device_deleted'));
            $this->dispatch('close-modal');
        } else {
            $this->dispatch('notice', type: 'error', text: __('messages.device_not_found'));
        }
    }

    public function clearRecords()
    {
        // $this->resetExcept('deviceTestData', 'testResult', 'deviceTestResults', 'deviceVerifyData', 'search', 'status', 'tested', 'verified', 'dealer', 'client');
    }

    public function openDeviceTestModal($id)
    {
        $this->deviceTestData = Device::with('dealer:id,user_id', 'dealer.user:id,name', 'client:id,user_id', 'client.user:id,name')->find($id);

        $this->dispatch('close-modal');
        $this->testDevice();
    }

    public function openDeviceVerifyModal($id)
    {
        $this->deviceVerifyData = Device::find($id);

        $this->dispatch('open-modal', name: 'verify-device-modal');
    }


    public function testDevice($notify = null)
    {
        // Get the device IMEI from the loaded device data
        $imei = $this->deviceTestData->imei;

        // Load the JSON data
        $filePath = public_path('data/live/live_data.json');
        if (!file_exists($filePath)) {
            $this->dispatch('notice', type: 'error', text: __('messages.live_data_not_found'));
            return;
        }

        // Decode JSON file content
        $jsonData = json_decode(file_get_contents($filePath), true);

        // Check if the IMEI exists in the JSON data
        if (isset($jsonData[$imei])) {
            // Assign the data to testResult if the IMEI exists
            $this->testResult = $jsonData[$imei];

            // Dispatch modal events
            $this->dispatch('close-modal');
            $this->dispatch('open-modal', name: 'test-result-modal');
            if ($notify) {
                $this->dispatch('notice', type: 'error', text: __('messages.test_retried'));
            }
        } else {
            // Dispatch error notification if IMEI data is not found
            $this->dispatch('notice', type: 'error', text: __('messages.no_live_data_from_device'));
        }
    }

    public function getIgnitionStatus($ignitionStatus)
    {
        if ($ignitionStatus == 1) {
            return 'On';
        } else {
            return 'Off';
        }
    }
    public function getEngineStatus($ignitionStatus, $movement = 0, $speed = 0)
    {
        if ($ignitionStatus == 1 && $speed > 0 && $movement == 1) {

            return "On";
        } else {
            return "Off";
        }
    }


    public function finishTest()
    {
        if ($this->testResult && $this->deviceTestData) {
            Device::find($this->deviceTestData->id)->update(['is_tested' => 1, 'is_active' => 1]);

            $ignitionStatus = $this->getIgnitionStatus($this->testResult['239']);
            $engineStatus = $this->getEngineStatus($this->testResult['239'], $this->testResult['240'], $this->testResult['speed']);
            $batteryVoltage = isset($this->testResult['66']) ? round($this->testResult['66'], 2) : null;
            $batteryLevel = isset($this->testResult['113']) ? $this->testResult['113'] : null;

            $ignition_tested_at = null;

            if ($this->ignitionCommandResponse) {
                $ignition_tested_at = now();
            }


            DeviceTestResult::create([
                'device_id' => $this->deviceTestData->id,
                'gps_status' => $this->testResult['69'] ?? 'N/A',
                'signal' => $this->testResult['21'] ?? 0,
                'movement_status' => $this->testResult['240'] ?? 'N/A',
                'ignition_status' => $ignitionStatus ?? 'N/A',
                'engine_status' => $engineStatus ?? 'N/A',
                'battery_voltage' => $batteryVoltage ?? 'N/A',
                'battery_level' => $batteryLevel ?? 'N/A',
                'ignition_tested_at' => $ignition_tested_at,
                'location' => $this->testResult['address'] ?? 'N/A',
                'latitude' => $this->testResult['latitude'],
                'longitude' => $this->testResult['longitude'],
                'speed' => $this->testResult['speed'],
                'last_update' => $this->testResult['last_update'],
            ]);
            $this->dispatch('notice', type: 'error', text: __('messages.device_tested'));
            $this->dispatch('close-modal');

            if (auth()->user()->role == 'dealer') {
                $this->openDeviceVerifyModal($this->deviceTestData->id);
            }
        } else {
            $this->dispatch('notice', type: 'error', text: __('messages.no_test_result'));
        }
    }


    public function showDeviceTestResults($id)
    {
        $device = Device::with('testResults')->find($id);
        if ($device) {
            $this->deviceTestResults = $device;
            $this->dispatch('open-modal', name: 'device-test-results-modal');
        } else {
            $this->dispatch('notice', type: 'error', text: __('messages.device_not_found'));
        }
    }

    // public function showDeviceTestResultsForVerification($id)
    // {
    //     $device = Device::with('testResults')->find($id);
    //     if ($device) {

    //         $this->deviceTestResults = $device;
    //         $this->dispatch('open-modal', name: 'device-test-results-for-verification-modal');
    //     } else {
    //         $this->dispatch('notice', type: 'error', text: __('messages.device_not_found'));
    //     }
    // }


    public function verifyDevice()
    {
        if ($this->deviceVerifyData) {
            $this->validate([
                'deviceVerifyData.id' => 'required|exists:devices,id',
                'verification_client' => 'required|exists:clients,id',
                'verification_vehicle_type' => 'required|max:255',
                'vehicle_number_plate' => 'required|max:255',
                'duration' => 'required|max:255',
                'start_date' => 'required|max:255',
                'end_date' => 'required|max:255',
                'vehicle_brand' => 'required|max:255',
                'vehicle_model' => 'required|max:255',
                'vehicle_color' => 'required|max:255',
                'vehicle_registration_date' => 'required|max:255',
                'vehicle_km' => 'required|max:255',
                'starter_motor_block' => 'required|max:255',
            ]);

            // Generate unique contract number
            $contractNumber = $this->generateUniqueContractNumber();

            $uploadedFile = null;
            if ($this->installation_image) {
                $uploadedFile = $this->installation_image->store('images', 'public');
            }
            // Insert data into Contract
            $deviceContractData = Contract::create([
                'device_id' => $this->deviceVerifyData->id,
                'client_id' => $this->verification_client,
                'dealer_id' => auth()->user()->dealer->id ?? null,
                'duration' => $this->duration,
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
                'verification_vehicle_type' => $this->verification_vehicle_type,
                'vehicle_brand' => $this->vehicle_brand,
                'vehicle_model' => $this->vehicle_model,
                'vehicle_color' => $this->vehicle_color,
                'vehicle_registration_date' => $this->vehicle_registration_date,
                'vehicle_number_plate' => $this->vehicle_number_plate,
                'frame' => $this->frame,
                'vehicle_km' => $this->vehicle_km,
                'starter_motor_block' => $this->starter_motor_block,
                'status' => 'pending',
                'contract_number' => $contractNumber,
                'service_details' => $this->service_details,
                'installation_image' => $uploadedFile,
            ]);



            // Device::find($this->deviceVerifyData->id)->update(['client_id' => $this->verification_client, 'is_verified' => 1]);

            if ($deviceContractData?->client?->phone_number) {
                try {
                    // Authenticate User
                    $auth = authenticateUser(env('ARUBA_SMS_USERNAME'), env('ARUBA_SMS_PASSWORD'));

                    if ($auth) {
                        // Send SMS
                        $recipients = [$deviceContractData?->client?->phone_number]; // List of recipients
                        $sender = 'AllTech'; // Optional sender name

                        $contractLink = route('contract-response', ['contract_number' => $deviceContractData->contract_number]);

                        $message = "MeMove: Hai un nuovo contratto. Accetta o rifiuta qui: " . $contractLink;


                        $scheduleTime = gmdate('YmdHis', strtotime("+5 minutes")); // Ensure correct format


                        $smsResponse = sendSMS($auth, $message, $recipients, $sender, $scheduleTime);

                        // Display Result
                        if ($smsResponse && isset($smsResponse["result"]) && $smsResponse["result"] === "OK") {
                        } else {
                            \Log::info('Create Contract: Failed to send SMS.');
                        }
                    } else {
                        \Log::info('Create Contract: Authentication failed. Please check your credentials.');
                    }
                } catch (\Exception $e) {
                    \Log::info('Create Contract: ' . $e->getMessage());
                }
            }

            // Generate and send contract PDF
            $this->sendContractEmail($deviceContractData);


            // Close modal and show success message
            $this->dispatch('close-modal');
            $this->dispatch('notice', type: 'success', text: __('messages.device_inspection_completed'));
        } else {
            $this->dispatch('notice', type: 'error', text: __('messages.device_not_found'));
        }
    }



    private function generateUniqueContractNumber()
    {
        do {
            $contractNumber = strtoupper(Str::random(12)); // Generate a random 12-character uppercase alphanumeric string
        } while (Contract::where('contract_number', $contractNumber)->exists());

        return $contractNumber;
    }


    private function sendContractEmail($contract)
    {
        // Fetch client email
        $client = Client::find($this->verification_client);
        $clientEmail = $client->user->email;

        // Generate PDF from Blade view
        $pdf = Pdf::loadView('pdf.contract', compact('contract'))->setOption('defaultFont', 'Roboto');

        // Prepare contract link
        $contractLink = route('contract-response', ['contract_number' => $contract->contract_number]);

        // Send email with PDF attachment
        Mail::send('mails.contract_notification', ['contractLink' => $contractLink], function ($message) use ($clientEmail, $pdf, $contract) {
            $message->to($clientEmail)
                ->subject("Nuovo contratto: {$contract->contract_number}")
                ->attachData($pdf->output(), "Contratto_{$contract->contract_number}.pdf");
        });
    }


    // Calculate end date when duration or start date is updated
    public function updatedDuration()
    {
        $this->calculateEndDate();
    }

    public function updatedStartDate()
    {
        $this->calculateEndDate();
    }

    // Calculate end date based on start date and duration
    public function calculateEndDate()
    {
        if ($this->start_date && $this->duration) {
            $startDate = Carbon::parse($this->start_date);
            $endDate = $startDate->copy()->addMonths((int)$this->duration);
            $this->end_date = $endDate->format('Y-m-d');
        } else {
            $this->end_date = null;
        }
    }



    // Method to export as Excel
    public function exportExcel()
    {
        return Excel::download(new DevicesExport($this->getFiltereddevices()), 'devices.xlsx');
    }

    // Method to export as PDF
    public function exportPDF()
    {
        $devices = $this->getFiltereddevices();

        try {
            $pdf = Pdf::loadView('pdf.devices', compact('devices'));

            return response()->streamDownload(function () use ($pdf) {
                echo $pdf->stream();
            }, 'devices.pdf');
        } catch (\Exception $e) {
            $this->dispatch('notice', type: 'error', text: $e->getMessage());
        }




        // return $pdf->download('devices.pdf');
    }
    public function updatedSelectAllDevices($value)
    {
        $this->selectedDevices = $value ? Device::pluck('id')->toArray() : [];
        $this->selected_records =  $this->selectedDevices ? count($this->selectedDevices) : 0;
    }

    public function assignDealerToSelected()
    {

        $this->validate([
            'selected_dealer' => 'required|exists:dealers,id',
            'transportOfficerSignature' => 'nullable|file|mimes:png,jpg,jpeg|max:5000',
            'recipientSignature' => 'nullable|file|mimes:png,jpg,jpeg|max:5000',
        ]);

        if ($this->transportOfficerSignature) {
            $transportOfficerSignaturePath = $this->transportOfficerSignature->storeAs(
                'media',
                'transport_officer_signature_' . time() . '.' . $this->transportOfficerSignature->getClientOriginalExtension(),
                'public'
            );
        } else {
            $transportOfficerSignaturePath = null;
        }

        if ($this->recipientSignature) {
            $recipientSignaturePath = $this->recipientSignature->storeAs(
                'media',
                'recipient_signature_' . time() . '.' . $this->recipientSignature->getClientOriginalExtension(),
                'public'
            );
        } else {
            $recipientSignaturePath = null;
        }

        $devices = [];
        foreach ($this->selectedDevices as $selectedDevice) {
            $device = Device::find($selectedDevice);
            if (!$device->is_tested) {
                $this->dispatch('notice', type: 'error', text: $device->imei . ' ' . __('messages.device_not_tested_error'));

                return;
            }
            if (!$device->is_active) {
                $this->dispatch('notice', type: 'error', text: $device->imei . ' ' . __('messages.device_not_working_error'));
                return;
            }
            $devices[] = $device;
        }

        Device::whereIn('id', $this->selectedDevices)
            ->update(['dealer_id' => $this->selected_dealer]);


        // Generate DDT Number
        $ddtNumber = $this->generateDdtNumber();

        // Fetch Dealer Information
        $dealer = Dealer::find($this->selected_dealer);
        if ($dealer) {
            // Generate PDF
            $pdf = Pdf::loadView('pdf.ddt', [
                'devices' => $devices,
                'ddtNumber' => $ddtNumber,
                'dealer' => $dealer,
                'incharge_of_transport' => $this->incharge_of_transport,
                'transport_reason' => $this->transport_reason,
                'delivery_terms' => $this->delivery_terms,
                'parcels' => $this->parcels,
                'weight' => $this->weight,
                'transportOfficerSignaturePath' => $transportOfficerSignaturePath ? public_path('storage/' . $transportOfficerSignaturePath) : null,
                'recipientSignaturePath' => $recipientSignaturePath ? public_path('storage/' . $recipientSignaturePath) : null,
            ]);

            // Save the DDT PDF in storage
            $ddtPdfPath = 'ddts/ddt_' . $ddtNumber . '.pdf';
            Storage::disk('public')->put($ddtPdfPath, $pdf->output());

            // Store the DDT path in the database
            Ddt::create([
                'ddt_number' => $ddtNumber,
                'ddt_path' => $ddtPdfPath,
                'dealer_id' => $this->selected_dealer,
                'type' => 'assigned',

            ]);

            if ($dealer->user->email) {
                // Send Email with PDF Attachment
                Mail::send([], [], function ($message) use ($dealer, $pdf, $ddtNumber) {
                    $message->to($dealer->user->email)
                        ->subject("DDT Document #$ddtNumber")
                        ->attachData($pdf->output(), "DDT_$ddtNumber.pdf");
                });
            }
        }

        // Optional: Refresh devices and clear selected devices
        $this->selectedDevices = [];
        $this->selectAllDevices = false;
        $this->dispatch('notice', type: 'error', text: __('messages.dealer_assigned'));
        $this->dispatch('close-modal'); // Optionally emit an event for UI feedback

        // Delete Temporary Files
        if ($transportOfficerSignaturePath) {
            unlink(public_path('storage/' . $transportOfficerSignaturePath));
        }

        if ($recipientSignaturePath) {
            unlink(public_path('storage/' . $recipientSignaturePath));
        }
    }
    // Computed property to fetch and group devices by dealer
    public function getReturnDevicesProperty()
    {
        // Fetch the selected devices and eager load the relationships
        $devices = Device::whereIn('id', $this->selectedDevices)
            ->with(['dealer', 'client']) // Eager load the relationships
            ->get();

        // Group the devices by dealer_id
        return $devices->groupBy('dealer_id');
    }

    public function openReturnDDTModal()
    {
        // Trigger the modal
        $this->dispatch('open-modal', name: 'return-dealer-devices');
    }

    public function submitReturnDeviceDDT()
    {
        $this->validate([
            'return_reason' => 'nullable|max:5000',
            'transportOfficerSignature' => 'nullable|file|mimes:png,jpg,jpeg|max:5000',
            'recipientSignature' => 'nullable|file|mimes:png,jpg,jpeg|max:5000',
        ]);

        if ($this->transportOfficerSignature) {
            $transportOfficerSignaturePath = $this->transportOfficerSignature->storeAs(
                'media',
                'transport_officer_signature_' . time() . '.' . $this->transportOfficerSignature->getClientOriginalExtension(),
                'public'
            );
        } else {
            $transportOfficerSignaturePath = null;
        }

        if ($this->recipientSignature) {
            $recipientSignaturePath = $this->recipientSignature->storeAs(
                'media',
                'recipient_signature_' . time() . '.' . $this->recipientSignature->getClientOriginalExtension(),
                'public'
            );
        } else {
            $recipientSignaturePath = null;
        }

        // Fetch and group devices by dealer
        $devices = Device::whereIn('id', $this->selectedDevices)
            ->with(['dealer', 'client']) // Eager load dealer and client relationships
            ->get()
            ->groupBy('dealer_id'); // Group by dealer_id

        if ($devices->isEmpty()) {
            $this->dispatch('notice', type: 'error', text: __('messages.no_device_selected'));

            return;
        }
        // Loop through each dealer's grouped devices
        foreach ($devices as $dealerId => $groupedDevices) {
            $dealer = Dealer::with('user:id,name')->find($dealerId);
            if (!$dealer) {
                $this->dispatch('notice', type: 'error', text: __('messages.dealer_not_found_for_device'));
                continue;
            }

            // Generate the return DDT Number
            $ddtNumber = $this->generateDdtNumber();

            // Generate PDF for the return DDT document
            $pdf = Pdf::loadView('pdf.ddt_return', [
                'devices' => $groupedDevices,
                'ddtNumber' => $ddtNumber,
                'dealer' => $dealer,
                'incharge_of_transport' => $this->incharge_of_transport,
                'transport_reason' => $this->transport_reason,
                'return_reason' => $this->return_reason,
                'transportOfficerSignaturePath' => $transportOfficerSignaturePath ? public_path('storage/' . $transportOfficerSignaturePath) : null,
                'recipientSignaturePath' => $recipientSignaturePath ? public_path('storage/' . $recipientSignaturePath) : null,
            ]);

            // Save the return DDT PDF in storage
            $ddtPdfPath = 'return_ddts/return_ddt_' . $ddtNumber . '.pdf';
            Storage::disk('public')->put($ddtPdfPath, $pdf->output());

            // Store the DDT path in the database
            Ddt::create([
                'ddt_number' => $ddtNumber,
                'ddt_path' => $ddtPdfPath,
                'dealer_id' => $dealerId,
                'type' => 'returned',

            ]);

            // Send email with return DDT PDF to dealer
            if ($dealer->user->email) {
                Mail::send([], [], function ($message) use ($dealer, $pdf, $ddtNumber) {
                    $message->to($dealer->user->email)
                        ->subject("Return DDT Document #$ddtNumber")
                        ->attachData($pdf->output(), "Return_DDT_$ddtNumber.pdf");
                });
            }
        }

        Device::whereIn('id', $this->selectedDevices)->update(['dealer_id' => null]);

        // Optional: Refresh devices and clear selected devices
        $this->selectedDevices = [];
        $this->selectAllDevices = false;
        $this->dispatch('notice', type: 'success', text: __('messages.return_ddt_generated'));
        $this->dispatch('close-modal'); // Optionally emit an event for UI feedback

        // Delete Temporary Files
        if ($transportOfficerSignaturePath) {
            unlink(public_path('storage/' . $transportOfficerSignaturePath));
        }

        if ($recipientSignaturePath) {
            unlink(public_path('storage/' . $recipientSignaturePath));
        }
    }


    public function getFiltereddevices()
    {
        return Device::with([
            'dealer:id,user_id',
            'dealer.user:id,name',
            'client:id,user_id',
            'client.user:id,name'
        ])
            ->when(auth()->user()->role == 'dealer', function ($query) {
                $query->whereNotNull('dealer_id')->where('dealer_id', auth()->user()->dealer->id ?? 0);
            })
            ->when(auth()->user()->role == 'client', function ($query) {
                $query->whereNotNull('client_id')->where('client_id', auth()->user()->client->id ?? 0);
            })
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('imei', 'like', '%' . $this->search . '%')
                        ->orWhere('model', 'like', '%' . $this->search . '%')
                        ->orWhere('vehicle_type', 'like', '%' . $this->search . '%')
                        ->orWhereHas('client.user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%');
                        })
                        ->orWhereHas('dealer.user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->when($this->dealer, fn($query) => $query->where('dealer_id', $this->dealer))
            ->when($this->client, fn($query) => $query->where('client_id', $this->client))
            ->when(isset($this->status), fn($query) => $query->where('is_active', $this->status))
            ->when(isset($this->tested), fn($query) => $query->where('is_tested', $this->tested))
            ->when(isset($this->verified), fn($query) => $query->where('is_verified', $this->verified))
            ->latest()
            ->limit(300)
            ->get();
    }

    public function ignitionOn($imei = null)
    {
        if ($imei) {
            $command = 'setdigout 1';
            $name = 'Turn ON ignition';
            // Check if the command already exists in the queue for the given IMEI
            $existingCommand = Command::where('imei', $imei)
                ->where('command', $command)
                ->where('added_in_queue', 1)
                ->whereNull('response')
                ->first();

            if ($existingCommand) {
                $this->commandTestedId = $existingCommand->id;
                $this->dispatch('notice', type: 'error', text: __('messages.command_already_in_queue'));
                return;
            }

            // Save command to the database
            $command = Command::create([
                'imei' => $imei,
                'command' => $command,
                'name' => $name,
            ]);

            // Update the command queue JSON file
            $this->updateCommandQueue();

            $this->commandTestedId = $command->id;

            $this->dispatch('notice', type: 'success', text: __('messages.block_motor_command_sent'));
        } else {
            $this->dispatch('notice', type: 'error', text: __('messages.imei_not_received'));
        }
    }

    public function ignitionOff($imei = null)
    {
        if ($imei) {
            $command = 'setdigout 0';
            $name = 'Turn OFF ignition';
            // Check if the command already exists in the queue for the given IMEI
            $existingCommand = Command::where('imei', $imei)
                ->where('command', $command)
                ->where('added_in_queue', 1)
                ->whereNull('response')
                ->first();

            if ($existingCommand) {
                $this->commandTestedId = $existingCommand->id;


                $this->dispatch('notice', type: 'error', text: __('messages.command_already_in_queue'));
                return;
            }

            // Save command to the database
            $command = Command::create([
                'imei' => $imei,
                'command' => $command,
                'name' => $name,
            ]);

            // Update the command queue JSON file
            $this->updateCommandQueue();
            $this->commandTestedId = $command->id;


            $this->dispatch('notice', type: 'success', text: __('messages.unblock_motor_command_sent'));
        } else {
            $this->dispatch('notice', type: 'error', text: __('messages.imei_not_received'));
        }
    }

    private function updateCommandQueue()
    {
        // Fetch commands that should be added to the queue
        $commands = Command::whereNull('response')
            ->where('added_in_queue', '!=', 2) // Exclude commands marked as deleted
            ->get();

        $queue = [];
        foreach ($commands as $cmd) {
            $queue[$cmd->imei][] = [
                'command' => $cmd->command,
            ];
        }

        $path = public_path('command/queue.json');
        File::ensureDirectoryExists(dirname($path));
        File::put($path, json_encode($queue, JSON_PRETTY_PRINT));

        // Mark commands as added to queue (only those not already deleted)
        Command::whereNull('response')
            ->where('added_in_queue', 0)
            ->update(['added_in_queue' => 1]);
    }

    public function loadCommandReult()
    {
        $ignitionCommandResponse = Command::where('id', $this->commandTestedId)->where('command', 'like', '%setdigout%')
            ->latest()
            ->first();

        $this->ignitionCommandResponse = $ignitionCommandResponse;

        $this->testDevice();
    }



    public function render()
    {
        $devices = Device::with([
            'dealer:id,user_id,last_name',
            'dealer.user:id,name',
            'client:id,user_id,last_name',
            'client.user:id,name',
            'latestPendingContract'
        ])
            ->when(auth()->user()->role == 'dealer', function ($query) {
                $query->whereNotNull('dealer_id')->where('dealer_id', auth()->user()->dealer->id ?? 0);
            })
            ->when(auth()->user()->role == 'client', function ($query) {
                $query->whereNotNull('client_id')->where('client_id', auth()->user()->client->id ?? 0);
            })
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('imei', 'like', '%' . $this->search . '%')
                        ->orWhere('model', 'like', '%' . $this->search . '%')
                        ->orWhere('vehicle_type', 'like', '%' . $this->search . '%')
                        ->orWhereHas('client.user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%');
                        })
                        ->orWhereHas('dealer.user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->when(isset($this->status), fn($query) => $query->where('is_active', $this->status))
            ->when(isset($this->tested), fn($query) => $query->where('is_tested', $this->tested))
            ->when(isset($this->verified), fn($query) => $query->where('is_verified', $this->verified))
            ->when(isset($this->dealer), fn($query) => $query->where('dealer_id', $this->dealer))
            ->when(isset($this->client), fn($query) => $query->where('client_id', $this->client))
            ->latest()
            ->paginate(10);



        return view('livewire.panel.devices', compact('devices'));
    }
}
