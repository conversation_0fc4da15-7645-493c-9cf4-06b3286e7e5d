<?php

namespace App\Livewire\Panel;

use App\Mail\ContractAcceptedNotification;
use App\Mail\ContractStatusAdminNotification;
use App\Mail\ContractStatusDealerNotification;
use App\Models\Contract;
use App\Models\Device;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Component;

class ContractResponse extends Component
{
    public $contract;
    public $memove_privacy = 'rejected';
    public $memove_partner_privacy = 'rejected';

    #[Title('Contract - Memove')]
    #[Layout('layout.auth')]


    public function mount($contract_number = null)
    {
        $this->contract = Contract::where('contract_number', $contract_number)->first();

        if (!$this->contract) {
            $this->dispatch('notice', type: 'error', text: __('messages.contract_not_found'));

            abort(404);
            return;
        }
    }

    private function generatePassword()
    {
        // Define the characters to use for the password
        $letters = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';

        // Initialize the password as an empty string
        $password = '';

        // Add 6 random letters
        for ($i = 0; $i < 6; $i++) {
            $password .= $letters[rand(0, strlen($letters) - 1)];
        }

        // Add 2 random numbers
        for ($i = 0; $i < 2; $i++) {
            $password .= $numbers[rand(0, strlen($numbers) - 1)];
        }

        return $password;
    }

    public function respondContract($action)
    {

        // Check if the contract has already been responded to
        if ($this->contract->responded_at) {
            $this->dispatch('notice', type: 'error', text: __('messages.contract_status_message', ['status' => $this->contract->status == 'active' ? __('messages.accepted') : __('messages.rejected')]));

            return;
        }

        $type = 'Rejected';
        if ($action === 'accept') {
            if ($this->memove_privacy != 'accepted' || $this->memove_partner_privacy != 'accepted') {
                if ($this->memove_privacy != 'accepted') {
                    $this->addError('memove_privacy', __('messages.memove_privacy_error'));
                } else {
                    $this->resetErrorBag('memove_privacy');
                }
                if ($this->memove_partner_privacy != 'accepted') {
                    $this->addError('memove_partner_privacy', __('messages.memove_partner_privacy_error'));
                } else {
                    $this->resetErrorBag('memove_partner_privacy');
                }
                return;
            }

            $this->contract->status = 'active';
            $this->contract->signed = 1;

            if ($this->contract?->client_id) {
                $this->contract?->device?->update([
                    'client_id' => $this->contract->client_id
                ]);
            }

            $type = 'Accepted';

            $password = $this->generatePassword();
            if ($this->contract?->client?->user?->email ?? null) {
                $this->contract->client->user->update([
                    'password' => Hash::make($password),
                    'is_active' => true
                ]);
                if ($this->contract?->client?->phone_number) {
                    try {
                        // Authenticate User
                        $auth = authenticateUser(env('ARUBA_SMS_USERNAME'), env('ARUBA_SMS_PASSWORD'));

                        if ($auth) {
                            // Send SMS
                            $recipients = [$this->contract?->client?->phone_number]; // List of recipients
                            $sender = 'AllTech'; // Optional sender name

                            $message = "Contratto accettato! Il tuo account MeMove è attivo. Nome utente: {$this->contract?->client?->user?->username}, Email: {$this->contract?->client?->user?->email}, Password: {$password}. Accedi: " . url('/');

                            $scheduleTime = gmdate('YmdHis', strtotime("+5 minutes")); // Ensure correct format



                            $smsResponse = sendSMS($auth, $message, $recipients, $sender, $scheduleTime);

                            // Display Result
                            if ($smsResponse && isset($smsResponse["result"]) && $smsResponse["result"] === "OK") {
                            } else {
                                \Log::info('Add Client: Failed to send SMS.');
                            }
                        } else {
                            \Log::info('Add Client: Authentication failed. Please check your credentials.');
                        }
                    } catch (\Exception $e) {
                        \Log::info('Contract Response: ' . $e->getMessage());
                    }
                }

                Device::find($this->contract->device_id)->update([
                    'is_verified' => 1,
                    'vehicle_type' => $this->contract->verification_vehicle_type,
                    'number_plate' => $this->contract->vehicle_number_plate,
                    'vehicle_model' => $this->contract->vehicle_model,
                    'vehicle_brand' => $this->contract->vehicle_brand,
                    'motor_block_enabled' => $this->contract->starter_motor_block == 'Yes' ? 1 : 0,
                    'initial_odometer' => $this->contract->vehicle_km
                ]);


                $this->contract->responded_at = Carbon::now();
                $this->contract->save();

                $contract = $this->contract;


                $pdf = Pdf::loadView('pdf.contract', compact('contract'))->setOption('defaultFont', 'Roboto');

                $mainPDF = Pdf::loadView('pdf.contract-collaudo', compact('contract'))->setOption('defaultFont', 'Roboto');
                // Define the storage path
                $pdfFileName = "contracts/Contratto_{$contract?->contract_number}.pdf";
                Storage::put($pdfFileName, $mainPDF->output());
                // Define the storage path
                $pdfFileName = "contracts/Contratto_full_{$contract?->contract_number}.pdf";
                Storage::put($pdfFileName, $pdf->output());


                // Send email notification
                Mail::to($contract->client->user->email)
                    ->send(new ContractAcceptedNotification($contract, $password));
            }
        } elseif ($action === 'reject') {
            $this->contract->status = 'rejected';

            $this->contract->responded_at = Carbon::now();
            $this->contract->save();
        }


        Mail::to(env('ADMIN_EMAIL'))->send(new ContractStatusAdminNotification($this->contract, $type));
        Mail::to($this->contract?->dealer?->user?->email)->send(new ContractStatusDealerNotification($this->contract, $type));


        $this->dispatch('notice', type: 'success', text: 'Contract successfully ' . $type);
    }

    public function downloadContract()
    {
        $contract = Contract::find($this->contract->id);

        // Generate PDF from Blade view
        $pdf = Pdf::loadView('pdf.contract', compact('contract'))->setOption('defaultFont', 'Roboto');


        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, "contratto_$contract->contract_number.pdf");
    }


    public function render()
    {
        return view('livewire.panel.contract-response');
    }
}
