<?php

namespace App\Livewire\Panel;

use App\Models\Event;
use Livewire\Component;
use Livewire\WithPagination;

class Events extends Component
{
    use WithPagination;

    public $search;


    public function render()
    {
        $events = Event::when(auth()->user()->role === 'dealer', function ($query) {
            $query->whereIn('imei', auth()->user()->dealer->devices()->pluck('imei'));
        })->when(auth()->user()->role === 'client', function ($query) {
            $query->whereIn('imei', auth()->user()->client->devices()->pluck('imei'));
        })->when(isset($this->search), function ($query) {
            $query->where('imei', 'like', '%' . $this->search . '%');
        })->latest()
            ->paginate(10);


        return view('livewire.panel.events', compact('events'));
    }
}
