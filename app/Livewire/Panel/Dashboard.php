<?php

namespace App\Livewire\Panel;

use App\Models\Device;
use Livewire\Component;

class Dashboard extends Component
{

    public function mount()
    {

        if (auth()->user()->role == 'dealer' || auth()->user()->role == 'warehouse_operator') {
            return redirect()->route('devices');
        }
    }

    // public $ignition;

    // public function updatedIgnition()
    // {

    //     $server = '3.76.234.36';
    //     $port = 9652;
    //     $socket = fsockopen($server, $port);
    //     fwrite($socket, 'setigndigout 1 0 0 0');
    //     $response = fread($socket, 1024);
    //     fclose($socket);
    //     return $response;
    // }


    public function render()
    {
        $dealerDevices = null;

        if (auth()->user()->role == 'dealer') {
            $dealerDevices = Device::whereNotNull('dealer_id')->where('dealer_id', auth()->user()->dealer->id ?? 0)->pluck('imei')->toArray();
        }
        if (auth()->user()->role == 'client') {
            $dealerDevices = Device::whereNotNull('client_id')->where('client_id', auth()->user()->client->id ?? 0)->pluck('imei')->toArray();
        }
        return view('livewire.panel.dashboard', compact('dealerDevices'));
    }
}
