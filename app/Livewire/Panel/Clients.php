<?php

namespace App\Livewire\Panel;

use App\Exports\ClientsExport;
use App\Mail\ClientCreated;
use App\Mail\PasswordResetMail;
use App\Models\Client;
use App\Models\Contract;
use App\Models\PasswordResetToken;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;

class Clients extends Component
{

    use WithPagination;


    public $name, $username, $type = 'private', $last_name, $company, $email, $phone_number, $password, $dealer, $address, $municipality, $zip_code, $province, $tax_code, $is_active = true;

    public $recordId, $deleteRecordId, $search, $status;
    public $dealers;

    public function mount()
    {
        $this->dealers = User::where('role', 'dealer')
            ->where('is_active', 1)
            ->with('dealer:id,user_id')
            ->latest()
            ->get()
            ->mapWithKeys(function ($user) {
                return [$user->dealer->id ?? null => $user->name];
            })
            ->filter() // This will remove any null keys if a user does not have a dealer.
            ->all();
    }


    public function editRecord($recordId)
    {
        $this->recordId = $recordId;

        if ($recordId) {
            $this->recordId = $recordId;
            $client = Client::find($this->recordId);

            if ($client) {
                // Set existing client data when editing
                $this->name = $client->user->name ?? '';
                $this->username = $client->user->username ?? '';
                $this->email = $client->user->email ?? '';
                $this->phone_number = $client->phone_number ?? '';
                $this->is_active = $client->user->is_active ? true : false;
                $this->address = $client->address;
                $this->company = $client->company;
                $this->municipality = $client->municipality;
                $this->zip_code = $client->zip_code;
                $this->province = $client->province;
                $this->tax_code = $client->tax_code;
                $this->dealer = $client->dealer_id; // dealer_id will be used for selecting the dealer
                $this->type = $client->type  ?? 'private';
                $this->last_name = $client->last_name;

                $this->dispatch('open-modal', name: 'add-client-modal');
            }
        }
    }

    // Add or Update Client
    public function addUpdateClient()
    {
        $this->validate();

        $passkey = $this->generateUniquePasskey();


        if ($this->recordId) {
            // Update existing client
            $client = Client::find($this->recordId);
            if ($client) {
                $client->company = $this->company;
                $client->address = $this->address;
                $client->municipality = $this->municipality;
                $client->zip_code = $this->zip_code;
                $client->province = $this->province;
                $client->tax_code = $this->tax_code;
                $client->dealer_id = $this->dealer;
                $client->phone_number = $this->phone_number;
                $client->last_name = $this->last_name;
                $client->type = $this->type;

                if (!$client->passkey) {
                    $client->passkey = $passkey;
                }

                $client->save();

                // Optionally update user if needed
                $client->user->update([
                    'name' => $this->name,
                    'username' => $this->username,
                    'email' => $this->email,
                    'password' => $this->password ? Hash::make($this->password) : $client->user->password,
                    'is_active' => $this->is_active ? true : false
                ]);
            }
        } else {

            $is_active = false;
            if (auth()->user()->role == 'admin') {
                $is_active = $this->is_active ? true : false;
            }
            // Create new client
            $user = User::create([
                'name' => $this->name,
                'username' => $this->username,
                'email' => $this->email,
                'password' => Hash::make($this->password), // Password hashing for new user
                'role' => 'client',
                'is_active' => $is_active,
            ]);

            $dealerId = $this->dealer;
            if (auth()->user()->role == 'dealer') {
                $dealerId = auth()->user()->dealer->id;
            }

            $client = new Client([
                'company' => $this->company,
                'address' => $this->address,
                'municipality' => $this->municipality,
                'zip_code' => $this->zip_code,
                'province' => $this->province,
                'tax_code' => $this->tax_code,
                'dealer_id' => $dealerId,
                'phone_number' => $this->phone_number,
                'passkey' => $passkey,
                'type' => $this->type,
                'last_name' => $this->last_name,
            ]);
            $client->user()->associate($user); // Associate user with client
            $client->save();

            if (auth()->user()->role == 'admin' && $is_active) {
                Mail::to($user->email)->send(new ClientCreated($user, $this->password, $passkey));
            }
        }

        $this->dispatch('notice', type: 'error', text: $this->recordId ? __('messages.client_created') : __('messages.client_updated'));

        $this->dispatch('close-modal');

        $this->reset();
    }

    public function generateUniquePasskey()
    {
        do {
            // Generate a random 16-character passkey
            $passkey = Str::random(10);
        } while (\App\Models\Client::where('passkey', $passkey)->exists());

        return $passkey;
    }

    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            $client = Client::find($this->deleteRecordId);

            if (Contract::where('client_id', $client->id)->where('status', 'active')->exists()) {
                $this->dispatch('notice', type: 'error', text: __('messages.cannot_delete_active_contract_client'));
                return;
            }
            $client->delete();

            $client->user()->delete();


            $this->dispatch('notice', type: 'error', text: __('messages.client_deleted'));
            $this->dispatch('close-modal');
        } else {
            $this->dispatch('notice', type: 'error', text: 'Client not found!');
        }
    }

    public function clearRecords()
    {
        $this->reset();
    }


    // Validation rules
    public function rules()
    {
        return [
            'name' => ['required'],
            'username' => ['required', 'string', 'max:255', Rule::unique('users')->ignore($this->recordId ? Client::find($this->recordId)->user->id : null)],
            'email' => ['required', 'email', 'max:255', Rule::unique('users')->ignore($this->recordId ? Client::find($this->recordId)->user->id : null)],
            'password' => $this->recordId ? ['nullable', 'min:6'] : ['required', 'min:6'],
            'dealer' => ['nullable', 'exists:dealers,id'],
            'address' => ['required', 'string', 'max:255'],
            'municipality' => ['nullable', 'string', 'max:255'],
            'zip_code' => ['nullable', 'string', 'max:15'],
            'company' => ['nullable', 'string', 'max:255'],
            'province' => ['nullable', 'string', 'max:255'],
            'tax_code' => ['nullable', 'string', 'max:100'],
            'phone_number' => ['nullable', 'string', 'max:100'],
        ];
    }

    // Method to export as Excel
    public function exportExcel()
    {
        return Excel::download(new ClientsExport($this->search, $this->status), 'clients.xlsx');
    }

    // Method to export as PDF
    public function exportPDF()
    {
        $clients = $this->getFilteredClients();


        $pdf = Pdf::loadView('pdf.clients', [
            'clients' => $clients->map(function ($client) {
                return array_map(fn($value) => mb_convert_encoding($value, 'UTF-8', 'UTF-8'), [
                    'name' => $client->name,
                    'username' => $client->user->username,
                    'email' => $client->user->email,
                    'phone_number' => $client->phone_number,
                    'dealer_name' => $client->dealer?->user?->name ?? 'N/A',
                    'address' => $client->address,
                    'company' => $client->company,
                    'municipality' => $client->municipality,
                    'zip_code' => $client->zip_code,
                    'province' => $client->province,
                    'tax_code' => $client->tax_code,
                ]);
            })
        ]);

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, 'clients.pdf');



        // return $pdf->download('clients.pdf');
    }

    public function sendPasswordResetLink($email)
    {
        // Check if the email belongs to a registered user
        $user = User::where('email', $email)->exists();

        if (!$user) {
            $this->dispatch('notice', type: 'error', text: 'No account found with that email.');
            return;
        }

        // Generate token and store it in the database
        $token = Str::random(60);
        PasswordResetToken::updateOrCreate(
            ['email' => $email],
            ['token' => $token, 'created_at' => Carbon::now()]
        );

        // Generate the reset URL
        $resetUrl = route('password.reset', ['token' => $token, 'email' => $email]);


        Mail::to($email)->send(new PasswordResetMail($email, $resetUrl));


        $this->dispatch('notice', type: 'success', text: __('messages.password_reset_link_sent'));
    }

    // Query to filter clients based on search
    public function getFilteredClients()
    {
        return Client::with(['user', 'dealer.user'])
            ->when(auth()->user()->role == 'dealer', function ($query) {
                $query->whereNotNull('dealer_id')->where('dealer_id', auth()->user()->dealer->id ?? 0);
            })
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('address', 'like', '%' . $this->search . '%')
                        ->orWhere('company', 'like', '%' . $this->search . '%')
                        ->orWhereHas('user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('username', 'like', '%' . $this->search . '%')
                                ->orWhere('email', 'like', '%' . $this->search . '%')
                                ->orWhere('phone_number', 'like', '%' . $this->search . '%');
                        })
                        ->orWhereHas('dealer.user', function ($q) {
                            $q->where('username', 'like', '%' . $this->search . '%')
                                ->orWhere('name', 'like', '%' . $this->search . '%');
                        });
                })
                    ->when($this->status, function ($query) {
                        $query->where(function ($query) {
                            $query->whereHas('user', function ($q) {
                                $q->where('is_active', $this->status);
                            });
                        });
                    });
            })
            ->get();
    }


    public function render()
    {
        $clients = Client::with(['user', 'dealer:id,user_id', 'dealer.user:id,name']) // Include any necessary relations
            ->when(auth()->user()->role == 'dealer', function ($query) {
                $query->whereNotNull('dealer_id')->where('dealer_id', auth()->user()->dealer->id ?? 0);
            })
            ->when(isset($this->search), function ($query) {
                $query->where(function ($query) {
                    $query->where('address', 'like', '%' . $this->search . '%')
                        ->orWhere('company', 'like', '%' . $this->search . '%')
                        ->orWhereHas('user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('username', 'like', '%' . $this->search . '%')
                                ->orWhere('email', 'like', '%' . $this->search . '%');
                        })
                        ->orWhereHas('dealer.user', function ($q) {
                            $q->where('username', 'like', '%' . $this->search . '%')
                                ->orWhere('name', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->when(isset($this->status), function ($query) {
                $query->whereHas('user', function ($q) {
                    $q->where('is_active', $this->status);
                });
            })
            ->latest()
            ->paginate(10);




        return view('livewire.panel.clients', compact('clients'));
    }
}
