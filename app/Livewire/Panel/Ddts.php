<?php

namespace App\Livewire\Panel;

use App\Models\Ddt;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithPagination;

class Ddts extends Component
{

    use WithPagination;

    public $deleteRecordId, $search;


    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            $ddt = Ddt::find($this->deleteRecordId);
            // Check if the DDT file exists in storage and delete it
            if ($ddt->ddt_path && Storage::disk('public')->exists($ddt->ddt_path)) {
                Storage::disk('public')->delete($ddt->ddt_path);
            }
            $ddt->delete();



            $this->dispatch('notice', type: 'error', text: __('messages.ddt_deleted'));

            $this->dispatch('close-modal');
        } else {
            $this->dispatch('notice', type: 'error', text: 'DDT not found!');
        }
    }

    public function clearRecords()
    {
        $this->reset();
    }


    public function downloadDDT($ddtId)
    {
        // Fetch the DDT record
        $ddt = Ddt::find($ddtId);

        if (!$ddt || !Storage::disk('public')->exists($ddt->ddt_path)) {
            $this->dispatch('notice', type: 'error', text: __('messages.ddt_file_not_found'));
            return;
        }

        // Generate the download response
        return response()->download(storage_path('app/public/' . $ddt->ddt_path));
    }





    public function render()
    {
        $ddts = Ddt::when(isset($this->search), function ($query) {
            $query->where(function ($query) {
                $query->where('ddt_number', 'like', '%' . $this->search . '%');
            })->orWhereHas('dealer.user', function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('username', 'like', '%' . $this->search . '%');
            });
        })
            ->when(auth()->user()->role == 'dealer', function ($query) {
                $query->where('dealer_id', auth()->user()->dealer->id ?? null);
            })
            ->when(isset($this->status), function ($query) {
                $query->where('is_active', $this->status);
            })
            ->latest()
            ->paginate(10);
        return view('livewire.panel.ddts', compact('ddts'));
    }
}
