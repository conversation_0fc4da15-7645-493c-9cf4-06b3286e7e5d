<?php

namespace App\Livewire\Panel;

use App\Exports\DealersExport;
use App\Mail\DealerCreated;
use App\Mail\PasswordResetMail;
use App\Models\Dealer;
use App\Models\PasswordResetToken;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;


class Dealers extends Component
{
    use WithPagination;


    public $name, $username, $type = 'private', $last_name, $email, $password, $company, $vat, $address, $municipality, $zip_code, $province, $tax_code, $is_active = true, $phone_number;

    public $recordId, $deleteRecordId, $search, $status;


    public function editRecord($recordId)
    {
        $this->recordId = $recordId;

        if ($recordId) {
            $this->recordId = $recordId;
            $dealer = Dealer::find($this->recordId);

            if ($dealer) {
                // Set existing dealer data when editing
                $this->name = $dealer->user->name ?? '';
                $this->username = $dealer->user->username ?? '';
                $this->email = $dealer->user->email ?? '';
                $this->phone_number = $dealer->phone_number ?? '';
                $this->is_active = $dealer->user->is_active ? true : false;
                $this->address = $dealer->address;
                $this->municipality = $dealer->municipality;
                $this->zip_code = $dealer->zip_code;
                $this->province = $dealer->province;
                $this->tax_code = $dealer->tax_code;
                $this->company = $dealer->company;
                $this->vat = $dealer->vat;
                $this->type = $dealer->type ?? 'private';
                $this->last_name = $dealer->last_name;

                $this->dispatch('open-modal', name: 'add-dealer-modal');
            }
        }
    }

    // Add or Update Dealer
    public function addUpdateDealer()
    {
        $this->validate();

        if ($this->recordId) {
            // Update existing dealer
            $dealer = Dealer::find($this->recordId);
            if ($dealer) {
                $dealer->address = $this->address;
                $dealer->municipality = $this->municipality;
                $dealer->zip_code = $this->zip_code;
                $dealer->province = $this->province;
                $dealer->tax_code = $this->tax_code;
                $dealer->company = $this->company;
                $dealer->vat = $this->vat;
                $dealer->type = $this->type;
                $dealer->last_name = $this->last_name;
                $dealer->phone_number = $this->phone_number;
                $dealer->save();

                // Optionally update user if needed
                $dealer->user->update([
                    'name' => $this->name,
                    'username' => $this->username,
                    'email' => $this->email,
                    'password' => $this->password ? Hash::make($this->password) : $dealer->user->password,
                    'is_active' => $this->is_active ? true : false
                ]);
            }
        } else {
            // Create new dealer
            $user = User::create([
                'name' => $this->name,
                'username' => $this->username,
                'email' => $this->email,
                'password' => Hash::make($this->password), // Password hashing for new user
                'role' => 'dealer',
                'is_active' => $this->is_active ? true : false
            ]);

            $dealer = new Dealer([
                'address' => $this->address,
                'municipality' => $this->municipality,
                'zip_code' => $this->zip_code,
                'province' => $this->province,
                'tax_code' => $this->tax_code,
                'company' => $this->company,
                'vat' => $this->vat,
                'type' => $this->type,
                'last_name' => $this->last_name,
                'phone_number' => $this->phone_number,
            ]);
            $dealer->user()->associate($user); // Associate user with dealer
            $dealer->save();


            // Send email to dealer with login details
            Mail::to($user->email)->send(new DealerCreated($user, $this->password));
        }

        $this->dispatch('notice', type: 'error', text: $this->recordId ? __('messages.dealer_updated') : __('messages.dealer_created'));

        $this->dispatch('close-modal');

        $this->reset();
    }

    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            $dealer = Dealer::find($this->deleteRecordId);

            $dealer->user()->delete();
            
            $dealer->delete();



            $this->dispatch('notice', type: 'error', text: __('messages.dealer_deleted'));
            $this->dispatch('close-modal');
        } else {
            $this->dispatch('notice', type: 'error', text: 'Dealer not found!');
        }
    }

    public function clearRecords()
    {
        $this->reset();
    }


    // Validation rules
    public function rules()
    {
        return [
            'name' => ['required'],
            'username' => ['required', 'string', 'max:255', Rule::unique('users')->ignore($this->recordId ? Dealer::find($this->recordId)->user->id : null)],
            'email' => ['required', 'email', 'max:255', Rule::unique('users')->ignore($this->recordId ? Dealer::find($this->recordId)->user->id : null)],
            'password' => $this->recordId ? ['nullable', 'min:6'] : ['required', 'min:6'],
            'company' => ['nullable', 'max:255'],
            'vat' => ['nullable', 'max:255'],
            'address' => ['required', 'string', 'max:255'],
            'municipality' => ['nullable', 'string', 'max:255'],
            'zip_code' => ['nullable', 'string', 'max:15'],
            'province' => ['nullable', 'string', 'max:255'],
            'tax_code' => ['nullable', 'string', 'max:100'],
            'phone_number' => ['nullable', 'string', 'max:100'],
        ];
    }

    // Method to export as Excel
    public function exportExcel()
    {
        return Excel::download(new DealersExport($this->search, $this->status), 'dealers.xlsx');
    }

    // Method to export as PDF
    public function exportPDF()
    {
        $dealers = $this->getFiltereddealers();


        $pdf = Pdf::loadView('pdf.dealers', [
            'dealers' => $dealers->map(function ($dealer) {
                return array_map(fn($value) => mb_convert_encoding($value, 'UTF-8', 'UTF-8'), [
                    'name' => $dealer->name,
                    'username' => $dealer->user->username,
                    'email' => $dealer->user->email,
                    'phone_number' => $dealer->phone_number,
                    'company' => $dealer->company ?? 'N/A',
                    'vat' => $dealer->vat,
                    'address' => $dealer->address,
                    'municipality' => $dealer->municipality,
                    'zip_code' => $dealer->zip_code,
                    'province' => $dealer->province,
                    'tax_code' => $dealer->tax_code,
                ]);
            })
        ]);

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, 'dealers.pdf');



        // return $pdf->download('dealers.pdf');
    }

    public function sendPasswordResetLink($email)
    {
        // Check if the email belongs to a registered user
        $user = User::where('email', $email)->exists();

        if (!$user) {
            $this->dispatch('notice', type: 'error', text: 'No account found with that email.');
            return;
        }

        // Generate token and store it in the database
        $token = Str::random(60);
        PasswordResetToken::updateOrCreate(
            ['email' => $email],
            ['token' => $token, 'created_at' => Carbon::now()]
        );

        // Generate the reset URL
        $resetUrl = route('password.reset', ['token' => $token, 'email' => $email]);


        Mail::to($email)->send(new PasswordResetMail($email, $resetUrl));


        $this->dispatch('notice', type: 'success', text: __('messages.password_reset_link_sent'));
    }

    // Query to filter dealers based on search
    public function getFiltereddealers()
    {
        return Dealer::with(['user'])
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('address', 'like', '%' . $this->search . '%')
                        ->orWhere('company', 'like', '%' . $this->search . '%')
                        ->orWhereHas('user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('username', 'like', '%' . $this->search . '%')
                                ->orWhere('email', 'like', '%' . $this->search . '%')
                                ->orWhere('phone_number', 'like', '%' . $this->search . '%');
                        });
                })->when($this->status, function ($query) {
                    $query->where(function ($query) {
                        $query->whereHas('user', function ($q) {
                            $q->where('is_active', $this->status);
                        });
                    });
                });
            })
            ->get();
    }


    public function render()
    {
        $dealers = Dealer::with(['user']) // Include any necessary relations
            ->when(isset($this->search), function ($query) {
                $query->where(function ($query) {
                    $query->where('address', 'like', '%' . $this->search . '%')
                        ->orWhere('company', 'like', '%' . $this->search . '%')
                        ->orWhereHas('user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('username', 'like', '%' . $this->search . '%')
                                ->orWhere('email', 'like', '%' . $this->search . '%')
                                ->orWhere('phone_number', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->when(isset($this->status), function ($query) {
                $query->whereHas('user', function ($q) {
                    $q->where('is_active', $this->status);
                });
            })
            ->latest()
            ->paginate(10);




        return view('livewire.panel.dealers', compact('dealers'));
    }
}
