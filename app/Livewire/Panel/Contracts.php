<?php

namespace App\Livewire\Panel;

use App\Exports\ContractsExport;
use App\Models\Client;
use App\Models\Contract;
use App\Models\Device;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Livewire\WithFileUploads;

class Contracts extends Component
{
    use WithPagination, WithFileUploads;


    public $device_id, $client_id, $dealer_id, $duration, $signed, $start_date, $end_date, $contract_status;

    public $recordId, $deleteRecordId, $search, $status;

    public $dealers, $clients, $devices;

    public $selectedContract = null;

    public $deviceVerifyData = null, $old_contract;

    public $verification_client, $verification_vehicle_type = 'truck', $service_details, $vehicle_brand, $vehicle_model, $vehicle_color, $vehicle_registration_number, $vehicle_chassis, $vehicle_km, $starter_motor_block = "No", $vehicle_number_plate, $vehicle_registration_date, $contract_duration, $contract_start_date, $contract_end_date, $phone_number, $frame, $installation_image, $old_installation_image, $verification_dealer_id, $inactive_client = 0;

    public $username, $name, $type = 'private', $last_name, $email, $password, $address, $company, $municipality, $zip_code, $province, $tax_code, $is_active = false;


    public function mount()
    {
        $this->dealers = User::where('role', 'dealer')
            ->where('is_active', 1)
            ->with('dealer:id,user_id,last_name')
            ->latest()
            ->get()
            ->mapWithKeys(function ($user) {
                return [$user->dealer->id ?? null => $user->name . ' ' . $user->dealer?->last_name];
            })
            ->filter() // This will remove any null keys if a user does not have a dealer.
            ->all();

        $this->clients = User::where('role', 'client')
            ->where('is_active', 1)
            ->with('client:id,user_id,last_name')
            ->latest()
            ->get()
            ->mapWithKeys(function ($user) {
                return [$user->client->id ?? null => $user->name . ' ' . $user->client?->last_name];
            })
            ->filter() // This will remove any null keys if a user does not have a dealer.
            ->all();

        $this->devices = Device::where('is_active', 1)
            ->with('dealer:id,user_id')
            ->latest()
            ->pluck('imei', 'id');
    }


    public function editRecord($recordId)
    {
        $this->recordId = $recordId;

        if ($recordId) {
            $this->recordId = $recordId;
            $contract = Contract::find($this->recordId);

            if ($contract) {
                $this->device_id = $contract->device_id;
                $this->client_id = $contract->client_id;
                $this->dealer_id = $contract->dealer_id;
                $this->duration = $contract->duration;
                $this->signed = $contract->signed;
                $this->start_date = $contract->start_date;
                $this->end_date = $contract->end_date;
                $this->contract_status = $contract->status;
                $this->verification_vehicle_type = $contract->verification_vehicle_type;
                $this->vehicle_brand = $contract->vehicle_brand;
                $this->vehicle_model = $contract->vehicle_model;
                $this->vehicle_color = $contract->vehicle_color;
                $this->vehicle_registration_number = $contract->vehicle_registration_number;
                $this->vehicle_chassis = $contract->vehicle_chassis;
                $this->vehicle_km = $contract->vehicle_km;
                $this->starter_motor_block = $contract->starter_motor_block;
                $this->vehicle_number_plate = $contract->vehicle_number_plate;
                $this->vehicle_registration_date = $contract->vehicle_registration_date;
                $this->contract_duration = $contract->duration;
                $this->contract_start_date = $contract->start_date;
                $this->contract_end_date = $contract->end_date;
                $this->phone_number = $contract->phone_number;
                $this->frame = $contract->frame;
                $this->old_installation_image = $contract->installation_image;
                $this->service_details = $contract->service_details;

                $this->dispatch('open-modal', name: 'add-contract-modal');
            }
        }
    }
    public function viewRecord($recordId)
    {
        $this->selectedContract  = null;

        if ($recordId) {
            $this->selectedContract = Contract::find($recordId);

            $this->dispatch('open-modal', name: 'view-contract-modal');
        }
    }

    // Add or Update Contract
    public function addUpdateContract()
    {
        $this->validate([
            'device_id' => 'required',
            'client_id' => 'required',
            'dealer_id' => 'required',
            'starter_motor_block' => 'required|max:255',

            'verification_vehicle_type' => 'required|max:255',
            'vehicle_number_plate' => 'required|max:255',
            'contract_duration' => 'required|max:255',
            'contract_start_date' => 'required|max:255',
            'contract_end_date' => 'required|max:255',
            'vehicle_brand' => 'required|max:255',
            'vehicle_model' => 'required|max:255',
            'vehicle_color' => 'required|max:255',
            'vehicle_registration_date' => 'required|max:255',
            'vehicle_km' => 'required|max:255',
        ]);

        if ($this->recordId) {
            $uploadedFile = null;
            if ($this->installation_image) {
                $uploadedFile = $this->installation_image->store('images', 'public');
            } else {
                $uploadedFile = $this->old_installation_image;
            }
            // Update existing contract
            $contract = Contract::find($this->recordId);
            if ($contract) {
                $contract->device_id = $this->device_id;
                $contract->client_id = $this->client_id;
                $contract->dealer_id = $this->dealer_id;
                $contract->duration = $this->duration;
                $contract->signed = $this->signed;
                $contract->start_date = $this->start_date;
                $contract->end_date = $this->end_date;

                $contract->status = $this->contract_status;
                $contract->verification_vehicle_type = $this->verification_vehicle_type;
                $contract->vehicle_brand = $this->vehicle_brand;
                $contract->vehicle_model = $this->vehicle_model;
                $contract->vehicle_color = $this->vehicle_color;
                $contract->vehicle_registration_date = $this->vehicle_registration_date;
                $contract->vehicle_number_plate = $this->vehicle_number_plate;
                $contract->frame = $this->frame;
                $contract->vehicle_km = $this->vehicle_km;
                $contract->starter_motor_block = $this->starter_motor_block;
                $contract->installation_image = $uploadedFile;
                $contract->service_details = $this->service_details;

                $contract->save();

                // update device number_plate
                Device::find($this->device_id)->update([
                    'number_plate' => $this->vehicle_number_plate,
                    'vehicle_type' => $this->verification_vehicle_type,
                    'vehicle_model' => $this->vehicle_model,
                    'vehicle_brand' => $this->vehicle_brand,
                    'motor_block_enabled' => $this->starter_motor_block == 'Yes' ? 1 : 0,
                    'initial_odometer' => $this->vehicle_km
                ]);
            }
        } else {
            $contract = Contract::create([
                'device_id' => $this->device_id,
                'client_id' => $this->client_id,
                'dealer_id' => $this->dealer_id,
                'duration' => $this->duration,
                'signed' => $this->signed,
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
                'status' => $this->contract_status,

            ]);
        }

        $this->dispatch('notice', type: 'error', text: $this->recordId ? __('messages.contract_updated') : __('messages.contract_created'));


        $this->dispatch('close-modal');

        $this->reset();
    }

    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            $contract = Contract::find($this->deleteRecordId);

            $contract->delete();

            $this->dispatch('notice', type: 'error', text: __('messages.contract_deleted'));
            $this->dispatch('close-modal');
        } else {
            $this->dispatch('notice', type: 'error', text: 'Contract not found!');
        }
    }

    public function clearRecords()
    {
        $this->resetExcept('dealers', 'clients', 'devices');
    }



    // Method to export as Excel
    public function exportExcel()
    {
        return Excel::download(new ContractsExport($this->getFilteredcontracts()), 'contracts.xlsx');
    }

    // Method to export as PDF
    public function exportPDF()
    {
        $contracts = $this->getFilteredcontracts();


        $pdf = Pdf::loadView('pdf.contracts', [
            'contracts' => $contracts->map(function ($contract) {
                return array_map(fn($value) => mb_convert_encoding($value, 'UTF-8', 'UTF-8'), [
                    'device' => $contract->device->imei,
                    'client' => $contract->client->user->name,
                    'dealer' => $contract->dealer->user->name,
                    'duration' => $contract->duration,
                    'signed' => $contract->signed,
                    'start_date' => $contract->start_date,
                    'end_date' => $contract->end_date,
                    'status' => $contract->status,
                    'created_at' => $contract->created_at->format('d/m/Y H:i'),
                ]);
            })
        ]);

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, 'contracts.pdf');



        // return $pdf->download('contracts.pdf');
    }

    public function downloadCollaudoContract($contract_id)
    {
        $contract = Contract::find($contract_id);

        // $pdfFileName = "contracts/Contratto_{$contract?->contract_number}.pdf";

        // if (Storage::exists($pdfFileName)) {
        //     return Storage::download($pdfFileName);
        // } else {
        // Generate PDF from Blade view
        $pdf = Pdf::loadView('pdf.contract-collaudo', compact('contract'))->setOption('defaultFont', 'Roboto');

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, "contratto_$contract->contract_number.pdf");
        // }
    }
    public function downloadContract($contract_id)
    {
        $contract = Contract::find($contract_id);

        // $pdfFileName = "contracts/Contratto_full_{$contract?->contract_number}.pdf";

        // if (Storage::exists($pdfFileName)) {
        //     return Storage::download($pdfFileName);
        // } else {
        // Generate PDF from Blade view
        $pdf = Pdf::loadView('pdf.contract', compact('contract'))->setOption('defaultFont', 'Roboto');

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, "contratto_$contract->contract_number.pdf");
        // }
    }


    // Query to filter contracts based on search
    public function getFilteredcontracts()
    {
        return Contract::with(['device:id,imei,model', 'dealer:id,user_id', 'dealer.user:id,name', 'client:id,user_id', 'client.user:id,name']) // Include any necessary relations
            ->when(auth()->user()->role == 'dealer', function ($query) {
                $query->whereNotNull('dealer_id')->where('dealer_id', auth()->user()->dealer->id ?? 0);
            })
            ->when(auth()->user()->role == 'client', function ($query) {
                $query->whereNotNull('client_id')->where('client_id', auth()->user()->client->id ?? 0);
            })
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('duration', 'like', '%' . $this->search . '%')
                        ->orWhereHas('device', function ($q) {
                            $q->where('imei', 'like', '%' . $this->search . '%');
                        })
                        ->orWhereHas('dealer.user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('username', 'like', '%' . $this->search . '%');
                        })
                        ->orWhereHas('client.user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('username', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->when($this->status, function ($query) {
                $query->where(function ($query) {
                    $query->where('status', $this->status);
                });
            })
            ->get();
    }


    // Calculate end date when duration or start date is updated
    public function updatedDuration()
    {
        $this->calculateEndDate();
    }

    public function updatedStartDate()
    {
        $this->calculateEndDate();
    }

    // Calculate end date based on start date and duration
    public function calculateEndDate()
    {
        if ($this->start_date && $this->duration) {
            $startDate = Carbon::parse($this->start_date);
            $endDate = $startDate->copy()->addMonths((int)$this->duration);
            $this->end_date = $endDate->format('Y-m-d');
        } else {
            $this->end_date = null;
        }
    }

    public function updatedContractDuration()
    {
        $this->calculateContractEndDate();
    }

    public function updatedContractStartDate()
    {
        $this->calculateContractEndDate();
    }

    // Calculate end date based on start date and duration
    public function calculateContractEndDate()
    {
        if ($this->contract_start_date && $this->contract_duration) {
            $startDate = Carbon::parse($this->contract_start_date);
            $endDate = $startDate->copy()->addMonths((int)$this->contract_duration);
            $this->contract_end_date = $endDate->format('Y-m-d');
        } else {
            $this->contract_end_date = null;
        }
    }

    public function sendContract($id)
    {
        $contract = Contract::find($id);

        // Fetch client email
        $client = $contract->client;
        $clientEmail = $client->user->email;

        // Generate PDF from Blade view
        $pdf = Pdf::loadView('pdf.contract', compact('contract'));

        // Prepare contract link
        $contractLink = route('contract-response', ['contract_number' => $contract->contract_number]);

        // Send email with PDF attachment
        Mail::send('mails.contract_notification', ['contractLink' => $contractLink], function ($message) use ($clientEmail, $pdf, $contract) {
            $message->to($clientEmail)
                ->subject("Nuovo contratto: {$contract->contract_number}")
                ->attachData($pdf->output(), "Contratto_{$contract->contract_number}.pdf");
        });

        $this->dispatch('notice', type: 'success', text: 'Contract sent to the client successfully!');
    }

    public function sendContractSMS($id)
    {
        $contract = Contract::find($id);

        // Fetch client email
        $client = $contract->client;

        // Generate PDF from Blade view
        $pdf = Pdf::loadView('pdf.contract', compact('contract'));


        if ($client?->phone_number) {
            try {
                // Authenticate User
                $auth = authenticateUser(env('ARUBA_SMS_USERNAME'), env('ARUBA_SMS_PASSWORD'));

                if ($auth) {
                    // Send SMS
                    $recipients = [$client?->phone_number]; // List of recipients
                    $sender = 'AllTech'; // Optional sender name

                    $contractLink = route('contract-response', ['contract_number' => $contract->contract_number]);

                    $message = "MeMove: Hai un nuovo contratto. Accetta o rifiuta qui: " . $contractLink;


                    $scheduleTime = gmdate('YmdHis', strtotime("+5 minutes")); // Ensure correct format


                    $smsResponse = sendSMS($auth, $message, $recipients, $sender, $scheduleTime);

                    // Display Result
                    if ($smsResponse && isset($smsResponse["result"]) && $smsResponse["result"] === "OK") {
                        $this->dispatch('notice', type: 'success', text: __('messages.contract_sms_sent'));
                    } else {
                        \Log::info('Create Contract: Failed to send SMS.');
                        $this->dispatch('notice', type: 'error', text: "Contract SMS not sent");
                    }
                } else {
                    \Log::info('Create Contract: Authentication failed. Please check your credentials.');
                    $this->dispatch('notice', type: 'error', text: "Contract SMS not sent");
                }
            } catch (\Exception $e) {
                \Log::info('Create Contract: ' . $e->getMessage());
                $this->dispatch('notice', type: 'error', text: "Contract SMS not sent" . $e->getMessage());
            }
        }
    }

    public function renewContract($id)
    {
        $contract = Contract::findOrFail($id);

        $this->old_contract = $contract->id;

        $this->deviceVerifyData = $contract->device;

        $this->verification_client = $contract->client_id;
        $this->verification_dealer_id = $contract->dealer_id;
        $this->verification_vehicle_type = $contract->verification_vehicle_type;
        $this->service_details = $contract->service_details;
        $this->vehicle_brand = $contract->vehicle_brand;
        $this->vehicle_model = $contract->vehicle_model;
        $this->vehicle_color = $contract->vehicle_color;
        $this->vehicle_registration_number = $contract->vehicle_registration_number;
        $this->vehicle_chassis = $contract->vehicle_chassis;
        $this->vehicle_km = $contract->vehicle_km;
        $this->starter_motor_block = $contract->starter_motor_block;
        $this->vehicle_number_plate = $contract->vehicle_number_plate;
        $this->vehicle_registration_date = $contract->vehicle_registration_date;
        $this->contract_duration = $contract->duration;
        $this->contract_start_date = now()->format('Y-m-d');
        $this->calculateContractEndDate();
        $this->phone_number = $contract->phone_number;
        $this->frame = $contract->frame;
        $this->old_installation_image = $contract->installation_image;

        $this->dispatch('valueUpdated', option: $contract->client_id, type: 'getClients');
        $this->dispatch('open-modal', name: 'verify-device-modal');
    }

    public function verifyDevice()
    {
        $contract = Contract::findOrFail($this->old_contract);

        $contract->status = 'expired';

        $contract->save();

        if ($this->inactive_client == 1) {
            if ($contract->client) {
                $contract->client()?->user()?->update([
                    'is_active' => 0
                ]);
            }
        }

        if ($this->deviceVerifyData) {
            $this->validate([
                'deviceVerifyData.id' => 'required|exists:devices,id',
                'verification_client' => 'required|exists:clients,id',
                'verification_vehicle_type' => 'required|max:255',
                'vehicle_number_plate' => 'required|max:255',
                'contract_duration' => 'required|max:255',
                'contract_start_date' => 'required|max:255',
                'contract_end_date' => 'required|max:255',
                'vehicle_brand' => 'required|max:255',
                'vehicle_model' => 'required|max:255',
                'vehicle_color' => 'required|max:255',
                'vehicle_registration_date' => 'required|max:255',
                'vehicle_km' => 'required|max:255',
                'starter_motor_block' => 'required|max:255',
            ]);

            // Generate unique contract number
            $contractNumber = $this->generateUniqueContractNumber();

            $uploadedFile = null;
            if ($this->installation_image) {
                $uploadedFile = $this->installation_image->store('images', 'public');
            } else {
                $uploadedFile = $this->old_installation_image;
            }
            // Insert data into Contract
            $deviceContractData = Contract::create([
                'device_id' => $this->deviceVerifyData->id,
                'client_id' => $this->verification_client,
                'dealer_id' => $this->verification_dealer_id ?? null,
                'duration' => $this->contract_duration,
                'start_date' => $this->contract_start_date,
                'end_date' => $this->contract_end_date,
                'verification_vehicle_type' => $this->verification_vehicle_type,
                'vehicle_brand' => $this->vehicle_brand,
                'vehicle_model' => $this->vehicle_model,
                'vehicle_color' => $this->vehicle_color,
                'vehicle_registration_date' => $this->vehicle_registration_date,
                'vehicle_number_plate' => $this->vehicle_number_plate,
                'frame' => $this->frame,
                'vehicle_km' => $this->vehicle_km,
                'starter_motor_block' => $this->starter_motor_block,
                'status' => 'pending',
                'contract_number' => $contractNumber,
                'service_details' => $this->service_details,
                'installation_image' => $uploadedFile,
            ]);



            // Device::find($this->deviceVerifyData->id)->update(['client_id' => $this->verification_client, 'is_verified' => 1]);

            if ($deviceContractData?->client?->phone_number) {
                try {
                    // Authenticate User
                    $auth = authenticateUser(env('ARUBA_SMS_USERNAME'), env('ARUBA_SMS_PASSWORD'));

                    if ($auth) {
                        // Send SMS
                        $recipients = [$deviceContractData?->client?->phone_number]; // List of recipients
                        $sender = 'AllTech'; // Optional sender name

                        $contractLink = route('contract-response', ['contract_number' => $deviceContractData->contract_number]);

                        $message = "MeMove: Hai un nuovo contratto. Accetta o rifiuta qui: " . $contractLink;


                        $scheduleTime = gmdate('YmdHis', strtotime("+5 minutes")); // Ensure correct format


                        $smsResponse = sendSMS($auth, $message, $recipients, $sender, $scheduleTime);

                        // Display Result
                        if ($smsResponse && isset($smsResponse["result"]) && $smsResponse["result"] === "OK") {
                        } else {
                            \Log::info('Create Contract: Failed to send SMS.');
                        }
                    } else {
                        \Log::info('Create Contract: Authentication failed. Please check your credentials.');
                    }
                } catch (\Exception $e) {
                    \Log::info('Create Contract: ' . $e->getMessage());
                }
            }

            // Generate and send contract PDF
            $this->sendContractEmail($deviceContractData);


            // Close modal and show success message
            $this->dispatch('close-modal');
            $this->dispatch('notice', type: 'success', text: __('messages.device_inspection_completed'));
        } else {
            $this->dispatch('notice', type: 'error', text: __('messages.device_not_found'));
        }
    }

    private function generateUniqueContractNumber()
    {
        do {
            $contractNumber = strtoupper(Str::random(12)); // Generate a random 12-character uppercase alphanumeric string
        } while (Contract::where('contract_number', $contractNumber)->exists());

        return $contractNumber;
    }

    private function sendContractEmail($contract)
    {
        // Fetch client email
        $client = Client::find($this->verification_client);
        $clientEmail = $client->user->email;

        // Generate PDF from Blade view
        $pdf = Pdf::loadView('pdf.contract', compact('contract'))->setOption('defaultFont', 'Roboto');

        // Prepare contract link
        $contractLink = route('contract-response', ['contract_number' => $contract->contract_number]);

        // Send email with PDF attachment
        Mail::send('mails.contract_notification', ['contractLink' => $contractLink], function ($message) use ($clientEmail, $pdf, $contract) {
            $message->to($clientEmail)
                ->subject("Nuovo contratto: {$contract->contract_number}")
                ->attachData($pdf->output(), "Contratto_{$contract->contract_number}.pdf");
        });
    }

    #[On('verification_clientUpdated')]
    public function verification_clientUpdated($option = null)
    {
        $this->verification_client = $option;
    }

    public function removeImage()
    {
        $this->old_installation_image = null;
    }

    public function addUpdateClient()
    {
        $this->validate([
            'name' => ['required', 'max:255'],
            'username' => ['required', 'string', 'max:255', 'unique:users,username'],
            'email' => ['required', 'email', 'max:255', 'unique:users,email'],
            'password' => ['required', 'min:6'],
            'address' => ['nullable', 'string', 'max:255'],
            'company' => ['nullable', 'string', 'max:255'],
            'municipality' => ['nullable', 'string', 'max:255'],
            'zip_code' => ['nullable', 'string', 'max:15'],
            'province' => ['nullable', 'string', 'max:255'],
            'tax_code' => ['nullable', 'string', 'max:100'],
            'type' => ['required', 'string', 'max:100'],
            'phone_number' => ['required', 'string', 'max:100'],
        ]);


        $passkey = $this->generateUniquePasskey();


        // Create new client
        $user = User::create([
            'name' => $this->name,
            'username' => $this->username,
            'email' => $this->email,
            'password' => Hash::make($this->password), // Password hashing for new user
            'role' => 'client',
            'is_active' => $this->is_active ? true : false,
        ]);

        Client::create([
            'user_id' => $user->id,
            'company' => $this->company,
            'address' => $this->address,
            'municipality' => $this->municipality,
            'zip_code' => $this->zip_code,
            'province' => $this->province,
            'tax_code' => $this->tax_code,
            'type' => $this->type,
            'last_name' => $this->last_name,
            'phone_number' => $this->phone_number,
            'dealer_id' => $this->verification_dealer_id,
            'passkey' => $passkey,
        ]);

        $this->dispatch('notice', type: 'error', text: __('messages.client_created'));

        $this->dispatch('close-modal');
        $this->dispatch('open-modal', name: 'verify-device-modal');

        $this->reset(
            'company',
            'name',
            'username',
            'password',
            'address',
            'municipality',
            'zip_code',
            'province',
            'tax_code',
            'phone_number',
            'type',
            'last_name',
            'is_active',
        );
    }

    public function generateUniquePasskey()
    {
        do {
            // Generate a random 16-character passkey
            $passkey = Str::random(10);
        } while (\App\Models\Client::where('passkey', $passkey)->exists());

        return $passkey;
    }


    public function render()
    {
        $contracts = Contract::with(['device:id,imei,model,number_plate', 'dealer:id,user_id,last_name', 'dealer.user:id,name', 'client:id,user_id,last_name', 'client.user:id,name']) // Include any necessary relations
            ->when(auth()->user()->role == 'dealer', function ($query) {
                $query->whereNotNull('dealer_id')->where('dealer_id', auth()->user()->dealer->id ?? 0);
            })
            ->when(auth()->user()->role == 'client', function ($query) {
                $query->whereNotNull('dealer_id')->where('client_id', auth()->user()->client->id ?? 0);
            })
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('duration', 'like', '%' . $this->search . '%')
                        ->orWhereHas('device', function ($q) {
                            $q->where('imei', 'like', '%' . $this->search . '%');
                        })
                        ->orWhereHas('dealer.user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('username', 'like', '%' . $this->search . '%');
                        })
                        ->orWhereHas('client.user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('username', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->when($this->status, function ($query) {
                $query->where(function ($query) {
                    $query->where('status', $this->status);
                });
            })
            ->latest()
            ->paginate(10);



        return view('livewire.panel.contracts', compact('contracts'));
    }
}
