<?php

namespace App\Livewire\Panel;

use App\Mail\PasswordResetMail;
use App\Models\PasswordResetToken;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Str;


class Admins extends Component
{
    use WithPagination;


    public $name, $username, $email, $password, $role, $is_active = true;

    public $recordId, $deleteRecordId, $search, $status;


    public function editRecord($recordId)
    {
        $this->recordId = $recordId;

        if ($recordId) {
            $this->recordId = $recordId;
            $admin = User::find($this->recordId);

            if ($admin) {
                // Set existing admin data when editing
                $this->name = $admin->name;
                $this->username = $admin->username ?? '';
                $this->email = $admin->email ?? '';
                $this->is_active = $admin->is_active ? true : false;
                $this->role = $admin->role;

                $this->dispatch('open-modal', name: 'add-admin-modal');
            }
        }
    }

    // Add or Update Admin
    public function addUpdateAdmin()
    {
        $this->validate();

        if ($this->recordId) {
            // Update existing admin
            $admin = User::find($this->recordId);
            if ($admin) {
                $admin->update([
                    'name' => $this->name,
                    'username' => $this->username,
                    'email' => $this->email,
                    'role' => $this->role,
                    'password' => $this->password ? Hash::make($this->password) : $admin->password,
                    'is_active' => $this->is_active ? true : false
                ]);
            }
        } else {
            // Create new admin
            User::create([
                'name' => $this->name,
                'username' => $this->username,
                'email' => $this->email,
                'role' => $this->role,
                'password' => Hash::make($this->password), // Password hashing for new user
                'role' => 'admin',
                'is_active' => $this->is_active ? true : false
            ]);
        }

        $this->dispatch('notice', type: 'error', text: $this->recordId ? __('messages.admin_updated') : __('messages.admin_created'));


        $this->dispatch('close-modal');

        $this->reset();
    }

    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            $admin = User::find($this->deleteRecordId);

            $admin->delete();



            $this->dispatch('notice', type: 'error', text: __('messages.admin_deleted'));
            $this->dispatch('close-modal');
        } else {
            $this->dispatch('notice', type: 'error', text: 'Admin not found!');
        }
    }

    public function clearRecords()
    {
        $this->reset();
    }


    // Validation rules
    public function rules()
    {
        return [
            'name' => ['nullable', 'string', 'max:255'],
            'username' => ['required', 'string', 'max:255', 'unique:users,username,' . $this->recordId],
            'email' => ['required', 'email', 'max:255', 'unique:users,email,' . $this->recordId],
            'password' => $this->recordId ? ['nullable', 'min:6'] : ['required', 'min:6'],
            'role' => ['required', 'max:255']
        ];
    }

    public function sendPasswordResetLink($email)
    {
        // Check if the email belongs to a registered user
        $user = User::where('email', $email)->exists();

        if (!$user) {
            $this->dispatch('notice', type: 'error', text: __('messages.no_account_found'));

            return;
        }

        // Generate token and store it in the database
        $token = Str::random(60);
        PasswordResetToken::updateOrCreate(
            ['email' => $email],
            ['token' => $token, 'created_at' => Carbon::now()]
        );

        // Generate the reset URL
        $resetUrl = route('password.reset', ['token' => $token, 'email' => $email]);


        Mail::to($email)->send(new PasswordResetMail($email, $resetUrl));


        $this->dispatch('notice', type: 'success', text: __('messages.password_reset_sent'));
    }



    public function render()
    {
        $admins = User::whereIn('role', ['admin', 'technician', 'operator', 'warehouse_operator']) // Include any necessary relations
            ->when(isset($this->search), function ($query) {
                $query->where(function ($query) {
                    $query->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('username', 'like', '%' . $this->search . '%')
                        ->orWhere('email', 'like', '%' . $this->search . '%')
                        ->orWhere('role', 'like', '%' . $this->search . '%');
                });
            })
            ->when(isset($this->status), function ($query) {
                $query->where('is_active', $this->status);
            })
            ->latest()
            ->paginate(10);


        return view('livewire.panel.admins', compact('admins'));
    }
}
