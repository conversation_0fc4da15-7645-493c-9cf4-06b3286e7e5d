<?php

namespace App\Livewire\Panel;

use App\Models\Event;
use Livewire\Component;

class Notifications extends Component
{
    public $filter;

    public function filterNotifications($filter = null)
    {
        $this->filter = $filter;
    }

    public function render()
    {
        if (auth()->check()) {
            $notifications = Event::when(auth()->user()->role === 'dealer', function ($query) {
                $query->whereIn('imei', auth()->user()->dealer->devices()->pluck('imei'));
            })->when(auth()->user()->role === 'client', function ($query) {
                $query->whereIn('imei', auth()->user()->client->devices()->pluck('imei'));
            })->when($this->filter, function ($query) {
                if ($this->filter === 'today') {
                    $query->whereDate('created_at', now()->toDateString());
                } elseif ($this->filter === 'yesterday') {
                    $query->whereDate('created_at', now()->subDay()->toDateString());
                }
            })->latest()->limit(8)->get();
        } else {
            $notifications = [];
        }
        return view('livewire.panel.notifications', compact('notifications'));
    }
}
