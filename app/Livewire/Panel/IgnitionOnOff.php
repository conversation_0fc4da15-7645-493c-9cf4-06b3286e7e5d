<?php

namespace App\Livewire\Panel;

use App\Models\Command;
use Illuminate\Support\Facades\File;
use Livewire\Attributes\On;
use Livewire\Component;

class IgnitionOnOff extends Component
{
    #[On('ignition-on')]
    public function ignitionOn($imei = null)
    {
        if ($imei) {
            $command = 'setdigout 1';
            $name = 'Turn ON ignition';
            // Check if the command already exists in the queue for the given IMEI
            $existingCommand = Command::where('imei', $imei)
                ->where('command', $command)
                ->where('added_in_queue', 1)
                ->whereNull('response')
                ->exists();

            if ($existingCommand) {
                $this->dispatch('notice', type: 'error', text: __('messages.command_already_in_queue'));
                return;
            }

            // Save command to the database
            Command::create([
                'imei' => $imei,
                'command' => $command,
                'name' => $name,
            ]);

            // Update the command queue JSON file
            $this->updateCommandQueue();

            $this->dispatch('notice', type: 'success', text: __('messages.block_motor_command_sent'));
            $this->dispatch('close-modal');
        } else {
            $this->dispatch('notice', type: 'error', text: __('messages.imei_not_received'));
        }
    }

    #[On('ignition-off')]
    public function ignitionOff($imei = null)
    {
        if ($imei) {
            $command = 'setdigout 0';
            $name = 'Turn OFF ignition';
            // Check if the command already exists in the queue for the given IMEI
            $existingCommand = Command::where('imei', $imei)
                ->where('command', $command)
                ->where('added_in_queue', 1)
                ->whereNull('response')
                ->exists();

            if ($existingCommand) {
                $this->dispatch('notice', type: 'error', text: __('messages.command_already_in_queue'));
                return;
            }

            // Save command to the database
            Command::create([
                'imei' => $imei,
                'command' => $command,
                'name' => $name,
            ]);

            // Update the command queue JSON file
            $this->updateCommandQueue();

            $this->dispatch('notice', type: 'success', text: __('messages.unblock_motor_command_sent'));
            $this->dispatch('close-modal');
        } else {
            $this->dispatch('notice', type: 'error', text: __('messages.imei_not_received'));
        }
    }



    private function updateCommandQueue()
    {
        // Fetch commands that should be added to the queue
        $commands = Command::whereNull('response')
            ->where('added_in_queue', '!=', 2) // Exclude commands marked as deleted
            ->get();

        $queue = [];
        foreach ($commands as $cmd) {
            $queue[$cmd->imei][] = [
                'command' => $cmd->command,
            ];
        }

        $path = public_path('command/queue.json');
        File::ensureDirectoryExists(dirname($path));
        File::put($path, json_encode($queue, JSON_PRETTY_PRINT));

        // Mark commands as added to queue (only those not already deleted)
        Command::whereNull('response')
            ->where('added_in_queue', 0)
            ->update(['added_in_queue' => 1]);
    }

    public function render()
    {
        return view('livewire.panel.ignition-on-off');
    }
}
