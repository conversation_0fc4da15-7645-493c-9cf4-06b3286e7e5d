<?php

namespace App\Livewire\Panel;

use App\Exports\DeviceHistoryExport;
use App\Models\Device;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\File;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class History extends Component
{
    use WithPagination;


    public $deviceImei;
    public $initial_odometer;

    public $date;
    public $endDate;
    public $plate;
    public $deviceHistory = [];
    public $availableDates = [];
    public $perPage = 10; // Number of items per page
    public $currentPage = 1; // Tracks the current page

    public $paginatedHistory;
    public $vehicle_type;


    public function mount($deviceImei)
    {

        $this->deviceImei = $deviceImei;
        // Check if the logged-in user is a dealer
        if (auth()->user()->role === 'dealer') {
            $dealerId = auth()->user()->dealer->id;

            // Check if the IMEI belongs to the current dealer
            $device = Device::where('imei', $this->deviceImei)
                ->where('dealer_id', $dealerId)
                ->first();

            $this->plate = $device->number_plate ?? 'N/A';
            $this->vehicle_type = $device->vehicle_type ?? 'default';



            // If the device doesn't exist or isn't associated with the dealer, abort with a 404
            if (!$device) {
                abort(404);
            }
        } else {
            // Check if the IMEI belongs to the current dealer
            $device = Device::where('imei', $this->deviceImei)
                ->first();

            $this->plate = $device->number_plate ?? 'N/A';
            $this->vehicle_type = $device->vehicle_type ?? 'default';
        }

        // Check if the logged-in user is a client
        if (auth()->user()->role === 'client') {
            $clientId = auth()->user()->client->id;

            // Check if the IMEI belongs to the current client
            $device = Device::where('imei', $this->deviceImei)
                ->where('client_id', $clientId)
                ->first();

            $this->plate = $device->number_plate ?? 'N/A';
            $this->vehicle_type = $device->vehicle_type ?? 'default';


            // If the device doesn't exist or isn't associated with the dealer, abort with a 404
            if (!$device) {
                abort(404);
            }
        } else {
            // Check if the IMEI belongs to the current dealer
            $device = Device::where('imei', $this->deviceImei)
                ->first();

            $this->plate = $device->number_plate ?? 'N/A';
            $this->vehicle_type = $device->vehicle_type ?? 'default';
        }

        $this->initial_odometer = $device->initial_odometer ?? 0;


        // $this->fetchAvailableDates();
        // $this->fetchDeviceHistory();
    }


    public function exportExcel()
    {
        if (empty($this->paginatedHistory) && empty($this->paginatedHistory['data'])) {
            return false;
        }

        return Excel::download(
            new DeviceHistoryExport(
                $this->paginatedHistory['data'], // Pass the history data to the export class
                $this->initial_odometer
            ),
            $this->deviceImei . '_history.xlsx'
        );
    }

    public function exportPDF()
    {
        // Ensure deviceHistory contains valid data
        if (empty($this->paginatedHistory) || empty($this->paginatedHistory['data'])) {
            return false;
        }

        // Step 1: Calculate total distance covered
        $deviceHistoryData = $this->paginatedHistory['data'];

        // Extract odometer values from the first and last records
        $firstOdometer = $deviceHistoryData[0]['16'] ?? null; // First odometer value
        $lastOdometer = end($deviceHistoryData)['16'] ?? null; // Last odometer value

        // Ensure odometer values are valid and calculate total distance
        $totalDistanceMeters = ($lastOdometer !== null && $firstOdometer !== null)
            ? max(0, $lastOdometer - $firstOdometer) // Prevent negative distance
            : 0;

        // Convert to kilometers
        $totalDistanceKm = $totalDistanceMeters / 1000;

        // Step 2: Process the device history to prepare for the PDF
        $processedHistory = array_map(function ($deviceHistory) {
            $latitude = $deviceHistory['latitude'] ?? null;
            $longitude = $deviceHistory['longitude'] ?? null;

            // Fetch street name using Nominatim API
            $streetName = 'N/A';
            if ($latitude && $longitude) {
                // Nominatim API URL
                $url = "https://nominatim.openstreetmap.org/reverse?format=json&lat={$latitude}&lon={$longitude}";

                // Set User-Agent
                $options = [
                    "http" => [
                        "header" => "User-Agent: MeMove/1.0 (<EMAIL>)\r\n"
                    ]
                ];

                // Create stream context
                $context = stream_context_create($options);

                // Fetch the data
                $response = @file_get_contents($url, false, $context);

                if ($response) {
                    $json = json_decode($response, true);
                    $streetName = $json['address']['road'] ?? 'N/A';
                }
            }


            return [
                'timestamp' => $deviceHistory['last_update'] ?? 'N/A',
                'movement' => $deviceHistory['240'] ? 'Moving' : 'Stopped',
                'ignition' => $deviceHistory['239'] ? 'On' : 'Off',
                'speed' => $deviceHistory['speed'] ?? 'N/A',
                'odometer' => $deviceHistory['16'] ?? 'N/A',
                'street_name' => $streetName
            ];
        }, $deviceHistoryData);

        // Step 3: Load the processed data into the PDF
        $pdf = Pdf::loadView('pdf.device-history', [
            'device_history' => $processedHistory,
            'plate' => $this->plate,
            'total_distance' => number_format($totalDistanceKm, 2) . ' km' // Pass total distance to PDF
        ]);

        // Stream the PDF as a downloadable file
        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, $this->deviceImei . '_history.pdf');
    }



    /**
     * Fetch device history for the selected IMEI and date.
     */
    public function fetchDeviceHistory()
    {
        $filePath = public_path("data/history/{$this->deviceImei}/{$this->date}.json");

        if (File::exists($filePath)) {
            $jsonData = json_decode(File::get($filePath), true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->deviceHistory = []; // Handle invalid JSON gracefully
                $this->paginatedHistory = [];
                return;
            }
            $this->deviceHistory = $jsonData;

            $this->paginateHistory();
        } else {
            $this->deviceHistory = []; // Clear history if file doesn't exist

            $this->paginatedHistory = [];
        }
    }

    /**
     * Paginate the fetched device history.
     */
    public function paginateHistory()
    {
        $items = collect($this->deviceHistory);
        $paginated = new LengthAwarePaginator(
            $items->forPage($this->currentPage, $this->perPage), // Items for the current page
            $items->count(), // Total items
            $this->perPage, // Items per page
            $this->currentPage, // Current page
            ['path' => request()->url(), 'query' => request()->query()] // Pagination URL
        );

        // Store only essential data in Livewire-friendly format
        $this->paginatedHistory = [
            'data' => $paginated->items(),
            'current_page' => $paginated->currentPage(),
            'last_page' => $paginated->lastPage(),
            'per_page' => $paginated->perPage(),
            'total' => $paginated->total(),
        ];
    }


    public function nextPage()
    {
        if ($this->currentPage < $this->paginatedHistory['last_page']) {
            $this->currentPage++;
            $this->paginateHistory();
        }
    }

    public function previousPage()
    {
        if ($this->currentPage > 1) {
            $this->currentPage--;
            $this->paginateHistory();
        }
    }



    /**
     * Fetch available dates from dates.json.
     */
    public function fetchAvailableDates()
    {
        $filePath = public_path("data/history/{$this->deviceImei}/dates.json");

        if (File::exists($filePath)) {
            $this->availableDates = json_decode(File::get($filePath), true);
        } else {
            $this->availableDates = []; // Clear dates if file doesn't exist
        }
    }

    /**
     * Increment the date to the next available date.
     */
    public function incrementDate()
    {
        $currentIndex = array_search($this->date, $this->availableDates);

        if ($currentIndex !== false && $currentIndex < count($this->availableDates) - 1) {
            $this->date = $this->availableDates[$currentIndex + 1];
            $this->fetchDeviceHistory();
        }
    }

    /**
     * Decrement the date to the previous available date.
     */
    public function decrementDate()
    {
        $currentIndex = array_search($this->date, $this->availableDates);

        if ($currentIndex !== false && $currentIndex > 0) {
            $this->date = $this->availableDates[$currentIndex - 1];
            $this->fetchDeviceHistory();
        }
    }

    /**
     * Update history when the date is manually selected.
     *
     * @param string $selectedDate
     */
    public function updateDate($selectedDate)
    {
        if (in_array($selectedDate, $this->availableDates)) {
            $this->date = $selectedDate;
            $this->fetchDeviceHistory();
        }
    }


    public function render()
    {

        return view('livewire.panel.history');
    }
}
