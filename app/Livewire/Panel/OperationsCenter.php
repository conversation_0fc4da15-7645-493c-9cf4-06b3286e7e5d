<?php

namespace App\Livewire\Panel;

use App\Exports\DeviceLogsExport;
use App\Models\Contract;
use App\Models\Device;
use App\Models\DeviceLog;
use Barryvdh\DomPDF\Facade\Pdf;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class OperationsCenter extends Component
{

    use WithPagination;


    public $device, $operator_id, $log;

    public $recordId, $deleteRecordId, $search;
    public $devices;
    public $alarmStatusChanged = false;
    public $startAlarm = false;
    public $selectedDevice;
    public $device_imei;
    public $status = 'open';

    public $selectedOption;
    public $free_notes;
    public $deviceContractInfo;

    public $showArchived;

    public function mount()
    {
        $this->devices = Device::latest()
            ->with('client') // Ensure client relation is loaded
            ->get(['id', 'imei', 'number_plate', 'client_id'])
            ->mapWithKeys(function ($device) {
                $clientName = optional($device->client?->user)->name; // Get client name safely
                $numberPlate = $device->number_plate ?? ''; // Default if null
                $imei = $device->imei;

                return [$device->id ?? null => "$imei - $numberPlate - $clientName"];
            })
            ->filter() // Remove any null keys
            ->all();

        $this->showArchived = session('showArchived', true);
    }

    public function addLog()
    {
        $this->resetExcept('showArchived', 'startAlarm', 'alarmStatusChanged', 'devices');
        $this->dispatch('open-modal', name: 'add-device-logs-modal');
    }


    public function editRecord($recordId)
    {
        $this->recordId = $recordId;

        if ($recordId) {
            $this->recordId = $recordId;
            $deviceLog = DeviceLog::find($this->recordId);

            if ($deviceLog) {
                // Set existing deviceLog data when editing
                $this->device_imei = $deviceLog->device->imei ?? '';
                $this->device = $deviceLog->device_id ?? '';
                $this->operator_id = $deviceLog->operator_id ?? '';
                $this->log = $deviceLog->log ?? '';
                $this->status = $deviceLog->status ?? '';

                if ($deviceLog->contacted_owner) {
                    $this->selectedOption = 'contacted_owner';
                } elseif ($deviceLog->contact_the_force_of_order) {
                    $this->selectedOption = 'contact_the_force_of_order';
                } elseif ($deviceLog->false_alaram) {
                    $this->selectedOption = 'false_alaram';
                } elseif ($deviceLog->other) {
                    $this->selectedOption = 'other';
                }
                $this->free_notes = $deviceLog->free_notes;

                $this->dispatch('open-modal', name: 'add-device-logs-modal');
            }
        }
    }

    // Add or Update Client
    public function addUpdateDeviceLog()
    {
        $this->validate([
            'log' => 'required',
        ]);

        if ($this->recordId) {

            $this->validate([
                'device' => 'required',
            ]);

            // Update existing client
            $deviceLog = DeviceLog::find($this->recordId);
            if ($deviceLog) {
                $deviceLog->device_id = $this->device;
                $deviceLog->log = $this->log;
                $deviceLog->status = $this->status;

                $deviceLog->contacted_owner = $this->selectedOption === 'contacted_owner';
                $deviceLog->contact_the_force_of_order = $this->selectedOption === 'contact_the_force_of_order';
                $deviceLog->false_alaram = $this->selectedOption === 'false_alaram';
                $deviceLog->other = $this->selectedOption === 'other';
                $deviceLog->free_notes = $this->free_notes;

                $deviceLog->save();
            }
        } else {
            $deviceId = Device::find($this->device_imei);
            if ($deviceId) {
                // Create new client
                $deviceLog = DeviceLog::create([
                    'device_id' => $deviceId->id,
                    'log' => $this->log,
                    'status' => $this->status,

                    'contacted_owner' => $this->selectedOption === 'contacted_owner',
                    'contact_the_force_of_order' => $this->selectedOption === 'contact_the_force_of_order',
                    'false_alaram' => $this->selectedOption === 'false_alaram',
                    'other' => $this->selectedOption === 'other',
                    'free_notes' => $this->free_notes,
                    'is_new' => 0

                ]);
            } else {
                $this->dispatch('notice', type: 'error', text: __('messages.device_not_found'));
                return;
            }
        }

        $this->dispatch('notice', type: 'error', text: $this->recordId ? __('messages.device_log_updated') : __('messages.device_log_created'));


        $this->dispatch('close-modal');

        $this->resetExcept('showArchived', 'startAlarm', 'alarmStatusChanged', 'devices');
    }

    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            DeviceLog::find($this->deleteRecordId)->delete();

            $this->dispatch('notice', type: 'error', text: __('messages.device_log_deleted'));
            $this->dispatch('close-modal');
        } else {
            $this->dispatch('notice', type: 'error', text: __('messages.client_not_found'));
        }
    }





    public function clearRecords()
    {
        $this->reset('device', 'log', 'recordId');
    }


    // Method to export as Excel
    public function exportExcel()
    {
        return Excel::download(new DeviceLogsExport($this->getFilteredDeviceLogs()), 'device-logs.xlsx');
    }

    // Method to export as PDF
    public function exportPDF()
    {
        $deviceLogs = $this->getFilteredDeviceLogs();


        $pdf = Pdf::loadView('pdf.deviceLogs', [
            'deviceLogs' => $deviceLogs->map(function ($deviceLog) {
                return array_map(fn($value) => mb_convert_encoding($value, 'UTF-8', 'UTF-8'), [
                    'device' => $deviceLog->device->imei ?? '',
                    'log' => $deviceLog->log,
                    'status' => $deviceLog->status,
                    'created_at' => $deviceLog->created_at->format('d/m/Y H:i'),
                ]);
            })
        ]);

        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, 'device-logs.pdf');



        // return $pdf->download('clients.pdf');
    }


    // Query to filter clients based on search
    public function getFilteredDeviceLogs()
    {
        return DeviceLog::when(isset($this->search), function ($query) {
            $query->where(function ($query) {
                $query->where('log', 'like', '%' . $this->search . '%')
                    ->orWhereHas('device', function ($q) {
                        $q->where('imei', 'like', '%' . $this->search . '%');
                    });
            });
        })
            ->with('device:id,imei')
            ->when(!$this->showArchived, function ($query) {
                $query->where('status', '!=', 'closed');
            })
            ->orderBy('is_new', 'desc')
            ->latest()->get();
    }

    public function stopAlaram()
    {
        DeviceLog::where('is_new', 1)->update(['is_new' => 0]);
        $this->startAlarm = false;
        $this->dispatch('stopAlaram');
    }

    public function showDevice($id)
    {
        $this->selectedDevice = DeviceLog::with('device', 'device.client', 'device.client.user')->find($id);

        if ($this->selectedDevice && $this->selectedDevice->device) {
            $this->deviceContractInfo = Contract::where('device_id', $this->selectedDevice->device->id)
                ->where('client_id', $this->selectedDevice->device->client_id)
                ->first();
        }

        $this->dispatch('open-modal', name: 'show-device-details');
        $this->dispatch('showGeofence', imei: $this->selectedDevice->device->imei ?? null);

        // Fetch live data from JSON
        $filePath = public_path('data/live/live_data.json');
        if (file_exists($filePath)) {
            $liveData = json_decode(file_get_contents($filePath), true);
            $imei = $this->selectedDevice->device->imei ?? null;

            // If IMEI exists in live data, pass it to the frontend
            if ($imei && isset($liveData[$imei])) {
                $deviceData = $liveData[$imei];
                $this->dispatch('showDeviceMarkers', [
                    'imei' => $imei ?? null,
                    'latitude' => $deviceData['latitude'] ?? null,
                    'longitude' => $deviceData['longitude'] ?? null,
                    'vehicle_type' => $this->selectedDevice->device->vehicle_type ?? 'default',
                    'ignition_status' => $deviceData['239'] ?? null, // Example key for ignition
                    'movement_status' => $deviceData['240'] ?? null  // Example key for movement
                ]);
            }
        }
    }

    public function updatedShowArchived()
    {
        if ($this->showArchived == true) {
            session()->put('showArchived', true);
        } else {
            session()->put('showArchived', false);
        }
    }


    public function render()
    {
        $deviceLogs = DeviceLog::when(isset($this->search), function ($query) {
            $query->where(function ($query) {
                $query->where('log', 'like', '%' . $this->search . '%')
                    ->orWhereHas('device', function ($q) {
                        $q->where('imei', 'like', '%' . $this->search . '%');
                    });
            });
        })
            ->with('device:id,imei')
            ->when(!$this->showArchived, function ($query) {
                $query->where('status', '!=', 'closed');
            })
            ->orderBy('is_new', 'desc')
            ->latest()
            ->paginate(10); // Fetch results and assign to $deviceLogs

        // Loop through paginated items, not the entire object
        foreach ($deviceLogs->items() as $deviceLog) {
            if ($deviceLog->is_new) {
                $this->startAlarm = true;
                $this->dispatch('start-alaram');
            }
        }


        return view('livewire.panel.operations-center', compact('deviceLogs'));
    }
}
