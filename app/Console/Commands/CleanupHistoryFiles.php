<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class CleanupHistoryFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:history';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete JSON history files older than 180 days';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $historyPath = public_path('data/history');
        $currentDate = now();

        // Check if the history directory exists
        if (!File::exists($historyPath)) {
            $this->info('No history directory found.');
            return;
        }

        // Iterate over each IMEI folder in the history directory
        foreach (File::directories($historyPath) as $imeiFolder) {
            $datesFilePath = $imeiFolder . '/dates.json';

            // Check if dates.json exists
            if (!File::exists($datesFilePath)) {
                $this->warn("No dates.json file found in $imeiFolder");
                continue;
            }

            // Read the dates.json file
            $dates = json_decode(File::get($datesFilePath), true);

            if (!is_array($dates)) {
                $this->warn("Invalid dates.json format in $imeiFolder");
                continue;
            }

            // Filter dates that are older than 180 days
            $filteredDates = [];
            foreach ($dates as $date) {
                $dateObject = Carbon::createFromFormat('d-m-Y', $date);

                if ($dateObject->diffInDays($currentDate) > 180) {
                    // Delete the corresponding JSON file
                    $jsonFilePath = $imeiFolder . '/' . $date . '.json';

                    if (File::exists($jsonFilePath)) {
                        File::delete($jsonFilePath);
                        $this->info("Deleted file: $jsonFilePath");
                    }
                } else {
                    $filteredDates[] = $date; // Keep this date
                }
            }

            // Update dates.json with remaining dates
            File::put($datesFilePath, json_encode($filteredDates));
            $this->info("Updated dates.json in $imeiFolder");
        }

        $this->info('History cleanup completed.');
    }
}
