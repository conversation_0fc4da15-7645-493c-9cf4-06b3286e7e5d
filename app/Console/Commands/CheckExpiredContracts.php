<?php

namespace App\Console\Commands;

use App\Models\Contract;
use App\Models\Notification;
use App\Services\FCMService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CheckExpiredContracts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contracts:check-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and update expired contracts, and send notifications if required';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get all active contracts
        $contracts = Contract::with(['client:id,user_id', 'client.user:id,name,is_active,contract_expiry,fcm_token', 'device:id,imei,number_plate'])->where('status', 'active')->get(['id', 'client_id', 'device_id', 'end_date', 'start_date', 'status']);

        $currentDate = now()->format('Y-m-d');

        foreach ($contracts as $contract) {
            // Check if the contract's end date is in the future
            if ($contract->end_date > $currentDate) {
                // Check if the contract is expiring in 5 days
                $daysBeforeExpiry = Carbon::parse($currentDate)->diffInDays($contract->end_date);

                // Send reminder notification 5 days before expiration
                if ($daysBeforeExpiry == 5) {
                    $client = $contract->client;
                    $user = $client?->user;

                    if ($user && $user->contract_expiry == 1) {
                        // Create reminder notification
                        $notificationTitle = 'Contract Expiry Reminder';
                        $notificationContent = "Your contract for vehicle {$contract->device?->number_plate} will expire in 5 days.";
                        $notificationType = 'Contract Expiry';
                        $imei = $contract->device?->imei;

                        Notification::create([
                            'title' => $notificationTitle,
                            'content' => $notificationContent,
                            'type' => $notificationType,
                            'imei' => $imei,
                            'user_id' => $user->id,
                        ]);

                        // Send FCM notification
                        $fcmToken = $user->fcm_token;
                        if ($fcmToken) {
                            FCMService::sendNotification($fcmToken, $notificationTitle, $notificationContent);
                        }
                    }
                }
            }
            // If the contract is already expired, process it as before
            elseif ($contract->end_date < $currentDate) {
                // Update contract status to expired
                $contract->status = 'expired';
                $contract->save();

                // Send expired contract notification
                $client = $contract->client;
                $user = $client?->user;

                if($user){
                    $user->is_active = 0;
                    $user->save();
                }
                if ($user && $user->contract_expiry == 1) {
                    // Create expiry notification
                    $notificationTitle = 'Contract Expiry';
                    $notificationContent = "Your contract has expired for vehicle {$contract->device?->number_plate} and device imei {$contract->device?->imei}";
                    $notificationType = 'Contract Expiry';
                    $imei = $contract->device?->imei;

                    Notification::create([
                        'title' => $notificationTitle,
                        'content' => $notificationContent,
                        'type' => $notificationType,
                        'imei' => $imei,
                        'user_id' => $user->id,
                    ]);

                    // Send FCM notification
                    $fcmToken = $user->fcm_token;
                    if ($fcmToken) {
                        FCMService::sendNotification($fcmToken, $notificationTitle, $notificationContent);
                    }
                }
            }
        }

        $this->info('Expired contracts checked and notifications sent.');
    }
}
