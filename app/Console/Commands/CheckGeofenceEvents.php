<?php

namespace App\Console\Commands;

use App\Models\Device;
use App\Models\DeviceLog;
use App\Models\Event;
use App\Models\Notification;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckGeofenceEvents extends Command
{
    protected $signature = 'check:geofence-events';
    protected $description = 'Check for pending geofence events older than 1 minute';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // $events = Notification::where('is_geofence_event', 1)
        //     ->where('response_status', ['pending', 'active'])
        //     ->where('created_at', '<=', Carbon::now()->subMinute())
        //     ->get();

        // foreach ($events as $event) {
        //     $event->response_status = 'expired';
        //     $event->save();

        //     $device = Device::where('imei', $event->imei)->first();

        //     $event = Event::where('imei', $event->imei)->where('event_message', 'like', '%Dispositivo%')->latest()->first();
        //     // Log the event and trigger automated call
        //     // $this->logAction($device->id, $event->event_message);

        //     if ($device->client?->phone_number) {
        //         $this->triggerAutomatedCall($device->client);
        //     }
        // }

        $this->info('Checked and updated geofence events.');
    }



    public function triggerAutomatedCall($customer)
    {
        // AMI connection settings
        $ipServer = "127.0.0.1"; // Asterisk server IP
        $port = "5038";
        $protocolServer = "tcp";
        $username = "memove"; // From manager.conf
        $password = "_-m4Q1vFOs"; // From manager.conf

        $ws = false; // Set to true if using WebSocket with Asterisk
        $context = 'default'; // Use the correct outbound context

        // Retrieve target phone number
        $target = $customer->phone_number;

        // Ensure international format (convert + to 00)
        if (substr($target, 0, 1) === "+") {
            $target = substr($target, 1);
        }

        // check if 39 country code is there remove it
        if (substr($target, 0, 2) === "39") {
            $target = substr($target, 2);
        }

        if ($ws) {
            $port .= "/ws";
        }

        // Connect to Asterisk AMI
        $socket = stream_socket_client("$protocolServer://$ipServer:$port");

        if ($target && $socket) {
            Log::info("Connected to Asterisk. Authenticating...");

            // Prepare authentication request
            $authenticationRequest = "Action: Login\r\n";
            $authenticationRequest .= "Username: $username\r\n";
            $authenticationRequest .= "Secret: $password\r\n";
            $authenticationRequest .= "Events: off\r\n\r\n";

            // Send authentication request
            $authenticate = stream_socket_sendto($socket, $authenticationRequest);
            if ($authenticate > 0) {
                usleep(200000); // Wait for response
                $authenticateResponse = fread($socket, 4096);

                if (strpos($authenticateResponse, 'Success') !== false) {
                    Log::info("Authenticated to Asterisk. Initiating call to $target...");


                    // Prepare originate request
                    $originateRequest = "Action: Originate\r\n";
                    $originateRequest .= "Channel: SIP/$target@convergenze\r\n";

                    $originateRequest .= "Callerid: 0899341047\r\n"; // Your VoIP number
                    $originateRequest .= "Exten: $target\r\n";
                    $originateRequest .= "Context: $context\r\n";
                    $originateRequest .= "Priority: 1\r\n"; // Ensure priority exists in dialplan
                    $originateRequest .= "Async: yes\r\n\r\n";

                    // Send originate request
                    $originate = stream_socket_sendto($socket, $originateRequest);
                    if ($originate > 0) {
                        usleep(200000);
                        $originateResponse = fread($socket, 4096);

                        if (strpos($originateResponse, 'Success') !== false) {
                            Log::info("Call initiated successfully to $target.");
                        } else {
                            Log::error("Failed to initiate call: " . $originateResponse);
                        }
                    } else {
                        Log::error("Could not write call initiation request to socket.");
                    }
                } else {
                    Log::error("Authentication to Asterisk Manager Interface failed.");
                }
            } else {
                Log::error("Could not send authentication request to Asterisk.");
            }
        } else {
            Log::error("Unable to connect to Asterisk socket.");
        }
    }

    public function logAction($deviceId, $action)
    {
        if ($deviceId) {
            DeviceLog::create([
                'device_id' => $deviceId,
                'log' => $action,
            ]);
        }
    }
}
