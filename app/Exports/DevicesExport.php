<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class DevicesExport implements FromCollection, WithHeadings
{

    public $data;
    public function __construct($data = null)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return $this->data->map(function ($dealer) {
            return [
                'imei' => $dealer->imei,
                'model' => $dealer->model ?? 'N/A',
                'iccid' => $dealer->iccid ?? 'N/A',
                'imsi' => $dealer->imsi ?? 'N/A',
                'vehicle_type' => $dealer->vehicle_type ?? 'N/A',
                'dealer' => $dealer->dealer->user->name ?? 'N/A',
                'client' => $dealer->client->user->name ?? 'N/A',
                'active' => $dealer->is_active ? 'Yes' : 'No',
                'tested' => $dealer->is_tested ? 'Yes' : 'No',
                'verified' => $dealer->is_verified ? 'Yes' : 'No',
            ];
        });
    }

    public function headings(): array
    {
        return [
            'IMEI',
            'Modello',
            'ICCID',
            'IMSI',
            'Tipo di veicolo',
            'Rivenditore',
            'Cliente',
            'Attivo',
            'Testato',
            'Verificato',
        ];
    }
}
