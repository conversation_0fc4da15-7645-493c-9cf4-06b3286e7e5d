<?php

namespace App\Exports;

use App\Models\Dealer;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class DealersExport implements FromCollection, WithHeadings
{
    protected $search;
    protected $status;

    public function __construct($search = null, $status = null)
    {
        $this->search = $search;
        $this->status = $status;
    }

    public function collection()
    {
        $dealers = Dealer::with(['user'])
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('address', 'like', '%' . $this->search . '%')
                        ->orWhereHas('user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('username', 'like', '%' . $this->search . '%')
                                ->orWhere('email', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->when($this->status, function ($query) {
                $query->where(function ($query) {
                    $query->whereHas('user', function ($q) {
                        $q->where('is_active', $this->status);
                    });
                });
            })
            ->get();

        return $dealers->map(function ($dealer) {
            return [
                'Dealer Name' => $dealer->user->name,
                'Username' => $dealer->user->username,
                'Email' => $dealer->user->email,
                'Company' => $dealer->user->name ?? null,
                'VAT' => $dealer->vat ?? null,
                'Address' => $dealer->address,
                'Municipality' => $dealer->municipality,
                'Zip Code' => $dealer->zip_code,
                'Province' => $dealer->province,
                'Tax Code' => $dealer->tax_code,
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Nome rivenditore',
            'Nome utente',
            'Email',
            'Cognome/Azienda',
            'Partita IVA',
            'Indirizzo',
            'Comune',
            'Codice postale',
            'Provincia',
            'Codice fiscale',
        ];
        
    }
}
