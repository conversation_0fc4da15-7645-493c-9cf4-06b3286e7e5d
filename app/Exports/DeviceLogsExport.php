<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class DeviceLogsExport implements FromCollection, WithHeadings
{
    public $data;
    public function __construct($data = null)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return $this->data->map(function ($devicelog) {
            return [
                'device' => $devicelog->device->imei,
                'log' => $devicelog->log,
                'status' => __('messages.'.$devicelog->status ?? 'unknown',[],'it'),
                'created_at' => $devicelog->created_at->format('d/m/Y H:i'),
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Dispositivo',
            'Registro',
            'Stato',
            'Creato il',
        ];
        
    }
}
