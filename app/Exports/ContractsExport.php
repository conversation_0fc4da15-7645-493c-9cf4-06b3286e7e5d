<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ContractsExport implements FromCollection, WithHeadings
{
    public $data;
    public function __construct($data = null)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return $this->data->map(function ($contract) {
            return [
                'device' => $contract->device->imei,
                'client' => $contract->client?->user?->name . ' ' . $contract->client?->last_name,
                'dealer' => $contract->dealer?->user?->name,
                'duration' => $contract->duration,
                'start_date' => $contract->start_date,
                'end_date' => $contract->end_date,
                'signed' => $contract->signed ? 'Signed' : 'UnSigned',
                'status' => ucfirst($contract->status),
                'created_at' => $contract->created_at->format('d/m/Y H:i'),
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Dispositivo',
            'Cliente',
            'Rivenditore',
            'Durata',
            'Data di inizio',
            'Data di fine',
            'Firmato',
            'Stato',
            'Creato il',
        ];
    }
}
