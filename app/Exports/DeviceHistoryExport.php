<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class DeviceHistoryExport implements FromCollection, WithHeadings
{

    protected $deviceHistory;
    protected $initial_odometer;

    public function __construct($deviceHistory, $initial_odometer = 0)
    {
        $this->deviceHistory = $deviceHistory;
        $this->initial_odometer = (double) $initial_odometer * 1000;
    }

    /**
     * Return the collection for export.
     *
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        // Process the device history data for Excel export
        $processedHistory = collect($this->deviceHistory)->map(function ($deviceHistory) {
            return [
                'Longitude' => $deviceHistory['longitude'] ?? 'N/A',
                'Latitude' => $deviceHistory['latitude'] ?? 'N/A',
                'Altitude' => $deviceHistory['altitude'] ?? 'N/A',
                'Angle' => $deviceHistory['angle'] ?? 'N/A',
                'Satellites' => $deviceHistory['satelites'] ?? 'N/A',
                'Speed' => $deviceHistory['speed'] ?? 'N/A',
                'Ignition' => $deviceHistory['239'] ? 'On' : 'Off',
                'Movement' => $deviceHistory['240'] ? 'Moving' : 'Stopped',
                'Odometer' => ($deviceHistory['16'] + $this->initial_odometer) / 1000 ?? 'N/A',
                'Timestamp' => $deviceHistory['last_update'] ?? 'N/A',
            ];
        });

        return $processedHistory;
    }

    /**
     * Return the headings for the Excel file.
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'Longitudine',
            'Latitudine',
            'Altitudine',
            'Angolo',
            'Satelliti',
            'Velocità',
            'Accensione',
            'Movimento',
            'Contachilometri',
            'Timestamp',
        ];
    }
}
