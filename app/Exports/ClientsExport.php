<?php

namespace App\Exports;

use App\Models\Client;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ClientsExport implements FromCollection, WithHeadings
{
    protected $search;
    protected $status;

    public function __construct($search = null, $status = null)
    {
        $this->search = $search;
        $this->status = $status;
    }

    public function collection()
    {
        $clients = Client::with(['user', 'dealer.user'])
            ->when(auth()->user()->role == 'dealer', function ($query) {
                $query->where('dealer_id', auth()->user()->dealer->id ?? null);
            })
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('address', 'like', '%' . $this->search . '%')
                        ->orWhere('company', 'like', '%' . $this->search . '%')
                        ->orWhereHas('user', function ($q) {
                            $q->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('username', 'like', '%' . $this->search . '%')
                                ->orWhere('email', 'like', '%' . $this->search . '%');
                        })
                        ->orWhereHas('dealer.user', function ($q) {
                            $q->where('username', 'like', '%' . $this->search . '%')
                                ->orWhere('name', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->when($this->status, function ($query) {
                $query->where(function ($query) {
                    $query->whereHas('user', function ($q) {
                        $q->where('is_active', $this->status);
                    });
                });
            })
            ->get();

        return $clients->map(function ($client) {
            return [
                'Client Name' => $client->user->name,
                'Last Name' => $client->last_name,
                'Username' => $client->user->username,
                'Company' => $client->user->name,
                'Email' => $client->user->email,
                'Phone Number' => $client->phone_number,
                'Dealer Name' => $client->dealer->user->name ?? null,
                'Address' => $client->address,
                'Municipality' => $client->municipality,
                'Zip Code' => $client->zip_code,
                'Province' => $client->province,
                'Tax Code' => $client->tax_code,
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Nome cliente',
            'Cognome cliente',
            'Nome utente',
            'Azienda',
            'Email',
            'Numero di telefono',
            'Nome rivenditore',
            'Indirizzo',
            'Comune',
            'Codice postale',
            'Provincia',
            'Codice fiscale',
        ];
    }
}
