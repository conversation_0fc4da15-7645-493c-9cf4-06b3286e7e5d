<?php

namespace App\Jobs;

use App\Models\Device;
use App\Models\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Models\DeviceLog;



class TriggerGeofenceCall implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $notificationId;
    protected $event;

    public function __construct($notificationId, $event)
    {
        $this->notificationId = $notificationId;
        $this->event = $event;
    }

    public function handle()
    {
        $event = Notification::find($this->notificationId);

        if ($event && $event->response_status === 'pending') {
            $event->response_status = 'expired';
            $event->save();

            $device = Device::where('imei', $event->imei)->first();

            if ($device && $device->client?->phone_number) {
                (new \App\Console\Commands\CheckGeofenceEvents)->triggerAutomatedCall($device->client);
                \Log::info("Automated call triggered for event ID: {$this->notificationId}");
            }

            DeviceLog::create([
                'device_id' => $device->id,
                'log' => $this->event?->event_message,
            ]);
        }
    }
}
