<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Devices List</title>
    <style>
        /* Global Styles */
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            color: #333;
            margin: 0;
            padding: 0;
        }

        /* Header */
        h1 {
            font-size: 16px;
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }

        /* Table Styles */
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th,
        td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
            font-size: 10px;
            font-weight: bold;
        }

        td {
            font-size: 9px;
        }

        /* Align long text and ensure readability */
        .text-left {
            text-align: left;
        }

        /* Page Margin */
        @page {
            margin: 20px;
        }
    </style>
</head>

<body>
    <h1>Dispositivi - MeMove</h1>
    <table>
        <thead>
            <tr>
                <th>IMEI</th>
                <th>Modello</th>
                <th>ICCID</th>
                <th>IMSI</th>
                <th>Tipo di <PERSON>ei<PERSON>lo</th>
                <th>Rivenditore</th>
                <th>Cliente</th>
                <th>Attivo</th>
                <th>Testato</th>
                <th>Verificato</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($devices as $device)
                <tr>
                    <td class="text-left">{{ $device->imei ?? 'N/A' }}</td>
                    <td class="text-left">{{ $device->model ?? 'N/A' }}</td>
                    <td class="text-left">{{ $device->iccid ?? 'N/A' }}</td>
                    <td class="text-left">{{ $device->imsi ?? 'N/A' }}</td>
                    <td class="text-left">{{ __('messages.' . $device->vehicle_type) ?? 'N/A' }}</td>
                    <td class="text-left">{{ $device->dealer->user->name ?? 'N/A' }}</td>
                    <td class="text-left">{{ $device->client->user->name ?? 'N/A' }}</td>
                    <td class="text-left">{{ $device->is_active ? 'Yes' : 'No' }}</td>
                    <td class="text-left">{{ $device->is_tested ? 'Yes' : 'No' }}</td>
                    <td class="text-left">{{ $device->is_verified ? 'Yes' : 'No' }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
</body>

</html>
