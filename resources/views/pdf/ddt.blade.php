<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document DDT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .header .company-info {
            text-align: right;
        }

        .title {
            text-align: center;
            font-weight: bold;
            margin: 20px 0;
        }

        .section {
            margin-top: 20px;
            border: 1px solid #000;
            padding: 10px;
        }

        .purple-box,
        .blue-box,
        .brown-box {
            background-color: #f8f8f8;
            margin: 10px 0;
        }

        .signature-boxes {
            display: flex;
            /* Arrange items in a row */
            justify-content: space-between;
            /* Space out the boxes evenly */
            align-items: center;
            /* Align items vertically in the center */
            gap: 20px;
            /* Space between boxes */
            margin-top: 20px;
            /* Margin from the top */
            width: 100%;
            /* Ensure the row spans the full container width */
            box-sizing: border-box;
            /* Include padding/margin in the width calculation */
        }

        .signature {
            flex: 0 0 45%;
            /* Ensure both boxes take 45% of the container's width */
            text-align: center;
            /* Center the text */
            border-top: 1px solid #000;
            /* Add the top border */
            padding-top: 10px;
            /* Add space between the border and text */
            white-space: nowrap;
            /* Prevent text wrapping */
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .info-table th,
        .info-table td {
            border: 1px solid #d5d5d5;
            padding: 10px;
            text-align: center;
        }

        .info-table th {
            background-color: #efefef;
            font-weight: bold;
        }

        .logo {
            display: inline;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="logo">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="MeMove" width="150">
            </div>
            <div class="company-info">
                <p>Alltechnology Srl<br>Via Ogliara, 13<br>84135 Salerno (SA) <br> PI 06225780656</p>
                <p><a href="mailto:<EMAIL>"><EMAIL></a><br>089 9341047</p>
            </div>
        </div>

        <!-- Document Title -->
        <div class="title">
            DOCUMENTO DI TRASPORTO No. {{ $ddtNumber ?? 'N/A' }} DEL {{ date('d-m-Y') ?? 'N/A' }}
        </div>
        <table class="info-table">
            <tr>
                <th>Destinatario</th>
                <th>Destinazione</th>
            </tr>
            <tr>
                <td>
                    @if ($dealer->type == 'company')
                        {{ $dealer->user->name ?? '' }}
                    @else
                        {{ $dealer->last_name ?? '' }} {{ $dealer->user->name ?? '' }}
                    @endif
                </td>
                <td>
                    {{ $dealer->address ?? '' }} {{ $dealer->zip_code ?? '' }}
                    {{ $dealer->municipality ?? '' }} {{ $dealer->province ?? '' }}
                </td>

            </tr>
        </table>

        <!-- Device Information -->
        <div class="section blue-box">
            <strong>Dispositivi da Spedire:</strong>
            <ul>
                @foreach ($devices as $device)
                    <li>IMEI: {{ $device->imei ?? 'N/A' }}</li>
                @endforeach
            </ul>
        </div>

        <!-- Device Count -->
        <div class="section brown-box">
            <strong>Totale Dispositivi: </strong> {{ count($devices) ?? 0 }}
        </div>

        <!-- Transport Details -->

        <table class="info-table">
            <tr>
                <th>INCARICATO DEL TRASPORTO</th>
                <th>CAUSALE DEL TRASPORTO</th>
            </tr>
            <tr>
                <td>{{ $incharge_of_transport }}</td>
                <td>{{ $transport_reason }}</td>
            </tr>
        </table>

        <!-- Additional Row -->

        <table class="info-table">
            <tr>
                @if ($delivery_terms)
                    <th>PORTO</th>
                @endif
                @if ($parcels)
                    <th>COLLI</th>
                @endif
                @if ($weight)
                    <th>PESO</th>
                @endif
            </tr>
            <tr>
                @if ($delivery_terms)
                    <td>{{ $delivery_terms }}</td>
                @endif
                @if ($parcels)
                    <td>{{ $parcels }}</td>
                @endif
                @if ($weight)
                    <td>{{ $weight }}</td>
                @endif
            </tr>
        </table>

        <table class="info-table">
            <tr>
                <th style="visibility: hidden; width: 30%;"></th>
                <th>Firma dell’Ufficiale di Trasporto</th>
                <th>Firma del Destinatario</th>
            </tr>
            <tr>
                <td style="visibility: hidden;">&nbsp;</td>
                <td>
                    @if ($transportOfficerSignaturePath)
                        <img src="{{ $transportOfficerSignaturePath }}" alt="Transport Officer Signature"
                            style="width: 100px; height: auto;">
                    @else
                        &nbsp;
                    @endif
                </td>
                <td>
                    @if ($recipientSignaturePath)
                        <img src="{{ $recipientSignaturePath }}" alt="Recipient Signature"
                            style="width: 100px; height: auto;">
                    @else
                        &nbsp;
                    @endif
                </td>
            </tr>
        </table>


    </div>
</body>

</html>
