<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Contract</title>

    <style>
        @font-face {
            font-family: 'Roboto';
            src: url('{{ storage_path('fonts/Roboto-Regular.ttf') }}') format('truetype');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'Roboto';
            src: url('{{ storage_path('fonts/Roboto-Bold.ttf') }}') format('truetype');
            font-weight: bold;
            font-style: normal;
        }

        body {
            font-family: 'Roboto', sans-serif;
        }

        .text-10px {
            font-size: 10px !important;
        }

        .w-1-4 {
            width: 25%;
        }
    </style>
</head>

<body class="leading-4 text-10px">

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">
        <h1 class="text-base font-bold text-center">
            CONTRATTO DI SERVIZIO "MEMOVE"
        </h1>

        <p class="text-sm font-medium text-center">
            GLOSSARIO
        </p>

        <div class="mt-5">
            <p>
                <span class="font-bold">Alltechnology Srl: </span>fornitore del servizio “ MeMove” e del Dispositivo
                Satellitare con sede legale in via
                Ogliara, 13 84135 Salerno (SA) – PEC
                <EMAIL> – P. Iva 06225780656.
            </p>

            <p class="mt-2">
                <span class="font-bold">Attivazione: </span>
                operazione compiuta da Alltechnology Srl, successivamente al collaudo, consistente nella registrazione
                dei dati personali
                contenuti nel modulo di adesione al servizio e associato al veicolo sul quale è installato il
                dispositivo satellitare e creazione dell’area riservata
                del cliente. Tale operazione garantisce l’avvio dei servizi di localizzazione satellitare successivi al
                collaudo. Al termine delle operazioni
                Alltechnology Srl invia una comunicazione via sms ed e-mail al Cliente per l’accettazione del contratto
                e successivamente un sms con le
                credenziali per l’accesso all’area riservata web ed app.
            </p>

            <p class="mt-2">
                <span class="font-bold">Canone: </span> corrispettivo versato da parte del Cliente per la fruizione dei
                servizi di localizzazione ed assistenza di Alltechnology Srl. Il Canone
                viene corrisposto dal Cliente anticipatamente per l’intera durata del periodo di erogazione dei servizi.
            </p>

            <p class="mt-2">
                <span class="font-bold"> Certificato di installazione e Collaudo: </span>
                documento con il quale l'installatore Autorizzato certifica di aver eseguito correttamente
                l’installazione
                del dispositivo sul Veicolo ed il Collaudo Tecnico e Funzionale. Il certificato di installazione ed il
                certificato di collaudo viene inviato da
                Alltechnology Srl al Cliente mediante e-mail e/o sms.
            </p>

            <p class="mt-2">
                <span class="font-bold"> Centrale Operativa di Sicurezza:</span>
                centrale di tele vigilanza per l'erogazione dei Servizi di Assistenza e
                Sicurezza, attiva 24 ore su 24; il
                Servizio è erogato dalla società̀ Selpol Srl, in persona del legale rappresentante p.t., P.IVA
                05519030653, con sede legale in via Della Libertà,
                102 84086 - Roccapiemonte (SA) - PEC <EMAIL>
            </p>
            <p class="mt-2">
                <span class="font-bold">Cliente:</span>
                la persona fisica o giuridica, proprietaria o locataria del Veicolo, che sottoscrive il Contratto per la
                fornitura del Servizio MeMove.
            </p>
            <p class="mt-2">
                <span>Collaudo:</span> operazione compiuta dall'installatore Autorizzato, consistente nella verifica
                delle
                funzionalità del dispositivo e del sistema con
                prove e verifiche tecniche attraverso l'invio e la ricezione di dati al e dal dispositivo.
            </p>
            <p class="mt-2">
                <span class="font-bold">Contratto:</span> il presente contratto di servizi stipulato dal Cliente con
                Alltechnology Srl relativo alla
                fornitura del Servizio MeMove;
            </p>
            <p class="mt-2">
                <span class="font-bold">Dispositivo Satellitare:</span> dispositivo elettronico GSM/GPRS/GNSS/BLUETOOTH
                dotato delle certificazioni
                CE/RED, E-Mark, EAC, RoHS,
                REACH, Anatel, ICASA, SIRIM QAS, SDPPI POSTEL
            </p>
            <p class="mt-2">
                <span class="font-bold">GNSS:</span> Sistema che consente la geolocalizzazione del veicolo tramite una o
                più costellazioni di satelliti
                in orbita intorno alla Terra
            </p>
            <p class="mt-2">
                <span class="font-bold">Dati Personali:</span> qualsiasi informazione relativa al Cliente e/o all'Utente
                (nome, cognome, numeri
                telefonici, e-Mail, indirizzo del Cliente)
                necessaria per la fornitura dei Servizi MeMove.
            </p>
            <p class="mt-2">
                <span class="font-bold">Dati di Localizzazione:</span> ogni dato che, tramite collegamento a reti di
                comunicazione, consenta di
                individuare la posizione del Veicolo.
            </p>
            <p class="mt-2">
                <span class="font-bold">Funzione Manutenzione:</span> se prevista dal Contratto, consente al Cliente,
                contattando il Centro Assistenza
                o in autonomia tramite la funzione
                in app, di effettuare operazioni di manutenzione del Veicolo senza provocare falsi allarmi. Qualora il
                Cliente dimentichi di disattivare la Funzione
                Manutenzione, Alltechnology Srl non è responsabile in nessun caso della mancata erogazione dei Servizi
                di Assistenza.
            </p>
            <p class="mt-2">
                <span class="font-bold">Funzione Trasporto:</span> se prevista dal Contratto, consente al Cliente,
                contattando il Centro Assistenza o
                in autonomia tramite la funzione in app,
                di abilitare il trasporto del Veicolo senza provocare falsi allarmi. Quando è attivata il Sistema
                Satellitare perde lo stato di Massima Protezione
                poiché́ viene disabilitato il segnale di allarme relativo al movimento del Veicolo a sistema inserito.
                Qualora il Cliente dimentichi di disattivare la
                Funzione Trasporto, Alltechnology Srl non è responsabile in nessun caso della mancata erogazione dei
                Servizi di Assistenza.
            </p>
            <p class="mt-2">
                <span class="font-bold">Furto:</span> violazione dell’art. 624 Codice penale.
            </p>
            <p class="mt-2">
                <span class="font-bold">Inizializzazione:</span> operazione relativa all'acquisizione e registrazione
                dei dati del Veicolo (marca -
                modello - colore - telaio - targa) e del
                dispositivo (modello e IMEI), raccolta dall'installatore, durante la fase di installazione del
                dispositivo sul Veicolo.
            </p>

        </div>


        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 1 a 14
                </div>
            </div>
        </div>
    </div>

    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <p>
            <span class="font-bold">Installatore Autorizzato:</span> Professionista, convenzionato da Alltechnology Srl,
            che ha ricevuto opportuna formazione tale da permettere di
            eseguire l'installazione e fornire assistenza sul dispositivo.
        </p>

        <p class="mt-2">
            <span class="font-bold">Manuale Utente: </span> manuale d'uso del dispositivo e dei Servizi di Assistenza,
            fornito digitalmente.
        </p>

        <p class="mt-2">
            <span class="font-bold">Massima Protezione:</span> stato del Sistema Satellitare quando è funzionante e le
            Funzioni Manutenzione e Trasporto non sono attive.
        </p>

        <p class="mt-2">
            <span class="font-bold"> MeMove:</span> è il Servizio di localizzazione GPS per veicoli e natanti, con
            funzione di antifurto satellitare, mediante installazione su veicolo o natante di Dispositivo
            Satellitare con connessione data GPRS, collegato con Centrale Operativa per attività̀ di Assistenza e
            Sicurezza in caso di Furto.
        </p>

        <p class="mt-2">

        </p>
        <p class="mt-2">
            <span class="font-bold"> Centro Assistenza:</span> ufficio assistenza che il Cliente può contattare per
            accedere ai Servizi di Assistenza via telefono.
        </p>
        <p class="mt-2">
            <span> Servizi di Assistenza:</span> prestazioni svolte per localizzare il Veicolo in caso di ricezione di
            segnali di allarme provenienti dal Veicolo stesso o
            a seguito di richiesta da parte del Cliente che, sotto sua responsabilità, dichiari il Furto del proprio
            Veicolo, contattando il centro assistenza.
        </p>
        <p class="mt-2">
            <span class="font-bold"> Recapiti:</span> numeri telefonici e indirizzi di e-mail del Cliente o
            dell'Utilizzatore.
            SIM Card GSM o GSM-GPRS: scheda di abbonamento telefonico, inserita all’interno della Box intestata alla
            Società ed utilizzata dalla stessa
            per il trasferimento dei dati al Centro Servizi.
        </p>
        <p class="mt-2">
            <span class="font-bold"> Stato Attivo:</span> stato del Servizio MeMove dopo l'esito positivo del Collaudo
            Tecnico e Funzionale.
        </p>
        <p class="mt-2">
            <span class="font-bold"> Veicolo:</span> il mezzo di trasporto, di proprietà̀ o in uso del Cliente, i cui
            dati di riferimento sono riportati nel Contratto, sul quale è installato il Dispositivo
            e per il quale vengono erogati i Servizi.
        </p>

        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 2 a 14
                </div>
            </div>
        </div>
    </div>

    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <h2 class="font-semibold text-center">
            - CONDIZIONI GENERALI –
        </h2>

        <div class="mt-4">
            <h2 class="font-medium">
                PREMESSE
            </h2>

            <ul style="list-style: decimal">
                <li>
                    Il Cliente è interessato all'acquisto del dispositivo satellitare MeMove, nonché all'acquisto del
                    Servizio MeMove, in quanto intende
                    avvalersi dei Servizi di geo-localizzazione alle condizioni generali di seguito riportate;
                </li>
                <li>
                    Il Cliente ha preso visione del Manuale Utente ed è edotto sulle modalità e sulle limitazioni di
                    funzionamento del Servizio MeMove;
                </li>
                <li>
                    Alltechnology Srl è titolare del contratto di abbonamento della scheda SIM inserita all'interno
                    del dispositivo satellitare;
                </li>
                <li>
                    Il Contratto si compone dei seguenti documenti: Glossario, Condizioni Generali, Modulo dati Cliente,
                    modulo di richiesta servizio, Collaudo;
                </li>
                <li>
                    Le premesse costituiscono parte integrante del contratto.
                </li>
            </ul>

            <h2 class="mt-4 font-medium">
                ARTICOLO 1 - OGGETTO
            </h2>

            <ul style="list-style: decimal">
                <li>
                    Il Contratto, regolato dalle presenti Condizioni Generali, e la cui efficacia deve intendersi
                    subordinata al pagamento del canone di
                    locazione, nonché alla regolare installazione, attivazione e Collaudo del dispositivo sul veicolo
                    del Cliente o in uso al Cliente, ha per
                    oggetto l'erogazione dei Servizi indicati nello stesso, e dettagliatamente descritti negli articoli
                    successivi. I servizi erogati dalla
                    Alltechnology Srl non costituiscono copertura assicurativa.
                </li>
                <li>
                    Il Cliente accettando le Condizioni Generali e modalità̀ di erogazione le Servizio:
                    <ul style="list-style: disc; margin-left: 20px; margin-top: 5px;">
                        <li>
                            acconsente all'installazione e Attivazione del Dispositivo, abilitando l'erogazione dei
                            Servizi così come elencati agli articoli
                            successivi;
                        </li>
                        <li>
                            autorizza Selpol Srl ad intervenire, anche mediante invio di sms/mail, per la gestione
                            operativa delle segnalazioni di Furto e/o
                            Rapina del Veicolo;
                        </li>
                        <li>
                            autorizza Selpol Srl e Alltechnology Srl al trattamento dei dati raccolti a fini per gli usi
                            di cui al presente Contratto.
                        </li>
                    </ul>
                </li>
            </ul>

            <h2 class="mt-4 font-medium">
                ARTICOLO 2 - CONDIZIONI PER L'ATTIVAZIONE
            </h2>

            <p>
                Il Cliente prende atto che le condizioni necessarie per completare l'Attivazione del Servizio MeMove
                sono:
            </p>

            <ul style="list-style: disc; margin-left: 20px; margin-top: 5px;">
                <li>
                    corretta installazione ed inizializzazione del Dispositivo presso un installatore Autorizzato da
                    Alltechnology Srl;
                </li>
                <li>
                    il Collaudo del Dispositivo, effettuato da un installatore Autorizzato da Alltechnology Srl;
                </li>
                <li>
                    la ricezione, via sms/e-mail/recapito fornito dal Cliente, da parte di Alltechnology Srl, del
                    Contratto, Collaudo debitamente
                    compilato in tutte le sue parti recante data/timbro/sottoscrizione del collaudatore.
                </li>
            </ul>

            <h2 class="mt-4 font-medium">

                ARTICOLO 3 - SERVIZIO “MEMOVE”
            </h2>
            <p>
                Il Servizio MeMove può̀ essere sottoscritto sia da Clienti persone fisiche che da Clienti persone
                giuridiche ed include i Servizi di Localizzazione
                e le Prestazioni di Controllo a Distanza del Veicolo <br> Per l'erogazione e l'Attivazione delle
                operazioni di localizzazione del Veicolo a seguito di Furto o Rapina, il Cliente deve comunicare
                immediatamente a Selpol Srl il Furto totale o la Rapina del Veicolo, chiamando il numero, direttamente o
                tramite App dedicata:
                +390899341047. Il cliente quando riceve notifica dall’app o si accorge di un tentato furto o furto è
                tenuto a contattare la centrale operativa
                per tentare il recupero e contestualmente le forze dell’ordine. <br> Il servizio decorre dal momento
                della Attivazione, ed è operativo 24 ore su 24 per ogni giorno dell'anno, a seguito della segnalazione
                di Furto
                o di Rapina comunicata dal Cliente a Selpol Srl, quest'ultima attiverà̀ la procedura per la
                localizzazione del Veicolo. Gli operatori Selpol Srl,
                per la corretta erogazione del Servizio, dovranno procedere preventivamente all'individuazione del
                Cliente ponendo a quest'ultimo le
                Domande di Riconoscimento. <br> Gli operatori Selpol Srl avvieranno la procedura di localizzazione del
                Veicolo unicamente a riconoscimento avvenuto. A tal fine, il Cliente
                autorizza la registrazione delle chiamate per il tempo necessario imposto dalla normativa vigente di
                settore ai fini dello svolgimento del
                Servizio MeMove e documentazione in caso di contestazioni ed esigenze di difesa giudiziaria dei relativi
                diritti. <br> Esclusivamente in caso di avvenuta localizzazione del Veicolo, se del caso e comunque a
                insindacabile giudizio degli operatori di Selpol
                Srl, questi ultimi potranno contattare le Forze dell'Ordine al fine di concordarne l'intervento. Ad
                avvenuto ritrovamento del Veicolo sarà̀
                cura di Selpol Srl darne comunicazione al Cliente. È responsabilità̀ del Cliente comunicare
                tempestivamente a Selpol Srl l'eventuale falso
                allarme Furto per annullare la richiesta di intervento. La mancata comunicazione può̀ comportare la
                diretta responsabilità del Cliente
                anche sul piano penale; eventuali spese, costi, oneri, sostenuti da Selpol Srl saranno pertanto a lui
                interamente addebitate. <br> Alltechnology Srl e/o Selpol Srl sono Manlevate in caso di mancanza di
                segnale gnss/gsm e/o mancata alimentazione dispositivo
            </p>




        </div>


        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 3 a 14
                </div>
            </div>
        </div>
    </div>



    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">
        <h2 class="mt-4 font-medium">
            ARTICOLO 4 - DURATA E RINNOVO DEL CONTRATTO - CORRISPETTIVO PER IL RECESSO
        </h2>


        <ul class="list-decimal">
            <li>
                L’efficacia del Contratto, per ciascun Veicolo, decorre dalla data riportata nel Certificato di
                Collaudo
                ed è limitata al periodo di durata
                dello stesso.
            </li>
        </ul>
        <ul class="list-decimal" start="2">
            <li>
                Il cliente è tenuto a selezionare la durata del servizio barrando la casella indicata nel "modulo di
                richiesta servizio". Il canone è
                collegato al periodo selezionato.
            </li>
            <li>
                Il Contratto perderà̀ la sua efficacia alle ore 24.00 del giorno di scadenza se non diversamente
                comunicato dal Cliente a mezzo mail
                o previo contatto telefonico. Il Contratto non si riterrà̀ in ogni caso rinnovato se il Cliente non
                avrà provveduto al pagamento del
                corrispettivo concordato e successivamente avrà ricevuto il nuovo certificato di collaudo con le
                date aggiornate.
            </li>
            <li>
                In caso di cessazione, per qualsiasi causa intervenuta, ad eccezione di quanto all'Art. 6.4, del
                rapporto contrattuale con Alltechnology
                Srl, Alltechnology Srl cesserà̀ di erogare al Cliente il Servizio. Il Cliente ha la facoltà̀ ed il
                diritto di recedere anticipatamente dal
                Contratto inviando a Alltechnology Srl apposita comunicazione scritta a mezzo PEC.
            </li>
            <li>
                In caso di annichilimento della SIM, chiusura anticipata o scadenza del contratto e/o mancato
                rinnovo nei modi e nei tempi stabiliti
                dall'art. 5 del presente contratto, le sim verranno disattivate da Alltechnology Srl a mezzo portale
                del gestore.
            </li>
            <li>
                In caso di recesso anticipato da parte del cliente la Alltechnology Srl non è tenuta alla
                restituzione del corrispettivo pagato dallo
                stesso per il dispositivo satellitare MeMove, nonché per il Servizio MeMove.
            </li>
        </ul>

        <h2 class="mt-4 font-medium">
            ARTICOLO 5 - CORRISPETTIVI E MODALITÀ DI PAGAMENTO
        </h2>

        <ul class="list-decimal">
            <li>
                Il canone deve essere pagato dal Cliente anticipatamente ed in base all'opzione selezionata sul "modulo
                di richiesta servizio";
            </li>
            <li>
                Ove il cliente intenda rinnovare il Contratto è tenuto ad inoltrare una formale richiesta alla
                Alltechnology Srl a mezzo mail/pec ai
                recapiti di quest'ultima precisando il numero del contratto che intende rinnovare nonché l'opzione che
                intende scegliere.
            </li>
            <li>
                A propria esclusiva discrezione e prima della scadenza del contratto la Alltechnology srl potrà inviare
                ai recapiti forniti dal cliente un
                promemoria ove indica il prezzo, le modalità di pagamento, il tempo massimo per il collaudo e che, in
                caso di mancato rinnovo entro
                le ore 24.00 dell'ultimo giorno utile, i servizi di cui al presente contratto non verranno più erogati e
                garantiti.
            </li>
        </ul>

        <h2 class="mt-4 font-medium">
            ARTICOLO 6 - CONDIZIONI PER IL CORRETTO FUNZIONAMENTO DI MEMOVE
        </h2>

        <p class="mt-2">
            Le condizioni fondamentali per il corretto funzionamento di MeMove sono:
        </p>

        <ul class="list-decimal">
            <li>
                corretta installazione del Dispositivo presso un installatore Autorizzato. L'installatore provvederà
                alla installazione a regola d'arte del
                Dispositivo accertandosi dell'avvenuta corretta Attivazione dei Servizi. Il Cliente, o l'Utente, prima
                di procedere al ritiro del Veicolo,
                deve verificare le perfette condizioni dello stesso e accertare l'avvenuta Attivazione dei Servizi;
            </li>
            <li>
                corretto funzionamento ed operatività̀ della Costellazione GPS, che consente la localizzazione del
                Veicolo sul territorio;
            </li>
            <li>
                corretto funzionamento ed operatività̀ della rete telefonica GSM e GSM-GPRS nonché́ delle linee
                telefoniche di rete fissa;
            </li>
            <li>
                capacità del Dispositivo di ricevere il segnale GPS e/o il segnale GSM GSM-GPRS, anche qualora il
                Dispositivo si trovi in luoghi
                isolati e/o senza copertura GPS, GSM e GSM-GPRS. In tali circostanze può̀ accadere che non ci sia
                copertura di tutti i segnali e
                quindi può̀ verificarsi la non corretta visibilità̀ del Veicolo per la sua localizzazione e per la
                rilevazione dei dati;
            </li>
            <li>
                adempimento, da parte del Cliente e dell'Utente, di quanto indicate nell'Articolo 8 - "Condizioni per la
                corretta Operatività̀ dei Servizi"
            </li>
        </ul>

        <h2 class="mt-4 font-medium">
            ARTICOLO 7 - CONDIZIONI PER LA CORRETTA OPERATIVITÀ DEI SERVIZI
        </h2>

        <p class="mt-2">
            L'operatività dei Servizi è subordinata ad una corretta installazione e Attivazione del Dispositivo:
        </p>

        <ul class="list-decimal">
            <li>
                il Cliente è tenuto a controllare che tutti i dati riportati sul Contratto siano esatti;
            </li>
            <li>
                il Cliente è tenuto a comunicare prontamente a Alltechnology Srl l'eventuale cambiamento dei dati e dei
                Recapiti telefonici e e-Mail
                propri e/o di quelli della persona, indicata dal Cliente, che Alltechnology Srl e/o Selpol Srl devono
                contattare per la corretta
                erogazione dei Servizi;
            </li>
            <li>
                in caso di guasto e/o di mancato funzionamento del Dispositivo, dopo segnalazione del Cliente verrà̀
                informato della necessita di
                recarsi, mettendo a disposizione il Veicolo, nel più̀ breve tempo possibile, presso un installatore
                Autorizzato per la verifica e
                manutenzione del Dispositivo;
            </li>



        </ul>
        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 4 a 14
                </div>
            </div>
        </div>
    </div>



    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <ul class="list-decimal" start="4">
            <li>
                Il Cliente ha l'obbligo di non intervenire direttamente e/o indirettamente sul Dispositivo e le sue
                componenti. Ogni intervento sullo
                stesso deve essere condotto da personale autorizzato da Alltechnology Srl, la quale non risponderà̀
                delle conseguenze derivanti
                dalla non osservanza di quanto qui precisato e si riserva il diritto di risoluzione immediata del
                Contratto, per fatto e colpa del Cliente,
                tramite comunicazione scritta al Cliente. Il Cliente e l'Utente hanno l'obbligo di utilizzare il
                Dispositivo unicamente per le finalità̀ di
                cui al Contratto e s'impegnano altresì̀ a non intervenire, modificare e/o alterare, direttamente e/o
                indirettamente la funzionalità del
                Dispositivo. Il Cliente, inoltre, si obbliga a non cedere, neanche provvisoriamente, l'uso del
                Dispositivo a terzi;
            </li>
            <li>
                Il Cliente è tenuto a recarsi il prima possibile presso l'installatore Autorizzato indicato da
                Alltechnology Srl al fine di verificare il
                corretto funzionamento del Dispositivo dopo un'incidente, dopo un Furto/Rapina o comunque dopo eventi
                che possono aver causato
                malfunzionamenti e danneggiamenti al Dispositivo o alla sua installazione nonché dopo operazioni di
                manutenzione e/o riparazione
                sul Veicolo svolte da personale estraneo alla rete degli installatori convenzionati, qualora sia stato
                oggettivamente constatato che
                tali riparazioni abbiano interessato il Dispositivo e parti ad essa accessorie, e comunque in tutti quei
                casi in cui si accorga di un
                malfunzionamento del Dispositivo e/o di una errata erogazione del Servizio;
            </li>
            <li>
                Ai fini dell'erogazione da parte di Selpol Srl del Servizio di localizzazione del Veicolo in caso di
                Furto o Rapina, l'Utente dovrà
                rendersi reperibile 24 ore al giorno, 365 giorni all'anno, al numero di telefono dallo stesso indicate.
                Gli operatori Selpol Srl, per la
                corretta erogazione di detto Servizio, dovranno procedere preventivamente al riconoscimento del Cliente,
                tramite la procedura
                indicata all'art. 4.2 del Contratto.
            </li>
        </ul>

        <h2 class="mt-4 font-medium">
            ARTICOLO 8 - RESPONSABILITÀ: ESONERO ED ESCLUSIONI
        </h2>

        <ul class="list-decimal">
            <li>
                In nessun caso Alltechnology Srl e/o Selpol Srl, a seconda del caso, risponderà̀ per interruzioni e
                limitazioni dei Servizi per le seguenti cause:
                <ul class="list-[lower-alpha] ml-6 mt-2">
                    <li>disposizioni di Legge o disposizioni amministrative sopravvenute;</li>
                    <li>provvedimenti emanati dalle Autorità̀ competenti;</li>
                    <li>modifiche effettuate dal Cliente o dall'Utente alla SIM Card GSM o GSM-GPRS;</li>
                    <li>per modifiche e/o danni e/o distruzione causati al Dispositivo dal Cliente, o dall'Utente e/o da
                        terzi, anche se in circostanze fortuite ed imprevedibili;</li>
                    <li>mancato funzionamento del Dispositivo rispetto a quanto previsto al precedente articolo 7 del
                        Contratto e/o mancato rispetto di quanto riportato al precedente articolo 8 del Contratto.</li>
                </ul>
            </li>
            <li>
                Selpol Srl, inoltre, si riserva di non intervenire a seguito di:
                <ul class="list-[lower-alpha] ml-6 mt-2">
                    <li>terremoti e calamità naturali in genere;</li>
                    <li>sviluppo comunque insorto, controllato o meno, di energia nucleare o di radioattività̀;</li>
                    <li>guerre, insurrezioni, disordini, scioperi, sommosse, atti di terrorismo, occupazioni militari e
                        vandalismo.</li>
                </ul>
            </li>
            <li>
                Selpol Srl, fatti salvi i casi di dolo e colpa grave della stessa, in nessun caso risponderà̀ degli
                eventuali danni derivanti al Cliente in caso di Furto e/o danneggiamento del Veicolo e/o di persone e/o
                materiale a presenti a bordo del Veicolo stesso, o in caso di mancato e/o tardivo intervento delle
                Autorità̀ competenti a seguito di segnalazione di Selpol Srl.
            </li>
            <li>
                Alltechnology Srl e/o Selpol Srl sono Manlevate in caso di mancanza di segnale gnss/gsm e/o mancata
                alimentazione dispositivo.
            </li>
        </ul>

        <h2 class="mt-4 font-medium">
            ARTICOLO 9 - SOSTITUZIONE, ALIENAZIONE, PRESTITO O LOCAZIONE DEL VEICOLO – PORTABILITÀ DEL CONTRATTO
        </h2>

        <ul class="list-decimal">
            <li>
                Il Cliente ha facoltà̀ di trasferire il Dispositivo su un altro Veicolo.
            </li>
            <li>
                Nel caso in cui il Cliente trasferisca a terzi, sotto qualsiasi forma ed a qualsiasi titolo, anche non
                oneroso, il Veicolo sul quale è installato
                il Dispositivo, senza rimozione dello stesso, il Cliente dovrà̀ prontamente comunicare al nuovo
                proprietario che il Veicolo è dotato del
                Sistema Satellitare MeMove e contestualmente dovrà darne comunicazione, tramite mail, alla Alltechnology
                Srl affinché proceda
                all'aggiornamento dell'anagrafica del cliente.
            </li>
            <li>
                Nel caso in cui il Cliente autorizzi l'utilizzo del Veicolo da parte di terzi, il Cliente si impegna a
                segnalare al terzo la presenza del Sistema
                MeMove e le sue funzionalità̀. La Società̀ non risponde in alcun modo di eventuali usi impropri del
                Sistema MeMove da parte del Cliente
                e/o di terzi.
            </li>
            <li>
                Nel caso in cui il Cliente conceda il Veicolo a terzi in locazione, il Cliente si impegna tassativamente
                a segnalare al locatario la presenza
                sul Veicolo del Sistema MeMove e le relative funzionalità̀. Alltechnology Srl non risponde in alcun modo
                di eventuali usi impropri del
                Sistema MeMove da parte del Cliente e/o del locatario.
            </li>
        </ul>



        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 5 a 14
                </div>
            </div>
        </div>
    </div>

    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <h2 class="mt-4 font-medium">
            ARTICOLO 10 - ESTENSIONE TERRITORIALE
        </h2>

        <ul class="list-decimal">
            <li>
                L'estensione territoriale dell'erogazione del Servizio di Assistenza e Sicurezza è limitata al solo
                territorio dello Stato italiano compreso
                lo Stato Vaticano e la Repubblica di San Marino.
            </li>
            <li>
                L'estensione territoriale delle Prestazioni di Notifica e Controllo è limitata al solo territorio dello
                Stato italiano compreso lo Stato Vaticano
                e la Repubblica di San Marino.
            </li>
            <li>
                Il Contraente è consapevole che i Servizi dipendono dalla copertura GSM o GSM-GPRS sui territori di cui
                sopra.
            </li>
        </ul>
        <h2 class="mt-4 font-medium">
            ARTICOLO 13 - CESSIONE DEL CONTRATTO
        </h2>

        <ul class="list-decimal">
            <li>
                Nel caso di vendita del Veicolo a terzi qualora comporti anche la cessione del Contratto, il Cliente
                dovrà informare il cessionario della
                presenza del Dispositivo sul Veicolo. Qualora il cessionario definisca un rapporto contrattuale con
                Alltechnology Srl, e subentri nel
                Contratto, si procederà alle necessarie modifiche;
            </li>
            <li>
                Il Cliente dichiara e conviene che Alltechnology Srl possa cedere a terzi il Contratto e/o i crediti
                dallo stesso derivanti, senza necessità
                di alcuna formalità e dell'accettazione del Cliente, fatto salvo il solo obbligo di comunicazione al
                Cliente a titolo informativo. Il consenso
                del Cliente rispetto alla previsione di cui al presente articolo si intende già irrevocabilmente
                espresso con la sottoscrizione del Contratto.
            </li>
        </ul>

        <h2 class="mt-4 font-medium">
            ARTICOLO 12 - COMUNICAZIONE DATI A COMPAGNIE ASSICURATIVE
        </h2>

        <p class="mt-2">
            Il Cliente autorizza Alltechnology Srl a fornire, su richiesta di compagnie assicuratrici che avessero
            concesso tariffe agevolate sulla polizza incendio e furto, dati ed informazioni relativi al veicolo ed al
            servizio prima, durante e dopo il tentativo di furto o il furto totale.
        </p>

        <h2 class="mt-4 font-medium">
            ARTICOLO 13 - INTERPRETAZIONE E FORO
        </h2>

        <p class="mt-2">
            Per l'interpretazione ed esecuzione del contratto le presenti condizioni generali prevalgono su ogni
            precedente contratto, documento o pattuizione, ferma ed impregiudicata la sola efficacia integrativa, ma non
            modificativa o innovativa, della proposta di Alltechnology Srl, nonché del modulo di attivazione e del
            modulo di installazione e Collaudo.
        </p>
        <p class="mt-2">
            Ogni controversia relativa al presente Contratto, i cui oneri e spese di registrazione sono a carico del
            Cliente, sarà devoluta alla competenza esclusiva e inderogabile del Foro di Salerno. Nel caso in cui il
            cliente è un consumatore trova applicazione la disciplina del Foro del Consumatore.
        </p>

        <h2 class="mt-4 font-medium">
            ARTICOLO 14 - COMUNICAZIONI E RECLAMI
        </h2>

        <p class="mt-2">
            Per qualsiasi comunicazione inerente alla gestione del Contratto e per eventuali Reclami il Cliente dovrà
            rivolgersi ad Alltechnology Srl, ai <NAME_EMAIL> o <EMAIL> o contattando il
            Centro Assistenza al numero 0899341047
        </p>

        <h2 class="mt-4 font-medium">
            ARTICOLO 15 – SOLVE ET REPETE
        </h2>

        <p class="mt-2">
            Ogni eventuale contestazione relativa al mancato adempimento degli obblighi derivanti dal presente contratto
            è subordinata al regolare pagamento del canone di locazione relativo al periodo di abbonamento selezionato
            dal cliente.
        </p>

        <h2 class="mt-4 font-medium">
            ARTICOLO 16 - LOCALIZZAZIONE DEL VEICOLO E NORMATIVA GIUSLAVORISTICA
        </h2>

        <ul class="mt-2 list-decimal">
            <li>
                Con la sottoscrizione del Modulo di Abbonamento, l'Utilizzatore autorizza Alltechnology Srl a comunicare
                in qualsiasi momento al Cliente, anche tramite visualizzazione sul Sito Web/Smartphone, i Dati relativi
                alla localizzazione del Veicolo, consentendo al Cliente medesimo di verificare in tempo reale la
                localizzazione del Veicolo.
            </li>
            <li>
                Il Cliente, e se nel caso l'Utilizzatore, si impegna a comunicare ai propri dipendenti, collaboratori,
                lavoratori parasubordinati e/o a terzi che utilizzano Veicoli in relazione all'esistenza ed
                all'operatività del servizio di localizzazione del Veicolo medesimo ed a richiedere ed ottenere
                l'esplicito consenso dei medesimi soggetti in tal senso.
            </li>

        </ul>


        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 6 a 14
                </div>
            </div>
        </div>
    </div>



    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <div class="mt-4">

            <ul class="list-decimal" start="3">
                <li>
                    II Cliente, e se nel caso l'Utilizzatore, si impegna ad ottenere le specifiche autorizzazioni
                    preventive
                    necessarie secondo la normativa vigente, in particolare in materia giuslavoristica, qualora abbia
                    richiesto il libero accesso alla consultazione dei Dati relativi alla localizzazione del Veicolo in
                    tempo reale anche tramite la visualizzazione sul Sito Web, nel caso il Veicolo medesimo sia nella
                    disponibilità di dipendenti, collaboratori, lavoratori parasubordinati e/o di terzi, consapevole che
                    le
                    predette autorizzazioni sono necessarie a prescindere dal consenso degli interessati di cui sopra,
                    dichiarando fin d'ora di manlevare e tenere indenne Alltechnology Srl da qualsiasi pregiudizio che
                    possa
                    derivare dalla violazione della predetta normativa.
                </li>
            </ul>

            <p class="mb-2">
                Alltechnology Srl, avente sede in Salerno (SA) alla via Ogliara, 13 provvederà all'attivazione del
                servizio di localizzazione denominato "MeMove" previa installazione del dispositivo satellitare.
            </p>

            <p class="mb-6">
                Ai sensi di legge, dichiaro che i dati indicati nel presente Modulo di Richiesta Servizio sono veritieri
                e dichiaro altresì di avere discusso e di accettare le Condizioni Generali che, riportate in allegato
                costituiscono parte integrante e sostanziale del presente Modulo, e di essere informato delle istruzioni
                di funzionamento del Dispositivo.
            </p>

            <div class="flex items-center justify-start p-2 mt-4 border border-black" style="width: fit-content">
                <span class="mr-4">
                    @if ($contract->status == 'active' && $contract->responded_at)
                        Contratto accettato in
                        @if ($contract->responded_at)
                            data
                            {{ \Carbon\Carbon::parse($contract->responded_at)->format('d/m/Y') }}
                            alle
                            {{ \Carbon\Carbon::parse($contract->responded_at)->format('H:i') }}
                        @else
                            data
                            {{ now()->format('d/m/Y') }}
                            alle
                            {{ now()->format('H:i') }}
                        @endif

                        dal numero {{ $contract->client?->phone_number ?? 'N/A' }}
                    @else
                        Contratto accettato in ...... data ...... alle ...... dal numero ......
                    @endif
                </span>

            </div>
        </div>



        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 7 a 14
                </div>
            </div>
        </div>
    </div>

    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <div>
            <h2 class="font-medium text-center">
                MODULO RICHIESTA SERVIZIO "MeMove"
            </h2>

            <h3 class="mt-3 font-medium text-center">
                Dati del Cliente
            </h3>

            {{-- table --}}
            <table class="w-full mt-4 border-collapse">
                <tr>
                    @if ($contract->client?->type == 'company')
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Ragione
                            Sociale</td>
                        <td class="p-1 border border-black w-1-4" colspan="3">
                            {{ $contract->client?->user?->name ?? '' }}
                        </td>
                    @else
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Cognome</td>
                        <td class="p-1 border border-black w-1-4">
                            {{ $contract->client?->last_name ?? '' }}
                        </td>
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Nome</td>
                        <td class="p-1 border border-black w-1-4">
                            {{ $contract->client?->user?->name ?? '' }}
                        </td>
                    @endif
                </tr>
                <tr>
                    @if ($contract->client?->type == 'company')
                        <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Partita Iva
                        </td>
                        <td colspan="3" class="p-2 border border-black">
                            {{ $contract->client?->tax_code ?? '' }}
                        </td>
                    @else
                        <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Codice
                            Fiscale
                        </td>
                        <td colspan="3" class="p-2 border border-black">
                            {{ $contract->client?->tax_code ?? '' }}
                        </td>
                    @endif


                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Indirizzo</td>
                    <td class="p-2 border border-black">
                        {{ $contract->client?->address ?? '' }}
                    </td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Comune</td>
                    <td class="p-2 border border-black">
                        {{ $contract->client?->municipality ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Cap</td>
                    <td class="p-2 border border-black">
                        {{ $contract->client?->zip_code ?? '' }}
                    </td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Provincia</td>
                    <td class="p-2 border border-black">
                        {{ $contract->client?->province ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">E-mail</td>
                    <td class="p-2 border border-black">
                        {{ $contract->client?->user?->email ?? '' }}
                    </td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Cellulare</td>
                    <td class="p-2 border border-black">
                        {{ $contract->client?->phone_number ?? '' }}
                    </td>
                </tr>
            </table>

            <h3 class="mt-3 font-medium text-center">
                Dati del Veicolo
            </h3>

            {{-- table --}}
            <table class="w-full mt-4 border-collapse">
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Tipo di veicolo
                    </td>
                    <td class="w-3/4 p-2 border border-black" colspan="3">
                        @lang('messages.' . $contract->verification_vehicle_type ?? '')
                    </td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Marca</td>
                    <td class="p-2 border border-black w-1-4">
                        {{ $contract->vehicle_brand ?? '' }}
                    </td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Modello</td>
                    <td class="p-2 border border-black w-1-4">
                        {{ $contract->vehicle_model ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Colore</td>
                    <td class="p-2 border border-black w-1-4">
                        {{ $contract->vehicle_color ?? '' }}
                    </td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Data
                        immatricolazione</td>
                    <td class="p-2 border border-black w-1-4">
                        {{ $contract->vehicle_registration_date ? \Carbon\Carbon::parse($contract->vehicle_registration_date)->format('d/m/Y') : '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Targa</td>
                    <td class="p-2 border border-black w-1-4">
                        {{ $contract->vehicle_number_plate ?? '' }}
                    </td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Telaio</td>
                    <td class="p-2 border border-black w-1-4">
                        {{ $contract->frame ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Km iniziali</td>
                    <td class="w-3/4 p-2 border border-black" colspan="3">
                        {{ $contract->vehicle_km ?? '' }}
                    </td>
                </tr>
            </table>

            <h3 class="mt-3 font-medium text-center">
                Dati del Servizio "MeMove" e del Dispositivo Satellitare
            </h3>

            {{-- table --}}
            <table class="w-full mt-4 border-collapse">
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">IMEI Periferica
                    </td>
                    <td class="p-2 border border-black w-1-4">
                        {{ $contract->device?->imei ?? '' }}
                    </td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Durata in mesi
                    </td>
                    <td class="p-2 border border-black w-1-4">
                        {{ $contract->duration ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Data inizio</td>
                    <td class="p-2 border border-black w-1-4">
                        {{ $contract->start_date ? \Carbon\Carbon::parse($contract->start_date)->format('d/m/Y') : '' }}
                    </td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Data fine</td>
                    <td class="p-2 border border-black w-1-4">
                        {{ $contract->end_date ? \Carbon\Carbon::parse($contract->end_date)->format('d/m/Y') : '' }}
                    </td>
                </tr>
            </table>

            <div class="mt-4">
                <p class="mb-2">
                    Alltechnology Srl, avente sede in Salerno (SA) alla via Ogliara, 13 provvederà all'attivazione del
                    servizio di localizzazione denominato "MeMove" previa installazione del dispositivo satellitare.
                </p>

                <p class="mb-6">
                    Ai sensi di legge, dichiaro che i dati indicati nel presente Modulo di Richiesta Servizio sono
                    veritieri e dichiaro altresì di avere discusso e di accettare le Condizioni Generali che, riportate
                    in allegato costituiscono parte integrante e sostanziale del presente Modulo, e di essere informato
                    delle istruzioni di funzionamento del Dispositivo.
                </p>

                <div class="flex items-center justify-start p-2 mt-4 border border-black" style="width: fit-content">
                    @if ($contract->status == 'active' && $contract->responded_at)
                        Contratto accettato in
                        @if ($contract->responded_at)
                            data
                            {{ \Carbon\Carbon::parse($contract->responded_at)->format('d/m/Y') }}
                            alle
                            {{ \Carbon\Carbon::parse($contract->responded_at)->format('H:i') }}
                        @else
                            data
                            {{ now()->format('d/m/Y') }}
                            alle
                            {{ now()->format('H:i') }}
                        @endif

                        dal numero {{ $contract->client?->phone_number ?? 'N/A' }}
                    @else
                        Contratto accettato in ...... data ...... alle ...... dal numero ......
                    @endif
                </div>
            </div>
        </div>



        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 8 a 14
                </div>
            </div>
        </div>
    </div>


    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <div>
            <h2 class="font-medium text-center">
                CERTIFICATO DI INSTALLAZIONE E COLLAUDO
            </h2>

            <!-- Dati del Cliente -->
            <h3 class="mt-3 font-medium text-center">Dati del Cliente</h3>
            <table class="w-full mt-4 border-collapse">
                <tr>
                    @if ($contract->client?->type == 'company')
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Ragione
                            Sociale</td>
                        <td class="p-1 border border-black w-1-4" colspan="3">
                            {{ $contract->client?->user?->name ?? '' }}
                        </td>
                    @else
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Cognome</td>
                        <td class="p-1 border border-black w-1-4">
                            {{ $contract->client?->last_name ?? '' }}
                        </td>
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Nome</td>
                        <td class="p-1 border border-black w-1-4">
                            {{ $contract->client?->user?->name ?? '' }}
                        </td>
                    @endif
                </tr>
            </table>

            <!-- Dati del Veicolo -->
            <h3 class="mt-3 font-medium text-center">Dati del Veicolo</h3>
            <table class="w-full mt-4 border-collapse">
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Tipo di veicolo
                    </td>
                    <td class="p-1 border border-black w-1-4">
                        @lang('messages.' . $contract->verification_vehicle_type ?? '')
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Km iniziali</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_km ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Marca</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_brand ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Modello</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_model ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Colore</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_color ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Data
                        immatricolazione</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_registration_date ? \Carbon\Carbon::parse($contract->vehicle_registration_date)->format('d/m/Y') : '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Targa</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_number_plate ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Telaio</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->frame ?? '' }}
                    </td>
                </tr>
            </table>

            <!-- Dati del Collaudo -->
            <h3 class="mt-3 font-medium text-center">Dati del Collaudo</h3>
            <table class="w-full mt-4 border-collapse">
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">IMEI Periferica
                    </td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->device?->imei ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Durata in mesi
                    </td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->duration ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Data inizio</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->start_date ? \Carbon\Carbon::parse($contract->start_date)->format('d/m/Y') : '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Data fine</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->end_date ? \Carbon\Carbon::parse($contract->end_date)->format('d/m/Y') : '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Data Collaudo
                    </td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->created_at ? \Carbon\Carbon::parse($contract->created_at)->format('d/m/Y') : '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Esito</td>
                    <td class="p-1 border border-black w-1-4">
                        @if ($contract->status == 'active')
                            Positivo
                        @else
                            {{ $contract->status ? __('messages.' . $contract->status) : '' }}
                        @endif
                    </td>
                </tr>
            </table>

            <!-- Dati del Rivenditore/Installatore -->
            <h3 class="mt-3 font-medium text-center">Dati del Rivenditore/Installatore</h3>
            <table class="w-full mt-4 border-collapse">
                <tr>
                    @if ($contract->dealer?->type == 'company')
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Ragione
                            Sociale</td>
                        <td class="p-1 border border-black w-1-4" colspan="3">
                            {{ $contract->dealer?->user?->name ?? '' }}
                        </td>
                    @else
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Cognome</td>
                        <td class="p-1 border border-black w-1-4">
                            {{ $contract->dealer?->last_name ?? '' }}
                        </td>
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Nome</td>
                        <td class="p-1 border border-black w-1-4">
                            {{ $contract->dealer?->user?->name ?? '' }}
                        </td>
                    @endif
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Indirizzo</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->address ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Comune</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->municipality ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Cap</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->zip_code ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Provincia</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->province ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">E-mail</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->user?->email ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Cellulare</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->phone_number ?? '' }}
                    </td>
                </tr>
            </table>

            <!-- Servizi e Funzioni -->
            <h3 class="mt-3 font-medium text-center">Servizi e Funzioni</h3>
            <table class="w-full mt-4 border-collapse">
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Area riservata
                        Web</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Area riservata
                        App</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Localizzazione
                        Internet</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Visualizzazione
                        storico</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Geofence
                        Automatica</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Geofence Manuale
                    </td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Positivo Sotto
                        Chiave</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Blocco
                        Avviamento</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">
                        @if ($contract->starter_motor_block == 'Yes')
                            X
                        @endif
                    </td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Centrale
                        Operativa 24/7</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Traffico dati
                        SIM</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Allarme Jamming
                    </td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Allarme
                        Sollevamento</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Allarme Traino
                    </td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Allarme Stacco
                        Batteria</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
            </table>


        </div>



        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 9 a 14
                </div>
            </div>
        </div>
    </div>



    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <div>
            <div class="mt-8 space-y-6">
                <!-- Declaration Section -->
                <div class="text-justify">
                    <p class="mb-2 font-semibold">Alltechnology Srl e "Ragione Sociale Dealer" con la presente
                        dichiarano che:</p>
                    <p>
                        Il collaudo dell'hardware/apparato satellitare installato sul suddetto veicolo ha consentito di
                        verificarne il regolare e perfetto funzionamento.
                    </p>
                </div>

                <!-- Alltechnology Declaration -->
                <div class="text-justify">
                    <p class="mb-2 font-semibold">Alltechnology Srl con la presente dichiara che:</p>
                    <p>
                        È stato attivato il servizio di telesorveglianza satellitare/centrale operativa per il periodo
                        sopra indicato.
                    </p>
                </div>

                <!-- App Information -->
                <div class="text-justify">
                    <p class="mb-2">
                        Scarichi l'applicazione <span class="font-semibold">MeMove</span> per smartphone del Play Store
                        o Apple Store per accedere all'area riservata, in modo da tenersi aggiornato in qualsiasi
                        momento e avere tutti i servizi a portata di mano.
                    </p>
                </div>

                <!-- Operations Center Information -->
                <div class="text-justify">
                    <p>
                        La Centrale Operativa è a sua disposizione 24 ore al giorno, 7 giorni su 7, per intervenire in
                        qualsiasi momento e avvisarla in caso di anomalie.
                    </p>
                </div>
            </div>

            <!-- Contatti Utili -->
            <h3 class="mt-3 font-medium text-center">Contatti Utili</h3>
            <table class="w-full mt-4 border-collapse">
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Assistenza
                        Tecnica</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">+39 089 934 1047</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Centrale
                        Operativa</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">+39 081 1962 3020</td>
                </tr>
            </table>


            <p class="flex items-center justify-start p-2 mt-4 border border-black" style="width: fit-content">
                @if ($contract->status == 'active' && $contract->responded_at)
                    Contratto accettato in
                    @if ($contract->responded_at)
                        data
                        {{ \Carbon\Carbon::parse($contract->responded_at)->format('d/m/Y') }}
                        alle
                        {{ \Carbon\Carbon::parse($contract->responded_at)->format('H:i') }}
                    @else
                        data
                        {{ now()->format('d/m/Y') }}
                        alle
                        {{ now()->format('H:i') }}
                    @endif

                    dal numero {{ $contract->client?->phone_number ?? 'N/A' }}
                @else
                    Contratto accettato in ...... data ...... alle ...... dal numero ......
                @endif
            </p>
        </div>


        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 10 a 14
                </div>
            </div>
        </div>
    </div>


    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <div>
            <h2 class="font-semibold text-center underline">
                INFORMATIVA SUL TRATTAMENTO DEI DATI PERSONALI
            </h2>
            <p class="italic text-center">
                (ai sensi degli artt. 13 e 14, Regolamento (UE) n. 2016/679)

            </p>

            <p>
                Il Cliente che si avvale dei Servizi MeMove di cui alla Condizioni Generali sottoscritte con
                Alltechnology Srl prende atto che il titolare del
                trattamento dei suoi Dati Personali Ai sensi del Regolamento (UE) 2016/679 (il “Regolamento” o “GDPR”),
                è: Alltechnology Srl con sede
                in Salerno (SA) alla via Ogliara, 13 - CAP 84135, PEC: <EMAIL>, P.IVA 06225780656.
            </p>
            <p>
                Alltechnology Srl tratterà i dati di tipo personale, tra cui i dati anagrafici, fiscali, economici, i
                dati del Veicolo, l'ubicazione e il percorso
                effettuato dal Veicolo. Tali dati saranno trattati per finalità connesse al corretto adempimento degli
                obblighi contrattuali, degli obblighi
                contabili e degli obblighi previsti dalla legge, in conformità al Regolamento (UE) n. 679 del 27 aprile
                2016, c.d. General Data Protection
                Regulation o "Regolamento", nonché dal D. Lgs. n. 196 del 30 giugno 2003, recante il "Codice per la
                protezione dei dati personali", come
                da ultimo modificato dal D. Lgs. 10 agosto 2018, n. 101 e come di volta in volta modificato o
                sostituito.
            </p>

            <div class="mt-2">
                <!-- Title -->
                <h2 class="font-semibold">
                    CATEGORIE DI DATI TRATTATI ED ORIGINE DEGLI STESSI
                </h2>

                <!-- Introduction -->
                <p class="mb-2">
                    Alltechnology Srl tratta le seguenti categorie di Dati Personali, raccolti direttamente presso di
                    Lei ovvero presso fonti pubbliche e soggetti terzi:
                </p>

                <!-- Data Categories List -->
                <div class="space-y-2">
                    <!-- Common Data -->
                    <div>
                        <p>
                            <span class="font-semibold">a)</span> dati comuni, quali:
                        </p>
                        <ul class="ml-8 list-roman">
                            <li>dati anagrafici (nome, cognome, indirizzo di residenza, codice fiscale)</li>
                            <li>dati di contatto (numero di telefono e indirizzo e-mail) da Lei forniti all'atto della
                                sottoscrizione del Contratto</li>
                            <li>voce, in caso di richiesta di assistenza al call center di Alltechnology</li>
                        </ul>
                    </div>

                    <!-- Vehicle Data -->
                    <div>
                        <p>
                            <span class="font-semibold">b)</span> dati relativi all'identificazione e alle
                            caratteristiche del veicolo assicurato (es. numero di targa, modello, classe, tipologia di
                            alimentazione, cilindrata, kW ecc.) raccolti presso soggetti terzi (es. Compagnie di
                            Assicurazione) e/o banche dati pubbliche (es. Pubblico Registro Automobilistico – P.R.A.) in
                            virtù di apposite convenzioni stipulate con i soggetti titolari di tali banche dati;
                        </p>
                    </div>

                    <!-- Telematic Data -->
                    <div>
                        <p>
                            <span class="font-semibold">c)</span> dati telematici puntuali rilevati e registrati dal
                            dispositivo installato sul Suo veicolo relativi ad eventi che coinvolgono il veicolo, quali
                            furto, soccorso stradale.
                        </p>
                    </div>

                    <!-- Specific Data -->
                    <div>
                        <p>
                            <span class="font-semibold">d)</span> Nello specifico, saranno oggetto di trattamento, a
                            titolo esemplificativo e non esaustivo, i seguenti dati puntuali: velocità in base a
                            parametri di tempo e luogo, dati relativi ai movimenti, dati di localizzazione e, più in
                            generale, alla geo-referenziazione del veicolo (ad es., ubicazione).
                        </p>
                    </div>

                    <!-- Third Party Data -->
                    <div>
                        <p>
                            <span class="font-semibold">e)</span> Qualora Lei comunichi dati personali di terzi (es.
                            dati del conducente principale), rispetto a tali dati Lei si pone come autonomo titolare del
                            trattamento, assumendosi tutti gli obblighi e le responsabilità di legge. In tal senso, sul
                            punto conferisce la più ampia manleva rispetto a ogni contestazione, pretesa, richiesta di
                            risarcimento del danno derivante dal trattamento ecc. che dovesse pervenire da tali terzi i
                            cui dati personali siano stati trattati in violazione delle norme sulla tutela dei dati
                            personali applicabile. In ogni caso, qualora fornisse o in altro modo trattasse dati
                            personali di terze parti, garantisce fin da ora – assumendosene ogni connessa
                            responsabilità, tra cui il conferimento a tale soggetto della presente Informativa – che
                            tale particolare ipotesi di trattamento si fonda su un'idonea base giuridica ai sensi del
                            Regolamento che legittima il trattamento dei dati personali.
                        </p>
                    </div>
                </div>
            </div>


            <div class="mt-4">
                <!-- Title -->
                <h2 class="font-semibold">
                    BASE GIURIDICA E FINALITÀ DEL TRATTAMENTO
                </h2>

                <!-- Introduction -->
                <p class="mb-2">
                    I Suoi Dati Personali saranno trattati da Alltechnology Srl per le seguenti finalità:
                </p>

                <!-- Purposes List -->
                <div class="space-y-2">
                    <!-- Purpose A -->
                    <div>
                        <p>
                            <span class="font-semibold">a)</span> Erogazione dei servizi da Lei richiesti con la
                            sottoscrizione del Contratto di cui Lei è parte e/o di misure precontrattuali adottate su
                            Sua richiesta, inclusi, a titolo esemplificativo e non esaustivo:
                        </p>
                        <ul class="mt-2 ml-8 list-roman">
                            <li>attivazione e gestione del profilo utente associato al dispositivo satellitare presente
                                sul veicolo al fine di consentirle l'utilizzo dei servizi telematici previsti dal
                                Contratto;</li>
                            <li>gestione ed evasione, mediante call center, delle richieste di assistenza (tecnica e/o
                                logistica) da Lei inviate in relazione ai prodotti e/o servizi resi da Alltechnology
                                Srl.</li>
                        </ul>
                        <p class="mt-2">
                            A tal fine, le telefonate ricevute (c.d. inbound) e/o effettuate (c.d. outbound) dagli
                            operatori del call center potranno essere registrate, previo espresso avviso che Le sarà
                            fornito prima dell'avvio della registrazione, ed utilizzate al solo fine di ottimizzare e
                            migliorare la qualità del servizio di assistenza clienti ("Finalità di esecuzione del
                            contratto"). Il conferimento dei Dati Personali per la predetta finalità è necessario ai
                            fini dell'esecuzione del Contratto stipulato con Alltechnology Srl e, in assenza di
                            conferimento, non sarà possibile erogare i servizi ivi previsti. La base giuridica del
                            trattamento per questa finalità è costituita dall'art. 6(1)(b) del Regolamento, ossia
                            l'esecuzione di un contratto di cui Lei è parte o di misure precontrattuali adottate su Sua
                            richiesta.
                        </p>
                    </div>

                </div>
            </div>
        </div>

        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 11 a 14
                </div>
            </div>
        </div>
    </div>

    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <div>

            <!-- Purpose B -->
            <div>
                <p>
                    <span class="font-semibold">b)</span> Adempimento di obblighi previsti da disposizioni di legge o
                    di regolamento (es. in materia fiscale) a cui Alltechnology Srl è soggetta. Il conferimento dei Suoi
                    Dati Personali per questa finalità è obbligatorio. La base giuridica è rappresentata dall'art.
                    6(1)(c) del Regolamento ("Finalità di compliance");
                </p>
            </div>

            <!-- Purpose C -->
            <div>
                <p>
                    <span class="font-semibold">c)</span> Prevenzione, individuazione e/o sanzione di frodi nel proprio
                    legittimo interesse nonché per conseguenti finalità difensive in caso di comportamenti illeciti,
                    abusi o frodi. La base giuridica del trattamento è rappresentata dal legittimo interesse di
                    Alltechnology Srl ai sensi dell'art. 6(1)(f) del Regolamento, consistente nell'esigenza di prevenire
                    e contrastare comportamenti illeciti, abusi o frodi ("Finalità di prevenzione e contrasto di
                    frodi");
                </p>
            </div>

            <!-- Purpose D -->
            <div>
                <p>
                    <span class="font-semibold">d)</span> Soddisfare eventuali esigenze difensive tanto in ambito
                    giudiziale quanto nelle fasi che precedono il contenzioso. La base giuridica del trattamento per
                    questa finalità è costituita dal legittimo interesse di Alltechnology Srl ex art. 6(1)(f) del
                    Regolamento, consistente nella necessità di garantirsi un'adeguata difesa tanto in ambito giudiziale
                    che stragiudiziale ("Finalità difensive");
                </p>
            </div>

            <!-- Purpose E -->
            <div>
                <p>
                    <span class="font-semibold">e)</span> Alcuni dei Suoi Dati Personali – ed in particolare i dati
                    relativi allo stile di guida nonché i dati concernenti le caratteristiche dei veicoli - raccolti
                    presso banche dati pubbliche e/o soggetti terzi - potranno inoltre essere anonimizzati in maniera
                    irreversibile da Alltechnology Srl, in base al legittimo interesse di questi, ed essere
                    successivamente trattati in forma anonima per finalità di miglioramento dei servizi, aggiornamento e
                    perfezionamento delle prestazioni degli algoritmi utilizzati da Alltechnology Srl nell'ambito della
                    fornitura dei propri prodotti e servizi telematici nonché per finalità di profilazione senza
                    ricadute personalizzate sull'interessato.
                </p>
            </div>


            <!-- Modalità del Trattamento Section -->
            <div class="mt-4">
                <h2 class="mb-2 font-semibold">
                    MODALITÀ DEL TRATTAMENTO
                </h2>
                <p class="mb-2">
                    Il trattamento dei Dati da parte di Alltechnology Srl avverrà mediante l'utilizzo di strumenti e
                    procedure idonei a garantirne la sicurezza e la riservatezza delle informazioni ed è effettuato
                    prevalentemente con l'ausilio di mezzi informatici e telematici, ma potrà essere effettuato anche
                    mediante supporti cartacei.
                </p>
            </div>

            <!-- Comunicazione dei Dati Section -->
            <div class="mt-4">
                <h2 class="mb-2 font-semibold">
                    COMUNICAZIONE DEI DATI PERSONALI
                </h2>
                <p class="mb-2">
                    I Suoi Dati Personali potranno essere comunicati alle seguenti categorie di destinatari:
                </p>

                <div class="space-y-4">
                    <!-- Category A -->
                    <div>
                        <p>
                            <span class="font-semibold">a)</span> persone fisiche autorizzate da Alltechnology Srl al
                            trattamento di dati personali ai sensi degli artt. 29 del Regolamento e 2-quaterdecies del
                            D.lgs. 196/2003 (c.d. "Codice Privacy"), in ragione dell'espletamento delle rispettive
                            mansioni lavorative (es. dipendenti e amministratori di sistema);
                        </p>
                    </div>

                    <!-- Category B -->
                    <div>
                        <p>
                            <span class="font-semibold">b)</span> soggetti che agiscono tipicamente in qualità di
                            responsabili del trattamento quali, a titolo esemplificativo e non esaustivo, installatori,
                            persone, società o studi professionali che prestano attività di assistenza e consulenza a
                            Alltechnology Srl in materia contabile, ammnistrativa, legale, tributaria e di recupero
                            crediti, società di assistenza stradale relativamente alla erogazione dei servizi oggetto
                            del Contratto;
                        </p>
                    </div>

                    <!-- Category C -->
                    <div>
                        <p>
                            <span class="font-semibold">c)</span> la compagnia assicurativa con cui Lei ha stipulato la
                            polizza Furto e Incendio;
                        </p>
                    </div>

                    <!-- Category D -->
                    <div>
                        <p>
                            <span class="font-semibold">d)</span> rispetto a questo trattamento, i dati puntuali
                            verranno comunicati alla compagnia limitatamente alle ipotesi in cui ciò sia necessario in
                            relazione alla gestione di un determinato evento (ad es., sinistro stradale, richiesta di
                            soccorso, furto o danneggiamento del veicolo);
                        </p>
                    </div>

                    <!-- Category E -->
                    <div>
                        <p>
                            <span class="font-semibold">e)</span> soggetti, enti o autorità pubbliche (giudiziarie, di
                            polizia ecc.), che agiscono nella qualità di autonomi titolari del trattamento, a cui sia
                            obbligatorio comunicare i Suoi Dati Personali in forza di disposizioni normative o di ordini
                            delle autorità.
                        </p>
                    </div>

                    <!-- Category F -->
                    <div>
                        <p>
                            <span class="font-semibold">f)</span> L'elenco completo e aggiornato dei destinatari dei
                            Suoi Dati Personali può essere richiesto al Titolare ai recapiti sottoindicati.
                        </p>
                    </div>
                </div>
            </div>


            <!-- Data Transfer Section -->
            <div class="mt-4">
                <h2 class="mb-2 font-semibold">
                    TRASFERIMENTI DEI DATI PERSONALI EXTRA SEE
                </h2>
                <div class="space-y-4">
                    <p>
                        Alcuni dei Suoi Dati Personali potrebbero essere condivisi con destinatari situati in un Paese
                        terzo al di fuori dello Spazio Economico Europeo ("SEE"). Il Titolare assicura che il
                        trattamento dei Suoi Dati Personali da parte di tali destinatari avviene nel rispetto del
                        Regolamento. In particolare, i trasferimenti si baseranno su una decisione di adeguatezza della
                        Commissione Europea o della sottoscrizione delle Clausole Contrattuali Tipo approvate dalla
                        Commissione Europea, eventualmente integrate con le misure suppletive previste dalle
                        Raccomandazioni dell'EDPB (01/2020), ovvero altro strumento di trasferimento previsto dal
                        Regolamento.
                    </p>
                    <p>
                        Maggiori informazioni sugli effettivi trasferimenti di Dati Personali verso Paesi terzi sono
                        disponibili presso il Titolare o il DPO scrivendo ai recapiti indicati nel paragrafo denominato
                        "contatti".
                    </p>
                </div>
            </div>


        </div>

        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 12 a 14
                </div>
            </div>
        </div>
    </div>
    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <div>
            <!-- Data Retention Section -->
            <div class="mt-4">
                <h2 class="mb-2 font-semibold">
                    PERIODO DI CONSERVAZIONE DEI DATI PERSONALI
                </h2>
                <div class="space-y-4">
                    <p>
                        I Suoi Dati Personali saranno conservati solo per il tempo necessario al perseguimento delle
                        finalità di trattamento sopra indicate, nel rispetto dei principi di minimizzazione e
                        limitazione della conservazione di cui all'art. 5, par. 1, lett. c) ed e) del Regolamento. In
                        generale, i Dati Personali saranno conservati per periodi di tempo diversi a seconda della
                        finalità perseguita da Alltechnology Srl, come di seguito specificato:
                    </p>

                    <ul class="ml-6 space-y-4">
                        <li>
                            <span class="font-semibold">Finalità di esecuzione del Contratto:</span> i dati di cui al
                            par. 2, lett. a) nonché i dati di cui al par. 2, lett. d) saranno conservati sui sistemi di
                            Alltechnology Srl per un periodo di 4 anni dalla cessazione del Contratto, a qualsiasi causa
                            dovuta e, comunque, sino a che sussistano obbligazioni o adempimenti connessi all'esecuzione
                            del contratto;
                        </li>
                        <li>
                            <span class="font-semibold">Finalità di compliance:</span> i dati di cui al par. 2, lett.
                            a) e b) saranno conservati da Alltechnology Srl per il periodo di tempo stabilito da
                            specifiche norme di legge o di regolamento (es. in materia fiscale e contabile) cui è
                            soggetto il Titolare (es. 5 e 10 anni dalla scadenza del contratto per l'assolvimento degli
                            obblighi di natura fiscale e contabile).
                        </li>
                        <li>
                            <span class="font-semibold">Finalità di prevenzione e contrasto di frodi e alle Finalità
                                difensive:</span> Alltechnology Srl conserverà i Dati Personali per il periodo di tempo
                            necessario all'accertamento di eventuali condotte illecite e, comunque, sino a quando
                            sussistano esigenze di tutela – in giudizio o nelle fasi precontenziose – dei diritti dei
                            Titolare.
                        </li>
                    </ul>

                    <p>
                        Alla scadenza dei termini di conservazione di cui al presente paragrafo, i Suoi Dati Personali
                        saranno, a seconda dei casi, anonimizzati in modo irreversibile o cancellati.
                    </p>
                </div>
            </div>

            <!-- Rights Section -->
            <div class="mt-4">
                <h2 class="mb-2 font-semibold text-center">
                    DIRITTI DELL'INTERESSATO
                </h2>

                <p class="mb-2">
                    In qualità di interessato può, in qualsiasi momento, esercitare i seguenti diritti:
                </p>

                <!-- Rights List -->
                <div class="space-y-4">
                    <!-- Access Right -->
                    <div>
                        <p>
                            <span class="font-semibold">a) Diritto di accesso (art. 15 del GDPR):</span>
                            Lei ha il diritto di ottenere la conferma circa l'esistenza o meno di un trattamento
                            concernente i Suoi Dati Personali nonché il diritto di ricevere ogni informazione relativa
                            al medesimo trattamento;
                        </p>
                    </div>

                    <!-- Rectification Right -->
                    <div>
                        <p>
                            <span class="font-semibold">b) Diritto alla rettifica (art. 16 del GDPR):</span>
                            Lei ha il diritto di ottenere la rettifica dei Suoi Dati Personali, qualora gli stessi siano
                            incompleti o inesatti;
                        </p>
                    </div>

                    <!-- Deletion Right -->
                    <div>
                        <p>
                            <span class="font-semibold">c) Diritto alla cancellazione (art. 17 del GDPR):</span>
                            in talune circostanze, Lei ha il diritto di ottenere la cancellazione dei Suoi Dati
                            Personali presenti all'interno degli archivi di Alltechnology Srl;
                        </p>
                    </div>

                    <!-- Processing Limitation Right -->
                    <div>
                        <p>
                            <span class="font-semibold">d) Diritto alla limitazione del trattamento (art. 18 del
                                GDPR):</span>
                            al verificarsi di talune condizioni, Lei ha il diritto di ottenere la limitazione del
                            trattamento dei Suoi Dati Personali;
                        </p>
                    </div>

                    <!-- Portability Right -->
                    <div>
                        <p>
                            <span class="font-semibold">e) Diritto alla portabilità (art. 20 del GDPR):</span>
                            Lei ha il diritto di ottenere, per i trattamenti basati sull'esecuzione del contratto o il
                            conferimento del consenso, il trasferimento dei Suoi Dati Personali verso un diverso
                            titolare del trattamento nonché il diritto di ottenere in un formato strutturato, di uso
                            comune e leggibile da dispositivo automatico i Dati Personali che La riguardano;
                        </p>
                    </div>

                    <!-- Opposition Right -->
                    <div>
                        <p>
                            <span class="font-semibold">f) Diritto di opposizione (art. 21 del GDPR):</span>
                            Lei ha il diritto di formulare una richiesta di opposizione al trattamento dei Suoi Dati
                            Personali nella quale dare evidenza delle ragioni che giustificano l'opposizione per i
                            trattamenti basati sul legittimo interesse. Il Titolare si riserva di valutare tale istanza,
                            che potrebbe non essere accettata nel caso sussistano motivi legittimi cogenti per procedere
                            al trattamento che prevalgano sui Suoi interessi, diritti e libertà;
                        </p>
                    </div>

                    <!-- Complaint Right -->
                    <div>
                        <p>
                            <span class="font-semibold">g) Diritto di proporre reclamo (art. 77 del GDPR):</span>
                            nel caso in cui ritenga che il trattamento che La riguarda violi la normativa in materia di
                            protezione dei dati personali e, in particolare, allorché il Titolare si rifiuti di
                            ottemperare ad una Sua richiesta, può proporre un reclamo all'Autorità di controllo dello
                            Stato membro in cui risiede abitualmente, lavora oppure del luogo ove si è verificata la
                            presunta violazione;
                        </p>
                    </div>

                    <!-- Jurisdictional Right -->
                    <div>
                        <p>
                            <span class="font-semibold">h) Diritto di adire le opportune sedi giurisdizionali (art. 79
                                del GDPR).</span>
                        </p>
                    </div>
                </div>

                <!-- Contacts Section -->
                <div class="mt-4">
                    <h2 class="mb-2 font-semibold">
                        CONTATTI
                    </h2>
                    <div class="space-y-4">
                        <p>
                            Potrà contattare Alltechnology Srl per qualsiasi chiarimento o necessità, ovvero per
                            esercitare i propri diritti di cui al paragrafo 8, inviando una e-mail all'indirizzo <span
                                class="font-semibold"><EMAIL></span>
                        </p>
                        <p>
                            Lei potrà contattare il responsabile della protezione dei dati (Data Protection Officer) di
                            Alltechnology Srl al seguente indirizzo: <span
                                class="font-semibold"><EMAIL></span>
                        </p>
                        <p>
                            Per i contatti nei confronti degli altri titolari può fare riferimento ai contatti indicati
                            nell'informativa ricevuta in relazione alla gestione dal rapporto assicurativo esistente.
                        </p>
                    </div>
                </div>
            </div>


        </div>

        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 13 a 14
                </div>
            </div>
        </div>
    </div>
    {{-- page break --}}
    <div style="page-break-after: always;"></div>

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">
        <div>
            <!-- Updates Section -->
            <div class="mt-4">
                <h2 class="mb-2 font-semibold">
                    MODIFICHE E AGGIORNAMENTI DELL'INFORMATIVA
                </h2>
                <div class="space-y-4">
                    <p>
                        Alltechnology Srl potrà modificare, integrare e/o aggiornare, in tutto o in parte, la
                        presente Informativa, anche in considerazione di eventuali modifiche legislative che
                        riguardino, in particolare, la normativa applicabile per la tutela dei dati personali.
                    </p>
                    <p>
                        Resta inteso che qualsiasi modifica, integrazione o aggiornamento Le verrà comunicato in
                        maniera tempestiva e puntuale tramite i mezzi elettronici o cartacei ritenuti più idonei. A
                        questo proposito, La invitiamo a verificare la data di aggiornamento dell'Informativa.
                    </p>
                    <p class="font-semibold">
                        Il cliente, con la sottoscrizione del presente contratto, dichiara di aver ricevuto copia
                        integrale del medesimo, Certificato di installazione e dell'appendice privacy, che di esso
                        costituiscono parte integrante e sostanziale.
                    </p>
                    <p class="font-semibold">
                        Ai sensi di legge,
                    </p>
                </div>
            </div>


            <div class="space-y-6">
                <!-- First consent section -->
                <div>
                    <div style="margin-top: 10px;">
                        <div style="display: inline-block; margin-right: 20px;">
                            @if ($contract->status == 'active' && $contract->responded_at)
                                <div
                                    style="display: inline-block; width: 16px; height: 16px; border: 1px solid black; vertical-align: middle;text-align: center;">
                                    X
                                </div>
                            @else
                                <div
                                    style="display: inline-block; width: 16px; height: 16px; border: 1px solid black; vertical-align: middle;">
                                </div>
                            @endif
                            <span style="vertical-align: middle;">Accetta</span>
                        </div>

                        <div style="display: inline-block;">
                            <div
                                style="display: inline-block; width: 16px; height: 16px; border: 1px solid black; vertical-align: middle;">
                            </div>
                            <span style="vertical-align: middle;">Non Accetta</span>
                        </div>
                    </div>

                    <p class="mb-2">
                        che i propri Dati Personali saranno trattati dalla Società, anche con l'ausilio di mezzi
                        elettronici e/o automatizzati, per finalità funzionali
                        alla nostra attività quali: la commercializzazione di prodotti e servizi, l'invio di materiale
                        pubblicitario informativo/promozionale e di
                        aggiornamenti su iniziative ed offerte, ricerche di mercato, analisi economiche e statistiche
                    </p>
                    <div class="flex items-center justify-start p-2 mt-4 border border-black"
                        style="width: fit-content">
                        @if ($contract->status == 'active' && $contract->responded_at)
                            Contratto accettato in
                            @if ($contract->responded_at)
                                data
                                {{ \Carbon\Carbon::parse($contract->responded_at)->format('d/m/Y') }}
                                alle
                                {{ \Carbon\Carbon::parse($contract->responded_at)->format('H:i') }}
                            @else
                                data
                                {{ now()->format('d/m/Y') }}
                                alle
                                {{ now()->format('H:i') }}
                            @endif

                            dal numero {{ $contract->client?->phone_number ?? 'N/A' }}
                        @else
                            Contratto accettato in ...... data ...... alle ...... dal numero ......
                        @endif
                    </div>



                </div>

                <!-- Second consent section -->
                <div>
                    <div style="margin-top: 10px;">
                        <div style="display: inline-block; margin-right: 20px;">
                            @if ($contract->status == 'active' && $contract->responded_at)
                                <div
                                    style="display: inline-block; width: 16px; height: 16px; border: 1px solid black; vertical-align: middle;text-align: center;">
                                    X
                                </div>
                            @else
                                <div
                                    style="display: inline-block; width: 16px; height: 16px; border: 1px solid black; vertical-align: middle;">
                                </div>
                            @endif
                            <span style="vertical-align: middle;">Accetta</span>
                        </div>

                        <div style="display: inline-block;">
                            <div
                                style="display: inline-block; width: 16px; height: 16px; border: 1px solid black; vertical-align: middle;">
                            </div>
                            <span style="vertical-align: middle;">Non Accetta</span>
                        </div>
                    </div>
                    <p class="mb-2">
                        che i propri Dati Personali saranno trattati da nostre Società Partner, anche con l'ausilio di
                        mezzi elettronici e/o automatizzati, per finalità
                        funzionali alla nostra attività quali: la commercializzazione di prodotti e servizi, l'invio di
                        materiale pubblicitario/informativo/promozionale
                        e di aggiornamenti su iniziative ed offerte, ricerche di mercato, analisi economiche e
                        statistiche
                    </p>
                    <div class="flex items-center justify-start p-2 mt-4 border border-black"
                        style="width: fit-content">
                        @if ($contract->status == 'active' && $contract->responded_at)
                            Contratto accettato in
                            @if ($contract->responded_at)
                                data
                                {{ \Carbon\Carbon::parse($contract->responded_at)->format('d/m/Y') }}
                                alle
                                {{ \Carbon\Carbon::parse($contract->responded_at)->format('H:i') }}
                            @else
                                data
                                {{ now()->format('d/m/Y') }}
                                alle
                                {{ now()->format('H:i') }}
                            @endif

                            dal numero {{ $contract->client?->phone_number ?? 'N/A' }}
                        @else
                            Contratto accettato in ...... data ...... alle ...... dal numero ......
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 14 a 14
                </div>
            </div>
        </div>
    </div>
    @include('pdf.contract-css')
</body>

</html>
