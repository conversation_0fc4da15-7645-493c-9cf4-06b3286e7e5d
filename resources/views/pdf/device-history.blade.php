<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Device History Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 12px;
        }

        .container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #450099;
            margin-bottom: 20px;
            padding-bottom: 10px;
        }

        .header .company-info {
            text-align: right;
            font-size: 12px;
            color: #555;
        }

        h2 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 22px;
            color: #450099;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 10px;
            overflow: hidden;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px 10px;
            text-align: center;
            font-size: 10px;
        }

        th {
            background-color: #450099;
            color: #ffffff;
            font-weight: normal;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f1f1f1;
        }

        a {
            color: #450099;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        .info-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            font-size: 14px;
        }

        .info-section div {
            display: flex;
            align-items: center;
        }

        .info-section span.label {
            color: #450099;
            font-weight: bold;
            margin-right: 5px;
        }

        .info-section span.value {
            color: #000;
        }

        /* Image section on a separate page */
        .image-section-page {
            page-break-before: always;
            /* Forces the image to a new page */
            text-align: center;
            margin-top: 20px;
        }

        .image-section-page img {
            width: 100%;
            /* Adjust to the full width of A4 */
            max-width: 100%;
            /* Ensures it's not too large */
            height: auto;
            border-radius: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="MeMove" width="150">
            </div>
            <div class="company-info">
                <p>Alltechnology Srl<br>Via Ogliara, 13<br>84135 Salerno (SA)<br>PI 06225780656</p>
                <p><a href="mailto:<EMAIL>"><EMAIL></a><br>089 9341047</p>
            </div>
        </div>
        <h2>Rapporto Storia Dispositivo</h2>
        <div class="info-section">
            <div>
                <span class="label">Targa:</span>
                <span class="value">{{ $plate }}</span>
            </div>
        </div>
        <table>
            <thead>
                <tr>
                    <th>Timestamp</th>
                    <th>Movimento</th>
                    <th>Accensione</th>
                    <th>Velocità</th>
                    <th>Nome della Strada</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($device_history as $history)
                    <tr>
                        <td>{{ $history['timestamp'] }}</td>
                        <td>{{ $history['movement'] }}</td>
                        <td>{{ $history['ignition'] }}</td>
                        <td>{{ $history['speed'] }}</td>
                        <td>{{ $history['address'] }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
        <div class="info-section">
            <div>
                <span class="label">Distanza Totale Percorsa:</span>
                <span class="value">{{ $total_distance ? $total_distance . 'km' : 0 }}</span>
            </div>
        </div>
    </div>

    <!-- Image on a separate page -->
    <div class="image-section-page">
        <img src="{{ $imagePath ?? '' }}" alt="Map Image" style="width:100%; max-height: 300px; margin-bottom: 20px;">

    </div>
</body>

</html>
