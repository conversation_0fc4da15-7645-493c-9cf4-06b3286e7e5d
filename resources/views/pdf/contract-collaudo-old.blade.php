<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Contract</title>

    <style>
        @font-face {
            font-family: 'Roboto';
            src: url('{{ storage_path('fonts/Roboto-Regular.ttf') }}') format('truetype');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'Roboto';
            src: url('{{ storage_path('fonts/Roboto-Bold.ttf') }}') format('truetype');
            font-weight: bold;
            font-style: normal;
        }

        body {
            font-family: 'Roboto', sans-serif;
        }
    </style>
</head>

<body>

    <div class="page" title="Page 11">

        <div class="layoutArea">
            <div class="column">

                <p style="text-align: center;">
                    <span style=" font-size: 10pt"><strong>CERTIFICATO DI INSTALLAZIONE E
                            COLLAUDO</strong></span>
                </p>
                <div style="display: block;">
                    <div style="display: inline-block; width: 49%; vertical-align: top;">

                        <p>
                            <span style=" font-size: 10pt"><strong>Dati del Cliente</strong></span>
                        </p>

                        @if ($contract->client->last_name)
                            <p style="width: 50%;">
                                <span style=" font-size: 10pt">Cognome:
                                    {{ $contract->client->user->username ?? '' }}</span>
                            </p>
                        @endif

                        <p>
                            <span style=" font-size: 10pt">Nome:
                                {{ $contract->client->user->name ?? '' }}</span>
                        </p>

                        <div style="display: flex;align-items: center;">
                            <p>
                                <span style=" font-size: 10pt"><strong>Dati del Servizio
                                        "MeMove"</strong></span>
                            </p>
                            <p style="width: 50%;">
                                <span style=" font-size: 10pt">IMEI Periferica</span>
                            </p>


                            <pre><span style="font-family:'Helvetica';font-size:10.000000pt;">{{ $contract->device->imei ?? '' }} </span></pre>

                            <p style="width: 50%;">
                                <span style=" font-size: 10pt">Durata:
                                    {{ $contract->duration ?? '' }}
                                    mesi</span>
                            </p>
                            <p>
                                <span style=" font-size: 10pt">Periodo:
                                    {{ $contract->start_date ? \Carbon\Carbon::parse($contract->start_date)->format('d/m/Y') : '' }}
                                    -
                                    {{ $contract->end_date ? \Carbon\Carbon::parse($contract->end_date)->format('d/m/Y') : '' }}</span>
                            </p>
                        </div>
                    </div>

                    <div style="display: inline-block; width: 48%; vertical-align: top;">
                        <p>
                            <span style=" font-size: 10pt"><strong>Dati del Veicolo</strong></span>
                        </p>
                        <p style="width: 50%;">
                            <span style=" font-size: 10pt">Veicolo:
                                {{ $contract->vehicle_model ?? '' }}</span>
                        </p>

                        <p>
                            <span style=" font-size: 10pt">Colore:
                                {{ $contract->vehicle_color ?? '' }}</span>
                        </p>

                        <p style="width: 50%;">
                            <span style=" font-size: 10pt">Targa:
                                {{ $contract->vehicle_number_plate ?? '' }}</span>
                        </p>
                        <p>
                            <span style=" font-size: 10pt">Telaio:
                                {{ $contract->frame ?? '' }}</span>
                        </p>

                        <p style="width: 50%;">
                            <span style=" font-size: 10pt">Data immatricolazione:
                                {{ $contract->vehicle_registration_date ? \Carbon\Carbon::parse($contract->vehicle_registration_date)->format('d/m/Y') : '' }}</span>
                        </p>
                        <p>
                            <span style=" font-size: 10pt">Km:
                                {{ $contract->vehicle_km ?? '' }}</span>
                        </p>


                    </div>

                </div>




                <div style="display: block;">

                    <div style="display: inline-block; width: 49%; vertical-align: top;">

                        <p>
                            <span style=" font-size: 10pt"><strong>Servizi internet</strong></span>
                        </p>
                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Sito con accesso dedicato</span>

                            <span>X</span>
                        </p>

                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Applicazione iOS/Android </span>
                            <span>
                                X
                            </span>
                        </p>

                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Esportazione di report</span>

                            <span>X</span>
                        </p>

                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Ricostruzione viaggi e soste</span>
                            <span>
                                X
                            </span>
                        </p>


                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Geofence automatica</span>

                            <span>X</span>
                        </p>

                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Geofence manuale</span>
                            <span>
                                X
                            </span>
                        </p>
                    </div>
                    <div style="display: inline-block; width: 48%; vertical-align: top;">
                        <p>
                            <span style=" font-size: 10pt"><strong>Funzioni</strong></span>
                        </p>
                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Positivo chiave</span>

                            <span>X</span>
                        </p>

                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Blocco motorino avviamento</span>
                            <span>
                                @if ($contract->starter_motor_block == 'Yes')
                                    X
                                @endif
                            </span>
                        </p>
                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Antifurto</span>

                            <span>X</span>
                        </p>

                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Centrale operativa 24/7 </span>
                            <span>
                                X
                            </span>
                        </p>


                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Anti Jammer - Anti sollevamento</span>

                            <span>X</span>
                        </p>

                        <p
                            style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-right: 20px;">
                            <span style=" font-size: 10pt">Stacco cavi - Stacco batteria</span>
                            <span>
                                X
                            </span>
                        </p>
                    </div>
                </div>


                {{-- @if (auth()->check() && auth()->user()->role == 'admin')
                    <p>
                        <span style=" font-size: 10pt"><strong>Immagine di installazione</strong></span>
                    </p>

                    @if ($contract->installation_image)
                        <div style="margin: 10px 0">
                            <img style="width: 400px;"
                                src="{{ public_path('storage/' . $contract->installation_image) }}" alt="">
                        </div>
                    @endif
                @endif --}}
            </div>


        </div>
        <div class="layoutArea">
            <div class="column" style=" font-size: 10pt">
                <p>
                    Alltechnology Srl con la presente dichiara che:&nbsp;
                </p>
                <p>
                    • Il collaudo dell'hardware/apparato satellitare installato sul suddetto veicolo ha consentito
                    di
                    verificarne il regolare e perfetto funzionamento.&nbsp;
                </p>
                <p>
                    • È stato attivato il servizio di telesorveglianza satellitare/centrale operativa per il periodo
                    sopra
                    indicato.&nbsp;
                </p>
                <p>
                    • Scarichi l'applicazione MeMove per smartphone del Play Store o Apple Store per accedere
                    all'area
                    riservata, in modo da tenersi aggiornato in qualsiasi momento e avere tutti i servizi a portata
                    di
                    mano.&nbsp;
                </p>
                <p>
                    • La Centrale Operativa è a sua disposizione 24 ore al giorno, 7 giorni su 7, per intervenire in
                    qualsiasi momento e avvisarla in caso di anomalie.
                </p>
            </div>
        </div>
    </div>

</body>

</html>
