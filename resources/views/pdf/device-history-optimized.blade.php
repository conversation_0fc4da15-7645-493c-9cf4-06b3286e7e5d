<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Device History Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 10px; /* Smaller font for large datasets */
        }

        .container {
            max-width: 100%;
            margin: 10px;
            padding: 15px;
            background: #ffffff;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #450099;
            margin-bottom: 15px;
            padding-bottom: 8px;
        }

        .header .company-info {
            text-align: right;
            font-size: 10px;
            color: #555;
        }

        h2 {
            text-align: center;
            margin-bottom: 15px;
            font-size: 18px;
            color: #450099;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 8px; /* Very small font for large datasets */
            page-break-inside: auto;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 4px 6px; /* Reduced padding */
            text-align: center;
            word-wrap: break-word;
            max-width: 120px; /* Limit column width */
        }

        th {
            background-color: #450099;
            color: #ffffff;
            font-weight: normal;
            font-size: 9px;
        }

        tr {
            page-break-inside: avoid;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .info-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            font-size: 12px;
        }

        .info-section div {
            display: flex;
            align-items: center;
        }

        .info-section span.label {
            color: #450099;
            font-weight: bold;
            margin-right: 5px;
        }

        .info-section span.value {
            color: #000;
        }

        .summary-section {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #450099;
        }

        .summary-section h3 {
            margin: 0 0 10px 0;
            color: #450099;
            font-size: 14px;
            text-align:center;

        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            font-size: 11px;
        }

    

        .summary-item .label {
            font-weight: bold;
            color: #450099;
        }

        .summary-item .value {
            font-size: 12px;
            color: #333;
        }

        /* Optimize for large tables */
        .large-dataset {
            font-size: 8px;
        }

        .large-dataset th,
        .large-dataset td {
            padding: 2px 4px;
        }

        /* Image section optimization */
        .image-section-page {
            page-break-before: always;
            text-align: center;
            margin-top: 20px;
        }

        .image-section-page img {
            width: 100%;
            max-width: 100%;
            height: auto;
            max-height: 400px;
            object-fit: contain;
        }

        /* Address column optimization */
        .address-cell {
            font-size: 7px;
            max-width: 100px;
            overflow: hidden;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="MeMove" width="120">
            </div>
            <div class="company-info">
                <p>Alltechnology Srl<br>Via Ogliara, 13<br>84135 Salerno (SA)<br>PI 06225780656</p>
                <p><a href="mailto:<EMAIL>"><EMAIL></a><br>089 9341047</p>
            </div>
        </div>
        
        <h2>Rapporto Storia Dispositivo</h2>
        
        <div class="info-section">
            <div>
                <span class="label">Targa:</span>
                <span class="value">{{ $plate }}</span>
            </div>
            <div>
                <span class="label">Data Esportazione:</span>
                <span class="value">{{ date('d/m/Y H:i') }}</span>
            </div>
        </div>

        <!-- Summary Section for Large Datasets -->
        {{-- @if(isset($record_count) && $record_count > 1000) --}}
        <div class="summary-section">
            <h3>Riepilogo Dati</h3>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="label">Totale Record</div>
                    <div class="value">{{ number_format($record_count) }}</div>
                </div>
                <div class="summary-item">
                    <div class="label">Distanza Totale</div>
                    <div class="value">{{ $total_distance ? number_format($total_distance, 2) . ' km' : '0 km' }}</div>
                </div>
                <div class="summary-item">
                    <div class="label">Periodo</div>
                    <div class="value">
                        @if(count($device_history) > 0)
                            {{ $device_history[0]['timestamp'] ?? 'N/A' }} - {{ $device_history[count($device_history)-1]['timestamp'] ?? 'N/A' }}
                        @else
                            N/A
                        @endif
                    </div>
                </div>
            </div>
        </div>
        {{-- @endif --}}

        <table class="{{ isset($record_count) && $record_count > 2000 ? 'large-dataset' : '' }}">
            <thead>
                <tr>
                    <th>Timestamp</th>
                    <th>Movimento</th>
                    <th>Accensione</th>
                    <th>Velocità</th>
                    <th>Indirizzo</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($device_history as $index => $history)
                    <tr>
                        <td>{{ $history['timestamp'] }}</td>
                        <td>{{ $history['movement'] }}</td>
                        <td>{{ $history['ignition'] }}</td>
                        <td>{{ $history['speed'] }}</td>
                        <td class="address-cell" title="{{ $history['address'] }}">{{ $history['address'] }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>

        <div class="info-section">
            <div>
                <span class="label">Distanza Totale Percorsa:</span>
                <span class="value">{{ $total_distance ? number_format($total_distance, 2) . ' km' : '0 km' }}</span>
            </div>
            @if(isset($record_count))
            <div>
                <span class="label">Totale Record:</span>
                <span class="value">{{ number_format($record_count) }}</span>
            </div>
            @endif
        </div>
    </div>

    <!-- Image on a separate page -->
    @if(isset($imagePath) && $imagePath)
    <div class="image-section-page">
        <h3 style="color: #450099; margin-bottom: 20px;">Mappa del Percorso</h3>
        <img src="{{ $imagePath }}" alt="Map Image">
    </div>
    @endif
</body>

</html>
