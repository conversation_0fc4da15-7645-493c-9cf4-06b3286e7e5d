<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Contract</title>

    <style>
        @font-face {
            font-family: 'Roboto';
            src: url('{{ storage_path('fonts/Roboto-Regular.ttf') }}') format('truetype');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'Roboto';
            src: url('{{ storage_path('fonts/Roboto-Bold.ttf') }}') format('truetype');
            font-weight: bold;
            font-style: normal;
        }

        body {
            font-family: 'Roboto', sans-serif;
        }

        .text-10px {
            font-size: 10px !important;
        }

        .w-1-4 {
            width: 25%;
        }
    </style>
</head>

<body class="leading-4 text-10px">

    <table width="100%">
        <tr>
            <td width="50%">
                <img src="{{ public_path('assets/images/alltech-logo.png') }}" alt="logo" style="width: 150px;">
            </td>
            <td width="50%" align="right">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="logo" style="width: 150px;">
            </td>
        </tr>
    </table>


    <div class="relative mt-5">

        <div>
            <h2 class="font-medium text-center">
                CERTIFICATO DI INSTALLAZIONE E COLLAUDO
            </h2>

            <!-- Dati del Cliente -->
            <h3 class="mt-3 font-medium text-center">Dati del Cliente</h3>
            <table class="w-full mt-4 border-collapse">
                <tr>
                    @if ($contract->client?->type == 'company')
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Ragione
                            Sociale</td>
                        <td class="p-1 border border-black w-1-4">
                            {{ $contract->client?->user?->name ?? '' }}
                        </td>
                    @else
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Cognome</td>
                        <td class="p-1 border border-black w-1-4">
                            {{ $contract->client?->last_name ?? '' }}
                        </td>
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Nome</td>
                        <td class="p-1 border border-black w-1-4">
                            {{ $contract->client?->user?->name ?? '' }}
                        </td>
                    @endif
                </tr>
            </table>

            <!-- Dati del Veicolo -->
            <h3 class="mt-3 font-medium text-center">Dati del Veicolo</h3>
            <table class="w-full mt-4 border-collapse">
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Tipo di veicolo
                    </td>
                    <td class="p-1 border border-black w-1-4">
                        @lang('messages.' . $contract->verification_vehicle_type ?? '')
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Km iniziali</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_km ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Marca</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_brand ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Modello</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_model ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Colore</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_color ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Data
                        immatricolazione</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_registration_date ? \Carbon\Carbon::parse($contract->vehicle_registration_date)->format('d/m/Y') : '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Targa</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->vehicle_number_plate ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Telaio</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->frame ?? '' }}
                    </td>
                </tr>
            </table>

            <!-- Dati del Collaudo -->
            <h3 class="mt-3 font-medium text-center">Dati del Collaudo</h3>
            <table class="w-full mt-4 border-collapse">
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">IMEI Periferica
                    </td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->device?->imei ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Durata in mesi
                    </td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->duration ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Data inizio</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->start_date ? \Carbon\Carbon::parse($contract->start_date)->format('d/m/Y') : '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Data fine</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->end_date ? \Carbon\Carbon::parse($contract->end_date)->format('d/m/Y') : '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Data Collaudo
                    </td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->created_at ? \Carbon\Carbon::parse($contract->created_at)->format('d/m/Y') : '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Esito</td>
                    <td class="p-1 border border-black w-1-4">
                        @if ($contract->status == 'active')
                            Positivo
                        @else
                            {{ $contract->status ? __('messages.' . $contract->status) : '' }}
                        @endif
                    </td>
                </tr>
            </table>

            <!-- Dati del Rivenditore/Installatore -->
            <h3 class="mt-3 font-medium text-center">Dati del Rivenditore/Installatore</h3>
            <table class="w-full mt-4 border-collapse">
                <tr>
                    @if ($contract->dealer?->type == 'company')
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Ragione
                            Sociale</td>
                        <td class="p-1 border border-black w-1-4" colspan="3">
                            {{ $contract->dealer?->user?->name ?? '' }}
                        </td>
                    @else
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Cognome</td>
                        <td class="p-1 border border-black w-1-4">
                            {{ $contract->dealer?->last_name ?? '' }}
                        </td>
                        <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Nome</td>
                        <td class="p-1 border border-black w-1-4">
                            {{ $contract->dealer?->user?->name ?? '' }}
                        </td>
                    @endif
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Indirizzo</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->address ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Comune</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->municipality ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Cap</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->zip_code ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Provincia</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->province ?? '' }}
                    </td>
                </tr>
                <tr>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">E-mail</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->user?->email ?? '' }}
                    </td>
                    <td class="p-1 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Cellulare</td>
                    <td class="p-1 border border-black w-1-4">
                        {{ $contract->dealer?->phone_number ?? '' }}
                    </td>
                </tr>
            </table>

            <!-- Servizi e Funzioni -->
            <h3 class="mt-3 font-medium text-center">Servizi e Funzioni</h3>
            <table class="w-full mt-4 border-collapse">
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Area riservata
                        Web</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Area riservata
                        App</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Localizzazione
                        Internet</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Visualizzazione
                        storico</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Geofence
                        Automatica</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Geofence Manuale
                    </td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Positivo Sotto
                        Chiave</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Blocco
                        Avviamento</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">
                        @if ($contract->starter_motor_block == 'Yes')
                            X
                        @endif
                    </td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Centrale
                        Operativa 24/7</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Traffico dati
                        SIM</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Allarme Jamming
                    </td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Allarme
                        Sollevamento</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
                <tr>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Allarme Traino
                    </td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                    <td class="p-2 font-semibold bg-yellow-500 border border-black w-1-4 text-primary">Allarme Stacco
                        Batteria</td>
                    <td class="p-2 text-center border border-black w-1-4 text-primary">X</td>
                </tr>
            </table>


        </div>



        <div class="absolute bottom-10 w-full text-xs text-center text-primary text-[10px] font-medium">
            <div class="relative ">
                <p>
                    Alltechnology Srl <br>
                    Via Ogliara, 13 84135 Salerno (SA) <br>
                    P. Iva 06225780656 <br>
                    <EMAIL> – <EMAIL> <br>
                </p>

                <div class="absolute top-0 right-0">
                    Pag. 1 a 1
                </div>
            </div>
        </div>
    </div>



    @include('pdf.contract-css')
</body>

</html>
