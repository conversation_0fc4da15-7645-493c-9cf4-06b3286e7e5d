<div class="relative">
    <label
        class="flex items-center flex-shrink-0 max-w-xs px-3 py-2 overflow-hidden bg-white rounded-full md:w-full md:size-auto size-8">
        <img class="cursor-pointer size-4" src="{{ asset('assets/images/search.svg') }}" alt="search">
        <input type="search" wire:model="search" placeholder="{{ __('messages.search') }}"
            class="w-full text-sm outline-none placeholder:text-lightGray placeholder:text-sm placeholder:font-light ms-3 text-primary md:block">
    </label>

    @if (!empty($devices))
        <ul
            class="absolute bottom-[120%] left-0 mt-2 w-full bg-white rounded-lg min-w-max shadow-lg md:block text-primary font-medium text-sm max-h-[15rem] overflow-y-auto">
            @foreach ($devices as $device)
                <li class="px-3 py-2 cursor-pointer search-result"
                    wire:click="$emit('selectDevice', '{{ $device->imei }}')">
                    <strong>IMEI:</strong> {{ $device->imei }} <br>
                    <small>@lang('messages.plate'): {{ $device->number_plate ?? 'N/A' }}</small>
                </li>
            @endforeach
        </ul>
    @elseif(strlen($search) > 0)
        <div
            class="absolute bottom-[120%] left-0 mt-2 w-full bg-white rounded-lg shadow-lg md:block text-primary font-medium text-sm">
            <div class="px-3 py-2">No results found</div>
        </div>
    @endif
</div>
