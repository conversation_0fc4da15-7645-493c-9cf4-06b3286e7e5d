<div class="flex items-center justify-end mt-10 text-textPrimary">
    @if ($paginator->hasPages())
        <nav class="flex items-center gap-x-1" aria-label="Pagination Navigation">
            <!-- Previous Button -->
            @if ($paginator->onFirstPage())
                <button type="button"
                    class="min-h-[28px] min-w-[28px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-xs rounded-lg text-gray-400 bg-gray-200 cursor-not-allowed"
                    aria-label="Previous" disabled>
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                    <span class="sr-only">Previous</span>
                </button>
            @else
                <button wire:click="previousPage" wire:loading.attr="disabled" rel="prev"
                    class="min-h-[28px] min-w-[28px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-xs rounded-lg text-gray-800 hover:bg-secondary focus:outline-none focus:bg-secondary">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                    <span class="sr-only">Previous</span>
                </button>
            @endif

            <!-- Pagination Links -->
            <div class="flex flex-wrap items-center gap-x-1 ">
                @foreach ($elements as $element)
                    @if (is_string($element))
                        <button type="button"
                            class="min-h-[28px] min-w-[28px] flex justify-center items-center text-gray-400 cursor-not-allowed bg-gray-200 py-2 px-3 text-xs rounded-lg"
                            disabled>{{ $element }}</button>
                    @endif

                    @if (is_array($element))
                        @foreach ($element as $page => $url)
                            @if ($page == $paginator->currentPage())
                                <button type="button"
                                    class="min-h-[28px] min-w-[28px] flex justify-center items-center bg-primary text-white py-2 px-3 text-xs rounded-lg focus:outline-none hover:bg-gray-300"
                                    aria-current="page">{{ $page }}</button>
                            @else
                                <button type="button"
                                    class="min-h-[28px] min-w-[28px] flex justify-center items-center text-gray-800 hover:bg-secondary py-2 px-3 text-xs rounded-lg focus:outline-none"
                                    wire:click="gotoPage({{ $page }})">{{ $page }}</button>
                            @endif
                        @endforeach
                    @endif
                @endforeach
            </div>

            <!-- Next Button -->
            @if ($paginator->onLastPage())
                <button type="button"
                    class="min-h-[28px] min-w-[28px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-xs rounded-lg text-gray-400 bg-gray-200 cursor-not-allowed"
                    aria-label="Next" disabled>
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                    <span class="sr-only">Next</span>
                </button>
            @else
                <button wire:click="nextPage" wire:loading.attr="disabled" rel="next"
                    class="min-h-[28px] min-w-[28px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-xs rounded-lg text-gray-800 hover:bg-secondary focus:outline-none focus:bg-secondary">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                    <span class="sr-only">Next</span>
                </button>
            @endif
        </nav>
    @endif
</div>
