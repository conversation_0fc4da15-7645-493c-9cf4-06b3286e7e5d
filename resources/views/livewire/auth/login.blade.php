    <main class="grid min-h-screen gap-20 font-roboto lg:grid-cols-2">
        <!-- left section -->
        <div wire:ignore
            class="bg-[url('/assets/images/login-bg.png')!important] bg-cover bg-center bg-no-repeat h-full lg:flex flex-col items-center justify-center hidden min-h-screen">

            <img class="h-16" src="{{ asset('assets/images/logo-white.svg') }}" alt="logo">

            <p class="mt-10 text-lg font-medium text-center text-white">
                {{ __('messages.efficient_management') }}
            </p>

            <div class="flex flex-wrap justify-center gap-3 mt-5">
                <a target="_blank"
                    href="https://play.google.com/store/apps/details?id=com.rixxsol.memove&pcampaignid=web_share">
                    <div class="flex items-center px-4 py-2 mx-2 bg-white border rounded-lg w-52">
                        <svg class="size-8" xmlns="http://www.w3.org/2000/svg" version="1.1"
                            xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" viewBox="0 0 511.999 511.999"
                            style="enable-background:new 0 0 512 512" xml:space="preserve" class="">
                            <g>
                                <path
                                    d="M382.369 175.623A775951.446 775951.446 0 0 1 79.355 6.028C69.372-.565 57.886-1.429 47.962 1.93l254.05 254.05 80.357-80.357z"
                                    style="" fill="#32bbff" data-original="#32bbff" class=""></path>
                                <path
                                    d="M47.962 1.93c-1.86.63-3.67 1.39-5.401 2.308C31.602 10.166 23.549 21.573 23.549 36v439.96c0 14.427 8.052 25.834 19.012 31.761a36.954 36.954 0 0 0 5.395 2.314L302.012 255.98 47.962 1.93z"
                                    style="" fill="#32bbff" data-original="#32bbff" class=""></path>
                                <path
                                    d="M302.012 255.98 47.956 510.035c9.927 3.384 21.413 2.586 31.399-4.103 143.598-80.41 237.986-133.196 298.152-166.746l4.938-2.772-80.433-80.434z"
                                    style="" fill="#32bbff" data-original="#32bbff" class=""></path>
                                <path
                                    d="M23.549 255.98v219.98c0 14.427 8.052 25.834 19.012 31.761a36.954 36.954 0 0 0 5.395 2.314L302.012 255.98H23.549z"
                                    style="" fill="#2c9fd9" data-original="#2c9fd9"></path>
                                <path
                                    d="M79.355 6.028C67.5-1.8 53.52-1.577 42.561 4.239l255.595 255.596 84.212-84.212A786009.366 786009.366 0 0 1 79.355 6.028z"
                                    style="" fill="#29cc5e" data-original="#29cc5e"></path>
                                <path
                                    d="M298.158 252.126 42.561 507.721c10.96 5.815 24.939 6.151 36.794-1.789 143.598-80.41 237.986-133.196 298.152-166.746l4.938-2.772-84.287-84.288z"
                                    style="" fill="#d93f21" data-original="#d93f21"></path>
                                <path
                                    d="M488.45 255.98c0-12.19-6.151-24.492-18.342-31.314 0 0-22.799-12.721-92.682-51.809l-83.123 83.123 83.204 83.205c69.116-38.807 92.6-51.892 92.6-51.892 12.192-6.821 18.343-19.123 18.343-31.313z"
                                    style="" fill="#ffd500" data-original="#ffd500"></path>
                                <path
                                    d="M470.108 287.294c12.191-6.822 18.342-19.124 18.342-31.314H294.303l83.204 83.205c69.117-38.806 92.601-51.891 92.601-51.891z"
                                    style="" fill="#ffaa00" data-original="#ffaa00"></path>
                            </g>
                        </svg>
                        <div class="ml-3 text-left">
                            <p class='text-xs text-black'>Scarica su </p>
                            <p class="text-sm md:text-base"> Google Play Store </p>
                        </div>
                    </div>
                </a>
                <a target="_blank" href="https://apps.apple.com/in/app/memove-alltech/id6740730389">
                    <div class="flex items-center px-4 py-2 mx-2 bg-white border rounded-lg w-52">
                        <svg xmlns="http://www.w3.org/2000/svg" version="1.1"
                            xmlns:xlink="http://www.w3.org/1999/xlink" class="size-8" x="0" y="0"
                            viewBox="0 0 22.773 22.773" style="enable-background:new 0 0 512 512" xml:space="preserve"
                            class="">
                            <g>
                                <path
                                    d="M15.769 0h.162c.13 1.606-.483 2.806-1.228 3.675-.731.863-1.732 1.7-3.351 1.573-.108-1.583.506-2.694 1.25-3.561C13.292.879 14.557.16 15.769 0zM20.67 16.716v.045c-.455 1.378-1.104 2.559-1.896 3.655-.723.995-1.609 2.334-3.191 2.334-1.367 0-2.275-.879-3.676-.903-1.482-.024-2.297.735-3.652.926h-.462c-.995-.144-1.798-.932-2.383-1.642-1.725-2.098-3.058-4.808-3.306-8.276v-1.019c.105-2.482 1.311-4.5 2.914-5.478.846-.52 2.009-.963 3.304-.765.555.086 1.122.276 1.619.464.471.181 1.06.502 1.618.485.378-.011.754-.208 1.135-.347 1.116-.403 2.21-.865 3.652-.648 1.733.262 2.963 1.032 3.723 2.22-1.466.933-2.625 2.339-2.427 4.74.176 2.181 1.444 3.457 3.028 4.209z"
                                    fill="#000000" opacity="1" data-original="#000000" class=""></path>
                            </g>
                        </svg>
                        <div class="ml-3 text-left">
                            <p class='text-xs text-black'>Scarica su </p>
                            <p class="text-sm md:text-base"> Apple Store </p>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- right section -->
        <div class="p-10 lg:p-20">

            <div class="absolute flex items-center justify-end md:top-10 md:right-10 top-5 right-5">
                <livewire:language-switcher color="primary" position="top" />
            </div>

            <div class="flex flex-col items-center justify-center text-center">
                <img class="h-12" src="{{ asset('assets/images/logo.svg') }}" alt="logo">

                <h1 class="md:text-3xl text-2xl font-semibold mt-10 text-primary tracking-wide [word-spacing:1px]">
                    {{ __('messages.welcome_back') }}
                </h1>

                <p class="mt-4 text-sm font-light text-[#465686] tracking-wide">
                    @lang('messages.manage_monitor')
                    @lang('messages.track_real_time')
                </p>
            </div>


            <!-- login form -->
            <form class="mt-10">

                <!-- email -->
                <fieldset>
                    <label for="email"
                        class="text-sm font-medium text-primary">{{ __('messages.login_email_label') }}</label>
                    <input type="text" wire:model.blur="username_or_email" id="email"
                        class="block w-full border-extraLightGray border placeholder:text-lightGray placeholder:text-sm placeholder:font-light rounded-lg mt-2 px-3 py-2 outline-none focus:border-primary focus:border-[1.3px] transition-all duration-300 @error('username_or_email')
                            focus:border-red-500 placeholder:text-red-500
                        @enderror"
                        placeholder="<EMAIL>">

                    @error('username_or_email')
                        <div class="mt-2 text-xs text-red-500">
                            {{ $message }}
                        </div>
                    @enderror
                </fieldset>

                <!-- password -->
                <fieldset class="mt-5">
                    <label for="password"
                        class="text-sm font-medium text-primary">{{ __('messages.login_password_label') }}</label>

                    <div class="relative" x-data="{ showPassword: false }">
                        <input type="password" x-bind:type="showPassword ? 'text' : 'password'" id="password"
                            wire:model.blur="password"
                            class="block w-full border-extraLightGray border placeholder:text-lightGray placeholder:text-sm placeholder:font-light rounded-lg mt-2 px-3 py-2 outline-none focus:border-primary focus:border-[1.3px] transition-all duration-300 @error('password')
                            focus:border-red-500 placeholder:text-red-500
                        @enderror"
                            placeholder="********">

                        <!-- password show hide button -->
                        <div class="absolute z-10 cursor-pointer top-3 right-5" @click="showPassword = !showPassword">
                            <img x-show="!showPassword" id="show-icon" class="size-5"
                                src="{{ asset('assets/images/eye.svg') }}" alt="Show password">
                            <img x-show="showPassword" id="hide-icon" class="size-5"
                                src="{{ asset('assets/images/eye-closed.svg') }}" alt="Hide password">
                        </div>
                    </div>
                    @error('password')
                        <div class="mt-2 text-xs text-red-500">
                            {{ $message }}
                        </div>
                    @enderror
                </fieldset>



                <div class="flex items-center justify-between mt-5 text-sm text-primary">
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="remember_me"
                            class="border-2 rounded shrink-0 border-primary text-primary focus:ring-primary disabled:opacity-50 disabled:pointer-events-none accent-primary">
                        <span class="ms-3">
                            {{ __('messages.keep_logged_in') }}
                        </span>
                    </label>


                </div>

                <div class="flex items-center justify-center mt-10">
                    <button wire:click="login" wire:loading.attr="disabled" wire:target="login" type="button"
                        class="w-full p-3 text-center text-white transition-all duration-300 rounded-lg bg-primary hover:bg-primaryDark">
                        <span wire:loading.remove wire:target="login">{{ __('messages.sign_in') }}</span>
                        <div wire:loading wire:target="login">
                            <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                            </div>
                        </div>
                    </button>
                </div>
            </form>

            <div class="flex flex-wrap justify-center gap-3 mt-5 md:hidden">
                <a target="_blank"
                    href="https://play.google.com/store/apps/details?id=com.rixxsol.memove&pcampaignid=web_share">
                    <div class="flex items-center px-4 py-2 mx-2 bg-white border rounded-lg w-52">
                        <svg class="size-8" xmlns="http://www.w3.org/2000/svg" version="1.1"
                            xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" viewBox="0 0 511.999 511.999"
                            style="enable-background:new 0 0 512 512" xml:space="preserve" class="">
                            <g>
                                <path
                                    d="M382.369 175.623A775951.446 775951.446 0 0 1 79.355 6.028C69.372-.565 57.886-1.429 47.962 1.93l254.05 254.05 80.357-80.357z"
                                    style="" fill="#32bbff" data-original="#32bbff" class=""></path>
                                <path
                                    d="M47.962 1.93c-1.86.63-3.67 1.39-5.401 2.308C31.602 10.166 23.549 21.573 23.549 36v439.96c0 14.427 8.052 25.834 19.012 31.761a36.954 36.954 0 0 0 5.395 2.314L302.012 255.98 47.962 1.93z"
                                    style="" fill="#32bbff" data-original="#32bbff" class=""></path>
                                <path
                                    d="M302.012 255.98 47.956 510.035c9.927 3.384 21.413 2.586 31.399-4.103 143.598-80.41 237.986-133.196 298.152-166.746l4.938-2.772-80.433-80.434z"
                                    style="" fill="#32bbff" data-original="#32bbff" class=""></path>
                                <path
                                    d="M23.549 255.98v219.98c0 14.427 8.052 25.834 19.012 31.761a36.954 36.954 0 0 0 5.395 2.314L302.012 255.98H23.549z"
                                    style="" fill="#2c9fd9" data-original="#2c9fd9"></path>
                                <path
                                    d="M79.355 6.028C67.5-1.8 53.52-1.577 42.561 4.239l255.595 255.596 84.212-84.212A786009.366 786009.366 0 0 1 79.355 6.028z"
                                    style="" fill="#29cc5e" data-original="#29cc5e"></path>
                                <path
                                    d="M298.158 252.126 42.561 507.721c10.96 5.815 24.939 6.151 36.794-1.789 143.598-80.41 237.986-133.196 298.152-166.746l4.938-2.772-84.287-84.288z"
                                    style="" fill="#d93f21" data-original="#d93f21"></path>
                                <path
                                    d="M488.45 255.98c0-12.19-6.151-24.492-18.342-31.314 0 0-22.799-12.721-92.682-51.809l-83.123 83.123 83.204 83.205c69.116-38.807 92.6-51.892 92.6-51.892 12.192-6.821 18.343-19.123 18.343-31.313z"
                                    style="" fill="#ffd500" data-original="#ffd500"></path>
                                <path
                                    d="M470.108 287.294c12.191-6.822 18.342-19.124 18.342-31.314H294.303l83.204 83.205c69.117-38.806 92.601-51.891 92.601-51.891z"
                                    style="" fill="#ffaa00" data-original="#ffaa00"></path>
                            </g>
                        </svg>
                        <div class="ml-3 text-left">
                            <p class='text-xs text-gray-700'>Scarica su </p>
                            <p class="text-sm md:text-base"> Google Play Store </p>
                        </div>
                    </div>
                </a>
                <a target="_blank" href="https://apps.apple.com/in/app/memove-alltech/id6740730389">
                    <div class="flex items-center px-4 py-2 mx-2 bg-white border rounded-lg w-52">
                        <svg xmlns="http://www.w3.org/2000/svg" version="1.1"
                            xmlns:xlink="http://www.w3.org/1999/xlink" class="size-8" x="0" y="0"
                            viewBox="0 0 22.773 22.773" style="enable-background:new 0 0 512 512"
                            xml:space="preserve" class="">
                            <g>
                                <path
                                    d="M15.769 0h.162c.13 1.606-.483 2.806-1.228 3.675-.731.863-1.732 1.7-3.351 1.573-.108-1.583.506-2.694 1.25-3.561C13.292.879 14.557.16 15.769 0zM20.67 16.716v.045c-.455 1.378-1.104 2.559-1.896 3.655-.723.995-1.609 2.334-3.191 2.334-1.367 0-2.275-.879-3.676-.903-1.482-.024-2.297.735-3.652.926h-.462c-.995-.144-1.798-.932-2.383-1.642-1.725-2.098-3.058-4.808-3.306-8.276v-1.019c.105-2.482 1.311-4.5 2.914-5.478.846-.52 2.009-.963 3.304-.765.555.086 1.122.276 1.619.464.471.181 1.06.502 1.618.485.378-.011.754-.208 1.135-.347 1.116-.403 2.21-.865 3.652-.648 1.733.262 2.963 1.032 3.723 2.22-1.466.933-2.625 2.339-2.427 4.74.176 2.181 1.444 3.457 3.028 4.209z"
                                    fill="#000000" opacity="1" data-original="#000000" class=""></path>
                            </g>
                        </svg>
                        <div class="ml-3 text-left">
                            <p class='text-xs text-gray-700'>Scarica su </p>
                            <p class="text-sm md:text-base"> Apple Store </p>
                        </div>
                    </div>
                </a>
            </div>
        </div>



    </main>
