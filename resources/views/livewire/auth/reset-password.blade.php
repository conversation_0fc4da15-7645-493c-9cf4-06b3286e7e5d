<div>
    <main class="grid min-h-screen gap-20 font-roboto lg:grid-cols-2">
        <!-- left section -->
        <div wire:ignore
            class="bg-[url('/assets/images/login-bg.png')!important] bg-cover bg-center bg-no-repeat h-full lg:flex flex-col items-center justify-center hidden min-h-screen">

            <img class="h-16" src="{{ asset('assets/images/logo-white.svg') }}" alt="logo">

            <p class="mt-10 text-lg font-medium text-center text-white">
                {{ __('messages.efficient_management') }}
            </p>
        </div>

        <!-- right section -->
        <div class="relative p-10 lg:p-20">
            <div class="absolute flex items-center justify-end md:top-10 md:right-10 top-5 right-5">
                <livewire:language-switcher color="primary" position="top" />
            </div>


            <div class="flex flex-col items-center justify-center text-center">
                <img class="h-12" src="{{ asset('assets/images/logo.svg') }}" alt="logo">

                <h1 class="md:text-3xl text-2xl font-semibold mt-10 text-primary tracking-wide [word-spacing:1px]">
                    {{ __('messages.reset_password') }}
                </h1>

                <p class="mt-4 text-sm font-light text-lightGray">
                    {{ __('messages.reset_password_instructions') }}

                </p>
            </div>


            <div class="mt-10">
                <!-- password -->
                <fieldset class="mt-5">
                    <label for="password"
                        class="text-sm font-medium text-primary">{{ __('messages.login_password_label') }}</label>

                    <div class="relative" x-data="{ showPassword: false }">
                        <input type="password" x-bind:type="showPassword ? 'text' : 'password'" id="password"
                            wire:model.blur="password"
                            class="block w-full border-extraLightGray border placeholder:text-lightGray placeholder:text-sm placeholder:font-light rounded-lg mt-2 px-3 py-2 outline-none focus:border-primary focus:border-[1.3px] transition-all duration-300 @error('email')
                        focus:border-red-500 placeholder:text-red-500
                    @enderror"
                            placeholder="********">

                        <!-- password show hide button -->
                        <div class="absolute z-10 cursor-pointer top-3 right-5" @click="showPassword = !showPassword">
                            <img x-show="!showPassword" id="show-icon" class="size-5"
                                src="{{ asset('assets/images/eye.svg') }}" alt="Show password">
                            <img x-show="showPassword" id="hide-icon" class="size-5"
                                src="{{ asset('assets/images/eye-closed.svg') }}" alt="Hide password">
                        </div>
                    </div>
                    @error('password')
                        <div class="mt-2 text-xs text-red-500">
                            {{ $message }}
                        </div>
                    @enderror
                </fieldset>

                <!-- password -->
                <fieldset class="mt-5">
                    <label for="password_confirmation"
                        class="text-sm font-medium text-primary">{{ __('messages.password_confirmation') }}</label>

                    <div class="relative" x-data="{ showPassword: false }">
                        <input type="password" x-bind:type="showPassword ? 'text' : 'password'"
                            id="password_confirmation" wire:model.blur="password_confirmation"
                            class="block w-full border-extraLightGray border placeholder:text-lightGray placeholder:text-sm placeholder:font-light rounded-lg mt-2 px-3 py-2 outline-none focus:border-primary focus:border-[1.3px] transition-all duration-300 @error('email')
                                focus:border-red-500 placeholder:text-red-500
                            @enderror"
                            placeholder="********">

                        <!-- password show hide button -->
                        <div class="absolute z-10 cursor-pointer top-3 right-5" @click="showPassword = !showPassword">
                            <img x-show="!showPassword" id="show-icon" class="size-5"
                                src="{{ asset('assets/images/eye.svg') }}" alt="Show password">
                            <img x-show="showPassword" id="hide-icon" class="size-5"
                                src="{{ asset('assets/images/eye-closed.svg') }}" alt="Hide password">
                        </div>
                    </div>
                    @error('password_confirmation')
                        <div class="mt-2 text-xs text-red-500">
                            {{ $message }}
                        </div>
                    @enderror
                </fieldset>
                <div class="flex items-center justify-center mt-10">

                    <button wire:click="resetPassword" wire:loading.attr="disabled" wire:target="resetPassword"
                        type="button"
                        class="w-full p-3 text-center text-white transition-all duration-300 rounded-lg bg-primary hover:bg-primaryDark">
                        <span wire:loading.remove
                            wire:target="resetPassword">{{ __('messages.reset_password') }}</span>
                        <div wire:loading wire:target="resetPassword">
                            <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                            </div>
                        </div>
                    </button>
                </div>
            </div>


        </div>


    </main>

</div>
