<div class="w-full">
    <div class="relative">
        <label for="multi-select" class="block mb-1 text-sm font-medium text-gray-700">{{ $label }}</label>
        <div @click.outside="$wire.set('open',false)" class="relative mt-1">
            <button type="button" wire:click="fetchOptions"
                class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 rounded-md shadow-sm cursor-default focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm">
                <!-- Search Input -->
                <input type="text" wire:model.live.debounce.400ms="search"
                    class="w-full border-gray-300 rounded-md outline-none sm:text-sm md:placeholder:text-sm placeholder:text-xs"
                    @if ($selectedOption) value="{{ $filteredOptions[$selectedOption] ?? '' }}" @endif
                    placeholder="{{ $placeholder ?? 'Search options...' }}">


                <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg class="text-gray-400 md:size-5 size-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                        fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd"
                            d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                            clip-rule="evenodd" />
                    </svg>
                </span>
            </button>
            @if ($open)

                <div
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                    @foreach ($filteredOptions ?? [] as $key => $option)
                        <div
                            wire:click="toggleOption('{{ $key }}')"class="relative py-2 pl-3 cursor-pointer select-none pr-9 hover:bg-primary hover:text-white">
                            <span
                                class="block
                                truncate {{ $option == $selectedOption ? 'font-semibold' : '' }}">
                                {{ $option }}</span>
                            @if ($option == $selectedOption)
                                <span
                                    class="absolute inset-y-0 right-0 flex items-center pr-4 text-primary hover:text-white"><svg
                                        class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0
                                            01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414
                                            0z" clip-rule="evenodd" />
                                    </svg></span>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endif

        </div>
    </div>
</div>
