    <div class="relative inline-block text-left">
        <div x-data="{ open: false }" class="relative">
            <!-- Language Button -->
            <button @click="open = !open" @click.outside="open = false" id="language-button"
                class="flex items-center text-{{ $color ?? 'white' }}">
                <svg class="size-6" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M16.5 29.3333C14.6208 29.3333 12.8448 28.9831 11.1719 28.2827C9.49896 27.5822 8.03825 26.6267 6.78975 25.416C5.54125 24.2053 4.55583 22.7889 3.8335 21.1667C3.11117 19.5444 2.75 17.8222 2.75 16C2.75 14.1556 3.11117 12.428 3.8335 10.8173C4.55583 9.20666 5.54125 7.79555 6.78975 6.584C8.03825 5.37244 9.49896 4.41689 11.1719 3.71733C12.8448 3.01778 14.6208 2.66755 16.5 2.66666C18.4021 2.66666 20.1841 3.01689 21.846 3.71733C23.5079 4.41778 24.9627 5.37333 26.2103 6.584C27.4578 7.79466 28.4433 9.20577 29.1665 10.8173C29.8897 12.4289 30.2509 14.1564 30.25 16C30.25 17.8222 29.8888 19.5444 29.1665 21.1667C28.4442 22.7889 27.4587 24.2058 26.2103 25.4173C24.9618 26.6289 23.5065 27.5844 21.8446 28.284C20.1827 28.9836 18.4012 29.3333 16.5 29.3333ZM16.5 26.6C17.0958 25.8 17.6115 24.9667 18.0469 24.1C18.4823 23.2333 18.8375 22.3111 19.1125 21.3333H13.8875C14.1625 22.3111 14.5177 23.2333 14.9531 24.1C15.3885 24.9667 15.9042 25.8 16.5 26.6ZM12.925 26.0667C12.5125 25.3333 12.1518 24.572 11.8429 23.7827C11.534 22.9933 11.2759 22.1769 11.0688 21.3333H7.0125C7.67708 22.4444 8.50804 23.4111 9.50537 24.2333C10.5027 25.0556 11.6426 25.6667 12.925 26.0667ZM20.075 26.0667C21.3583 25.6667 22.4987 25.0556 23.496 24.2333C24.4933 23.4111 25.3238 22.4444 25.9875 21.3333H21.9312C21.725 22.1778 21.4674 22.9947 21.1585 23.784C20.8496 24.5733 20.4884 25.3342 20.075 26.0667ZM5.84375 18.6667H10.5188C10.45 18.2222 10.3987 17.7836 10.3647 17.3507C10.3308 16.9178 10.3134 16.4676 10.3125 16C10.3116 15.5324 10.329 15.0827 10.3647 14.6507C10.4005 14.2187 10.4518 13.7796 10.5188 13.3333H5.84375C5.72917 13.7778 5.64346 14.2169 5.58663 14.6507C5.52979 15.0844 5.50092 15.5342 5.5 16C5.49908 16.4658 5.52796 16.916 5.58663 17.3507C5.64529 17.7853 5.731 18.224 5.84375 18.6667ZM13.2687 18.6667H19.7313C19.8 18.2222 19.8518 17.7836 19.8866 17.3507C19.9215 16.9178 19.9384 16.4676 19.9375 16C19.9366 15.5324 19.9192 15.0827 19.8853 14.6507C19.8513 14.2187 19.8 13.7796 19.7313 13.3333H13.2687C13.2 13.7778 13.1487 14.2169 13.1147 14.6507C13.0808 15.0844 13.0634 15.5342 13.0625 16C13.0616 16.4658 13.079 16.916 13.1147 17.3507C13.1505 17.7853 13.2018 18.224 13.2687 18.6667ZM22.4813 18.6667H27.1562C27.2708 18.2222 27.357 17.7836 27.4147 17.3507C27.4725 16.9178 27.5009 16.4676 27.5 16C27.4991 15.5324 27.4707 15.0827 27.4147 14.6507C27.3588 14.2187 27.2727 13.7796 27.1562 13.3333H22.4813C22.55 13.7778 22.6018 14.2169 22.6366 14.6507C22.6715 15.0844 22.6884 15.5342 22.6875 16C22.6866 16.4658 22.6692 16.916 22.6353 17.3507C22.6013 17.7853 22.55 18.224 22.4813 18.6667ZM21.9312 10.6667H25.9875C25.3229 9.55555 24.4924 8.58889 23.496 7.76666C22.4996 6.94444 21.3593 6.33333 20.075 5.93333C20.4875 6.66666 20.8487 7.428 21.1585 8.21733C21.4683 9.00666 21.7259 9.82311 21.9312 10.6667ZM13.8875 10.6667H19.1125C18.8375 9.68889 18.4823 8.76666 18.0469 7.9C17.6115 7.03333 17.0958 6.2 16.5 5.4C15.9042 6.2 15.3885 7.03333 14.9531 7.9C14.5177 8.76666 14.1625 9.68889 13.8875 10.6667ZM7.0125 10.6667H11.0688C11.275 9.82222 11.533 9.00533 11.8429 8.216C12.1527 7.42666 12.5134 6.66577 12.925 5.93333C11.6417 6.33333 10.5013 6.94444 9.504 7.76666C8.50667 8.58889 7.67617 9.55555 7.0125 10.6667Z"
                        fill="currentColor" />
                </svg>

            </button>

            <!-- Dropdown Menu -->
            <div x-show="open" x-transition:enter="transition ease-out duration-200"
                x-transition:enter-start="opacity-0 transform scale-95"
                x-transition:enter-end="opacity-100 transform scale-100"
                x-transition:leave="transition ease-in duration-150"
                x-transition:leave-start="opacity-100 transform scale-100"
                x-transition:leave-end="opacity-0 transform scale-95"
                class="absolute {{ $position }}-full right-full z-10 mt-2 w-max bg-white rounded-lg shadow-lg text-[#6B6B6B] text-sm overflow-hidden"
                style="display: none;">
                <ul>
                    @foreach ($languages as $shortCode => $language)
                        <li wire:click="changeLanguage('{{ $shortCode }}'); open = false;"
                            class="px-4 py-2 whitespace-nowrap cursor-pointer hover:bg-gray-100 hover:text-[#6B6B6B] @if ($currentLanguage == $shortCode) bg-primary text-white @endif">
                            <span class="w-full text-left">{{ $language['name'] }}</span>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>

    </div>
