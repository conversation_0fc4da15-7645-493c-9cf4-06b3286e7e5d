<div class="relative z-[111111111111111]">
    <style>
        [x-cloak] {
            display: none;
        }

        .progress-toast {
            animation: progressToast;
            animation-iteration-count: 1;
            animation-fill-mode: forwards;
            animation-timing-function: linear;
        }

        @keyframes progressToast {
            0% {
                width: 0%;
            }

            80% {
                opacity: 1;
            }

            100% {
                opacity: 0.5;
                width: 100%;
            }
        }
    </style>

    <div x-data="noticesHandler()"
        class="fixed md:right-8 right-0 flex flex-col-reverse items-end justify-end h-screen w-screen z-[111111111111111] md:top-20 top-12"
        @notice.window="add($event.detail)" style="pointer-events:none">
        <template x-for="notice of notices" :key="notice.id">
            <div x-show="visible.includes(notice)" x-transition:enter="transition ease-in duration-300"
                x-transition:enter-start="transform opacity-0 -translate-y-2"
                x-transition:enter-end="transform opacity-100" x-transition:leave="transition ease-out duration-500"
                x-transition:leave-start="transform translate-x-0 opacity-100"
                x-transition:leave-end="transform translate-x-full opacity-0" @click="remove(notice.id)"
                style="pointer-events:all">
                <div class="relative flex items-center justify-center mt-4 mr-6 w-64 min-h-16 h-fit text-white shadow-lg font-medium cursor-pointer p-6 text-sm"
                    :class="getBgColor(notice.type, 500)">
                    <p class="pb-2" x-text="notice.text"></p>
                    <div x-cloak :class="getBgColor(notice.type, 300)" class="absolute bottom-0 left-0 right-0 w-full">
                        <div class="h-2 w-0 progress-toast bg-white"
                            :style="`animation-duration:${notice.animationTime}ms`">
                        </div>
                    </div>

                    <button class="absolute top-3 right-3">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.8"
                            stroke="currentColor" class="size-4 text-white">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                        </svg>

                    </button>
                </div>
            </div>
        </template>
    </div>


    <script>
        function noticesHandler() {
            return {
                notices: [],
                visible: [],
                add(notice) {
                    notice.id = Date.now();
                    notice.animationTime = this.getAnimationTime();
                    this.notices.push(notice);
                    this.fire(notice);
                },
                fire(notice) {
                    this.visible.push(notice);
                    setTimeout(() => {
                        this.remove(notice.id);
                    }, notice.animationTime);
                },
                remove(id) {
                    const notice = this.visible.find(notice => notice.id == id);
                    const index = this.visible.indexOf(notice);
                    this.visible.splice(index, 1);
                },
                getAnimationTime() {
                    const delta = 1;
                    const transitionTime = 800;
                    return 7000 * delta - transitionTime * delta;
                },
                getBgColor(type, shade) {
                    // return `bg-${this.getThemeColor(type)}-${shade}`;
                    return `bg-primary`;
                },
                getThemeColor(type) {
                    switch (type) {
                        case 'success':
                            return 'green';
                        case 'warning':
                            return 'orange';
                        case 'error':
                            return 'red';
                        default:
                            return 'black';
                    }

                }
            };

        }
    </script>
</div>
