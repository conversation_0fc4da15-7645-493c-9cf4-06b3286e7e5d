@section('device_command_active', 'bg-primary/10 text-primary')

<main class="flex-shrink w-full overflow-auto">
    <section class="w-full p-6 mx-auto my-5">

        <div class="flex justify-end">
            <button @click="$dispatch('open-modal', {name:'send-command-modal'})"
                class="mt-4 flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-primaryDark ">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>

                <span>{{ __('messages.send_command') }}</span>
            </button>
        </div>

        <div class="w-full p-5 mt-10 mb-20 overflow-hidden bg-white rounded-lg shadow-md md:rounded-2xl md:p-10">
            <div class="flex flex-wrap items-center justify-between flex-grow md:gap-4">
                <h2 class="text-xl font-semibold">@lang('messages.device_commands')</h2>

                <div class="flex flex-col items-center flex-grow gap-4 md:flex-row md:justify-end">



                    <div class="w-full max-w-xs">
                        <div class="flex items-center w-full space-x-5">
                            <div class="flex w-full p-3 space-x-2 text-sm bg-gray-100 rounded-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <input wire:model.live.debounce.500ms="search" class="bg-gray-100 outline-none"
                                    type="search" placeholder="{{ __('messages.search') }}" />
                            </div>
                        </div>
                    </div>

                    <div class="w-fit">
                        <select wire:model.live="status" class="outline-none">
                            <option selected>@lang('messages.status')</option>
                            <option value="1">@lang('messages.completed')</option>
                            <option value="0">@lang('messages.queue')</option>
                        </select>
                    </div>
                </div>
            </div>

            <div wire:poll.5s class="w-full mt-10 mb-5 overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr
                            class="text-xs font-semibold tracking-wide text-left text-gray-900 uppercase border-b border-gray-600">
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.device')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.command')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.response')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.received_at')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.sent_at')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.status')</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white">
                        @forelse ($deviceCommands ?? [] as $deviceCommand)
                            <tr class="text-gray-700">
                                <td class="px-4 py-3 text-sm font-medium">{{ $deviceCommand->imei ?? 'N/A' }}</td>
                             
                                <td class="px-4 py-3 text-sm">
                                    {{ $deviceCommand->name ?? 'N/A' }}</td>

                                <td class="px-4 py-3 text-sm">{{ $deviceCommand->response ?? 'N/A' }}</td>

                                <td class="px-4 py-3 text-sm">
                                    {{ $deviceCommand->response_received_at ? \Carbon\Carbon::parse($deviceCommand->response_received_at)->format('d/m/Y H:i') : 'N/A' }}
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    {{ $deviceCommand->created_at->format('d/m/Y H:i') ?? 'N/A' }}</td>

                                <td class="flex items-center gap-2 px-4 py-3 text-sm">
                                    @if ($deviceCommand->response)
                                        <button
                                            class="px-2 py-1 text-xs rounded-full text-emerald-700 bg-emerald-100">@lang('messages.completed')</button>
                                    @elseif ($deviceCommand->added_in_queue == 1)
                                        <button
                                            class="px-2 py-1 text-xs text-yellow-600 bg-yellow-100 rounded-full">@lang('messages.queue')</button>


                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="deleteRecordConfirmation({{ $deviceCommand->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img wire:ignore class="size-5"
                                                    src="{{ asset('assets/images/delete.svg') }}" alt="Delete">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('messages.delete_from_queue')
                                            </div>
                                        </div>
                                    @elseif ($deviceCommand->added_in_queue == 2)
                                        <button
                                            class="px-2 py-1 text-xs rounded-full text-rose-700 bg-rose-100">@lang('messages.cancelled')</button>
                                    @endif
                                </td>

                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="px-4 py-3 text-center">
                                    {{ __('messages.no_record_found') }}
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>


                {{ $deviceCommands->links('livewire.custom-pagination') ?? '' }}

            </div>
    </section>


    <x-modal name="send-command-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black">{{ __('messages.send_command') }}</h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">

                        <div class="col-span-2">
                            <label class="text-sm text-[#414651]">{{ __('messages.devices') }}</label>

                            <x-dropdown name="imei" :options="$devices" />


                            @error('imei')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2">
                            <label class="text-sm text-[#414651]">{{ __('messages.command') }}</label>



                            <x-dropdown :options="$commands" name="command" />


                            @error('command')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        @if ($command && $command == 'other')
                            <div class="col-span-2">
                                <label class="text-sm text-[#414651]">{{ __('messages.custom_command') }}</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model="custom_command" id="command"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                        placeholder="{{ __('messages.enter_custom_command') }}">
                                </div>


                                @error('custom_command')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        @endif




                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false;"
                            class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>

                        <button wire:click="addCommand" wire:loading.attr="disabled" wire:target="addCommand"
                            type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            <span wire:loading.remove wire:target="addCommand"> {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="addCommand">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

    <x-modal name="delete-record-modal">
        <x-slot:body>

            <div class="flex items-center justify-between">
                <img class="size-10" src="{{ asset('assets/images/logout-icon.svg') }}" alt="logout">

                <button @click="show = false" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>

            </div>

            <div class="mt-4">
                <h3 class="font-medium">
                    {{ __('messages.delete_confirmation') }}
                </h3>

                <p class="text-sm mt-2 text-[#535862]">
                    {{ __('messages.delete_warning') }}
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                        {{ __('messages.cancel') }}
                    </button>
                    <button wire:click="deleteRecord"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        {{ __('messages.delete') }}
                    </button>
                </div>
            </div>


        </x-slot:body>
    </x-modal>

    @if ($openModal)
        @script
            <script>
                setTimeout(() => {
                    $wire.dispatch('open-modal', {
                        name: 'send-command-modal'
                    })
                }, 1000);
            </script>
        @endscript
    @endif

</main>
