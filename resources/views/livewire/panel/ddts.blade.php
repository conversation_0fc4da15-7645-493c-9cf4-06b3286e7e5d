@section('ddts_active', 'bg-primary/10 text-primary')

<main class="flex-shrink w-full overflow-auto">
    <section class="w-full p-6 mx-auto my-5">

        <div class="w-full p-5 mt-10 mb-20 overflow-hidden bg-white rounded-lg shadow-md md:rounded-2xl md:p-10">
            <div class="flex flex-wrap items-center justify-between flex-grow md:gap-4">
                <h2 class="text-xl font-semibold">DDT</h2>

                <div class="flex flex-col items-center flex-grow gap-4 md:flex-row md:justify-end">

                    <div class="w-full max-w-xs">
                        <div class="flex items-center w-full space-x-5">
                            <div class="flex w-full p-3 space-x-2 text-sm bg-gray-100 rounded-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <input wire:model.live.debounce.500ms="search" class="bg-gray-100 outline-none"
                                    type="search" placeholder="{{ __('messages.search') }}" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full mt-10 mb-5 overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr
                            class="text-xs font-semibold tracking-wide text-left text-gray-900 uppercase border-b border-gray-600">
                            <th class="px-4 py-3 border whitespace-nowrap">DDT No.</th>
                            <th class="px-4 py-3 border whitespace-nowrap">{{ __('messages.dealer') }}</th>
                            <th class="px-4 py-3 border whitespace-nowrap">{{ __('messages.added_at') }}</th>
                            <th class="px-4 py-3 border whitespace-nowrap">{{ __('messages.ddt_type') }}</th>
                            <th class="px-4 py-3 border whitespace-nowrap text-end min-w-28">
                                {{ __('messages.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white">
                        @forelse ($ddts as $ddt)
                            <tr class="text-gray-700">
                                <td class="px-4 py-3">
                                    <div>
                                        <p class="font-medium text-black">{{ $ddt->ddt_number ?? '' }}
                                        </p>
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm font-medium">{{ $ddt->dealer->user->name ?? 'N/A' }}</td>


                                <td class="px-4 py-3 text-sm">{{ $ddt->created_at->format('d/m/Y H:i') }}</td>
                                <td class="px-4 py-3 text-sm">
                                    @if ($ddt->type == 'assigned')
                                        <button class="px-2 py-1 text-xs text-white rounded-full bg-emerald-500">
                                            @lang('messages.assigned')
                                        </button>
                                    @else
                                        <button class="px-2 py-1 text-xs text-white bg-red-500 rounded-full">
                                            @lang('messages.returned')
                                        </button>
                                    @endif
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="flex items-center justify-end gap-2">



                                        <!-- Edit Button with Tooltip -->
                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="downloadDDT({{ $ddt->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                                                class="text-primary">

                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                    fill="currentColor" class="size-5">
                                                    <path
                                                        d="M12 1.5a.75.75 0 0 1 .75.75V7.5h-1.5V2.25A.75.75 0 0 1 12 1.5ZM11.25 7.5v5.69l-1.72-1.72a.75.75 0 0 0-1.06 1.06l3 3a.75.75 0 0 0 1.06 0l3-3a.75.75 0 1 0-1.06-1.06l-1.72 1.72V7.5h3.75a3 3 0 0 1 3 3v9a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3v-9a3 3 0 0 1 3-3h3.75Z" />
                                                </svg>


                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                {{ __('messages.download') }}
                                            </div>
                                        </div>

                                        @if (auth()->user()->role == 'admin')
                                            <!-- Delete Button with Tooltip -->
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="deleteRecordConfirmation({{ $ddt->id }})"
                                                    @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                    <img class="size-5" src="{{ asset('assets/images/delete.svg') }}"
                                                        alt="Delete">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    {{ __('messages.delete') }}
                                                </div>
                                            </div>
                                        @endif

                                    </div>

                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="px-4 py-3 text-center">{{ __('messages.no_record_found') }}
                                </td>
                            </tr>
                        @endforelse

                    </tbody>
                </table>
            </div>

            {{ $ddts->links('livewire.custom-pagination') }}

        </div>
    </section>

    {{-- modals --}}


    {{-- delete admin modal --}}
    <x-modal name="delete-record-modal">
        <x-slot:body>

            <div class="flex items-center justify-between">
                <img class="size-10" src="{{ asset('assets/images/logout-icon.svg') }}" alt="logout">

                <button @click="show = false" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>

            </div>

            <div class="mt-4">
                <h3 class="font-medium">
                    {{ __('messages.delete_confirmation') }}
                </h3>

                <p class="text-sm mt-2 text-[#535862]">
                    {{ __('messages.delete_warning') }}
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                        {{ __('messages.cancel') }}
                    </button>
                    <button wire:click="deleteRecord"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        {{ __('messages.delete') }}
                    </button>
                </div>
            </div>


        </x-slot:body>
    </x-modal>

</main>
