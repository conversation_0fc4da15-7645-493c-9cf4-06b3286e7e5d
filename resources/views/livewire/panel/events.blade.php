<main class="flex-shrink w-full overflow-auto">
    <section class="w-full p-6 mx-auto my-5">

        <div class="w-full p-5 mt-10 mb-20 overflow-hidden bg-white rounded-lg shadow-md md:rounded-2xl md:p-10">
            <div class="flex flex-wrap items-center justify-between flex-grow md:gap-4">
                <h2 class="text-xl font-semibold">@lang('messages.devices_alarms_events')</h2>

                <div class="flex flex-col items-center flex-grow gap-4 md:flex-row md:justify-end">



                    <div class="w-full max-w-xs">
                        <div class="flex items-center w-full space-x-5">
                            <div class="flex w-full p-3 space-x-2 text-sm bg-gray-100 rounded-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <input wire:model.live.debounce.500ms="search" class="bg-gray-100 outline-none"
                                    type="search" placeholder="{{ __('messages.search') }}" />
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="w-full mt-10 mb-5 overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr
                            class="text-xs font-semibold tracking-wide text-left text-gray-900 uppercase border-b border-gray-600">
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.device')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.number_plate')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.event')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">Long-Lat</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.occured_at')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.address')</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white">
                        @forelse ($events ?? [] as $event)
                            <tr class="text-gray-700">
                                <td class="px-4 py-3 text-sm font-medium">{{ $event->imei ?? 'N/A' }}</td>
                                <td class="px-4 py-3 text-sm font-medium">
                                    {{ \App\Models\Device::where('imei', $event->imei)->first()?->number_plate ?? 'N/A' }}
                                </td>
                                <td class="px-4 py-3 text-sm">{{ $event->event_message ?? 'N/A' }}</td>
                                <td class="px-4 py-3 text-sm">
                                    {{ $event->longitude ?? 'N/A' }}, {{ $event->latitude ?? 'N/A' }}
                                </td>
                                <td class="px-4 py-3 text-sm">{{ $event->created_at ?? 'N/A' }}</td>
                                <td class="px-4 py-3 text-xs text-wrap">
                                    <a
                                        href="http://maps.google.com/maps?z=12&t=m&q=loc:{{ $event->latitude }}+{{ $event->longitude }}">{{ $event->address ?? 'N/A' }}</a>
                                </td>

                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="px-4 py-3 text-center">{{ __('messages.no_record_found') }}
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>


                {{ $events->links('livewire.custom-pagination') }}

            </div>
        </div>
    </section>


</main>
