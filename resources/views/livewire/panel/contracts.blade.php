@section('contracts_active', 'bg-primary/10 text-primary')

<main class="flex-shrink w-full overflow-auto">
    <section class="w-full p-6 mx-auto my-5">

        {{-- <div class="flex justify-end">
            <button @click="$dispatch('open-modal', {name:'add-contract-modal'})"
                class="mt-4 flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-primaryDark ">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>

                <span>{{ __('messages.add_new_record') }}</span>
            </button>
        </div> --}}

        <div class="w-full p-5 mt-10 mb-20 overflow-hidden bg-white rounded-lg shadow-md md:rounded-2xl md:p-10">
            <div class="flex flex-wrap items-center justify-between flex-grow md:gap-4">
                <h2 class="text-xl font-semibold">{{ __('messages.contracts') }}</h2>

                <div class="flex flex-col items-center flex-grow gap-4 md:flex-row md:justify-end">

                    <div class="flex items-center ms-auto">
                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">

                            <button wire:click="exportPDF" @mouseenter="showTooltip = true"
                                @mouseleave="showTooltip = false"
                                class="p-2 text-sm text-white transition-all duration-300 bg-red-500 rounded-lg hover:bg-red-600 ">
                                <img src="{{ asset('assets/images/pdf.svg') }}" alt="excel" class="size-5">
                            </button>
                            <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                x-transition:enter-start="opacity-0 transform scale-95"
                                x-transition:enter-end="opacity-100 transform scale-100"
                                x-transition:leave="transition ease-in duration-150"
                                x-transition:leave-start="opacity-100 transform scale-100"
                                x-transition:leave-end="opacity-0 transform scale-95"
                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                style="display: none;">
                                @lang('messages.export_pdf')
                            </div>
                        </div>

                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                            <button wire:click="exportExcel" @mouseenter="showTooltip = true"
                                @mouseleave="showTooltip = false"
                                class="p-2 text-sm text-white transition-all duration-300 rounded-lg ms-2 bg-emerald-500 hover:bg-emerald-600 ">
                                <img src="{{ asset('assets/images/excel.svg') }}" alt="excel" class="size-5">
                            </button>
                            <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                x-transition:enter-start="opacity-0 transform scale-95"
                                x-transition:enter-end="opacity-100 transform scale-100"
                                x-transition:leave="transition ease-in duration-150"
                                x-transition:leave-start="opacity-100 transform scale-100"
                                x-transition:leave-end="opacity-0 transform scale-95"
                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                style="display: none;">
                                @lang('messages.export_excel')
                            </div>
                        </div>
                    </div>
                    <div class="w-full max-w-xs">
                        <div class="flex items-center w-full space-x-5">
                            <div class="flex w-full p-3 space-x-2 text-sm bg-gray-100 rounded-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <input wire:model.live.debounce.500ms="search" class="bg-gray-100 outline-none"
                                    type="search" placeholder="{{ __('messages.search') }}" />
                            </div>
                        </div>
                    </div>

                    <div class="w-fit">
                        <select wire:model.live="status" class="outline-none">
                            <option selected>@lang('messages.status')</option>
                            <option value="pending">@lang('messages.pending')</option>
                            <option value="active">@lang('messages.active')</option>
                            <option value="inactive">@lang('messages.inactive')</option>
                            <option value="expired">@lang('messages.expired')</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="w-full mt-10 mb-5 overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr
                            class="text-xs font-semibold tracking-wide text-left text-gray-900 uppercase border-b border-gray-600">
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.number_plate')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.device')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.dealer')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.client')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.duration')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.status')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.signed')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.added_at')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.signed_at')</th>
                            <th class="px-4 py-3 border whitespace-nowrap text-end min-w-40">@lang('messages.actions')</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white">
                        @forelse ($contracts as $contract)
                            <tr class="text-gray-700">
                                <td class="px-4 py-3 text-sm font-medium">
                                    {{ $contract->device?->number_plate ?? 'N/A' }}
                                </td>
                                <td class="px-4 py-3">
                                    <div>
                                        <p class="text-sm font-semibold text-black">
                                            {{ $contract->device->imei ?? 'N/A' }}
                                        </p>
                                        <p class="text-xs text-gray-600">{{ $contract->device->model ?? '' }}
                                        </p>
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm font-medium">{{ $contract->dealer->user->name ?? 'N/A' }}
                                    {{ $contract->dealer->last_name ?? '' }}
                                </td>

                                <td class="px-4 py-3 text-sm">{{ $contract->client->user->name ?? 'N/A' }}
                                    {{ $contract->client->last_name ?? '' }}
                                </td>
                                <td class="px-4 py-3 text-xs">
                                    <div class="font-medium">
                                        {!! $contract->duration
                                            ? $contract->duration . '<span class="text-[10px] uppercase"> ' . __('messages.months') . '</span>'
                                            : 'N/A' !!}
                                    </div>
                                    {{ \Carbon\Carbon::parse($contract->start_date)->format('d/m/Y') }} -
                                    {{ \Carbon\Carbon::parse($contract->end_date)->format('d/m/Y') }}

                                </td>
                                <td class="px-4 py-3 text-xs whitespace-nowrap">
                                    @if ($contract->status == 'active')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full text-emerald-700 bg-emerald-100">
                                            @lang('messages.active') </span>
                                    @elseif($contract->status == 'inactive')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight text-yellow-600 bg-yellow-100 rounded-full">
                                            @lang('messages.inactive') </span>
                                    @elseif($contract->status == 'pending')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight text-yellow-600 bg-yellow-100 rounded-full">
                                            @lang('messages.pending') </span>
                                    @elseif($contract->status == 'expired')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full text-rose-700 bg-rose-100">
                                            @lang('messages.expired') </span>
                                    @elseif($contract->status == 'rejected')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full text-rose-700 bg-rose-100">
                                            @lang('messages.rejected') </span>
                                    @endif
                                </td>
                                <td class="px-4 py-3 text-xs">
                                    @if ($contract->signed == 1)
                                        <img class="size-5" src="{{ asset('assets/images/check.svg') }}"
                                            alt="check">
                                    @else
                                        <img class="size-5" src="{{ asset('assets/images/cancel.svg') }}"
                                            alt="check">
                                    @endif
                                </td>

                                <td class="px-4 py-3 text-sm">{{ $contract->created_at->format('d/m/Y H:i') }}</td>
                                <td class="px-4 py-3 text-sm">
                                    @if ($contract->signed)
                                        {{ $contract->responded_at ? \Carbon\Carbon::parse($contract->responded_at)->format('d/m/Y H:i') : 'N/A' }}
                                    @endif
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="flex items-center justify-end gap-2">

                                        @if (auth()->user()->role == 'admin')
                                            {{-- @if ($contract->status == 'expired')       --}}
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="renewContract({{ $contract->id }})"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="size-5" src="{{ asset('assets/images/renew.svg') }}"
                                                        alt="Edit">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    {{ __('messages.renew') }}
                                                </div>
                                            </div>
                                            {{-- @endif --}}
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="renewContract({{ $contract->id }})"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="size-4" src="{{ asset('assets/images/change.svg') }}"
                                                        alt="Edit">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    {{ __('messages.change_vehicle_ownership') }}
                                                </div>
                                            </div>
                                        @endif



                                        {{-- @if (auth()->user()->role == 'admin' || $contract->signed == 1) --}}
                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0 mt-1 text-primary">
                                            <button wire:click="downloadCollaudoContract({{ $contract->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-4"
                                                    src="{{ asset('assets/images/contract-collaudo.svg') }}"
                                                    alt="test">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('messages.downlod_collaudo_contract')
                                            </div>
                                        </div>


                                        @if (auth()->user()->role != 'dealer')
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0 text-primary">
                                                <button wire:click="downloadContract({{ $contract->id }})"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                        fill="currentColor" class="size-5">
                                                        <path
                                                            d="M12 1.5a.75.75 0 0 1 .75.75V7.5h-1.5V2.25A.75.75 0 0 1 12 1.5ZM11.25 7.5v5.69l-1.72-1.72a.75.75 0 0 0-1.06 1.06l3 3a.75.75 0 0 0 1.06 0l3-3a.75.75 0 1 0-1.06-1.06l-1.72 1.72V7.5h3.75a3 3 0 0 1 3 3v9a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3v-9a3 3 0 0 1 3-3h3.75Z" />
                                                    </svg>


                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    @lang('messages.downlod_contract')
                                                </div>
                                            </div>
                                        @endif
                                        {{-- @endif --}}

                                        @if ($contract->signed != 1)
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0 text-primary">
                                                <button wire:click="sendContract({{ $contract->id }})"
                                                    @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                                                    wire:loading.attr="disabled" wire:target="sendContract">
                                                    <svg class="size-5" viewBox="0 -3.5 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns" fill="#450099"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <title>mail</title> <desc>Created with Sketch Beta.</desc> <defs> </defs> <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage"> <g id="Icon-Set-Filled" sketch:type="MSLayerGroup" transform="translate(-414.000000, -261.000000)" fill="#450099"> <path d="M430,275.916 L426.684,273.167 L415.115,285.01 L444.591,285.01 L433.235,273.147 L430,275.916 L430,275.916 Z M434.89,271.89 L445.892,283.329 C445.955,283.107 446,282.877 446,282.634 L446,262.862 L434.89,271.89 L434.89,271.89 Z M414,262.816 L414,282.634 C414,282.877 414.045,283.107 414.108,283.329 L425.147,271.927 L414,262.816 L414,262.816 Z M445,261 L415,261 L430,273.019 L445,261 L445,261 Z" id="mail" sketch:type="MSShapeGroup"> </path> </g> </g> </g></svg>
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">



                                                    <span wire:loading.remove wire:target="sendContract">
                                                        @lang('messages.send_contract')
                                                    </span>
                                                    <span wire:loading wire:target="sendContract">
                                                        @lang('messages.sending')...
                                                    </span>
                                                </div>
                                            </div>
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0 text-primary">
                                                <button wire:click="sendContractSMS({{ $contract->id }})"
                                                    @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                                                    wire:loading.attr="disabled" wire:target="sendContract">
                                                    <svg class="size-5" fill="#450099" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M448,0H64C28.6,0,0,28.6,0,64v256c0,35.4,28.6,64,64,64h128l-42.7,128l192-128H448c35.4,0,64-28.6,64-64V64 C512,28.6,483.4,0,448,0z M128,234.7c-23.6,0-42.7-19.1-42.7-42.7s19.1-42.7,42.7-42.7s42.7,19.1,42.7,42.7S151.6,234.7,128,234.7z M256,234.7c-23.6,0-42.7-19.1-42.7-42.7s19.1-42.7,42.7-42.7s42.7,19.1,42.7,42.7S279.6,234.7,256,234.7z M384,234.7 c-23.6,0-42.7-19.1-42.7-42.7s19.1-42.7,42.7-42.7s42.7,19.1,42.7,42.7S407.6,234.7,384,234.7z"></path> </g></svg>
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">



                                                    <span wire:loading.remove wire:target="sendContract">
                                                        @lang('messages.send_contract_via_sms')
                                                    </span>
                                                    <span wire:loading wire:target="sendContract">
                                                        @lang('messages.sending')...
                                                    </span>
                                                </div>
                                            </div>
                                        @endif

                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="viewRecord({{ $contract->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5" src="{{ asset('assets/images/eye-icon.svg') }}"
                                                    alt="Edit">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('messages.view')
                                            </div>
                                        </div>


                                        <!-- Edit Button with Tooltip -->
                                        @if (auth()->user()->role == 'admin')
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="editRecord({{ $contract->id }})"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="size-5" src="{{ asset('assets/images/edit.svg') }}"
                                                        alt="Edit">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    {{ __('messages.edit') }}
                                                </div>
                                            </div>
                                            <!-- Delete Button with Tooltip -->
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="deleteRecordConfirmation({{ $contract->id }})"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="size-5" src="{{ asset('assets/images/delete.svg') }}"
                                                        alt="Delete">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    {{ __('messages.delete') }}
                                                </div>
                                            </div>
                                        @endif


                                    </div>

                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="px-4 py-3 text-center">{{ __('messages.no_record_found') }}
                                </td>
                            </tr>
                        @endforelse

                    </tbody>
                </table>
            </div>

            {{ $contracts->links('livewire.custom-pagination') }}

        </div>
    </section>

    {{-- modals --}}


    @php
        // Convert GPS status code to a readable format
        function getGPSStatus($status)
        {
            switch ($status) {
                case 0:
                    return __('messages.gps_off');
                case 1:
                    return __('messages.gps_signal_good');
                case 2:
                    return __('messages.gps_signal_weak');
                case 3:
                    return __('messages.gps_inactive');
                default:
                    return __('messages.unknown');
            }
        }

        // Convert movement status code to a readable format
        function getMovementStatus($status)
        {
            return $status === 1 ? __('messages.moving') : __('messages.stopped');
        }

        // Convert movement status code to a readable format
        function getEngineStatus($ignitionStatus, $movement = 0, $speed = 0)
        {
            if ($ignitionStatus == 1 && $speed > 0 && $movement == 1) {
                return __('messages.on');
            } else {
                return __('messages.off');
            }
        }
        function getIgnitionStatus($ignitionStatus)
        {
            if ($ignitionStatus == 1) {
                return __('messages.on');
            } else {
                return __('messages.off');
            }
        }

        // Convert GNSS status code to a readable format
        function getGNSSStatus($status)
        {
            switch ($status) {
                case 0:
                    return 'GNSS OFF';
                case 1:
                    return 'GNSS ON with fix';
                case 2:
                    return 'GNSS ON without fix';
                case 3:
                    return 'GNSS sleep';
                default:
                    return 'Unknown';
            }
        }

    @endphp

    {{-- verify device modal --}}
    <x-modal name="verify-device-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <h3 class="text-lg font-medium">@lang('messages.perform_testing')</h3>

                <div class="mt-4">
                    <div class="space-y-4 text-gray-800">
                        <div class="justify-between md:flex">
                            <div class="flex">
                                <img class="mt-1 size-5" src="{{ asset('assets/images/device.svg') }}"
                                    alt="device">
                                <div class="ms-2">
                                    <h3 class="font-medium">
                                        <span class="text-gray-800">{{ $deviceVerifyData->imei ?? 'N/A' }}</span>

                                    </h3>

                                    <p class="text-sm text-[#6A6A6A]">
                                        <span class="text-gray-800">{{ $deviceVerifyData->model ?? 'N/A' }}</span>
                                    </p>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button
                                    @click="$dispatch('close-modal');$dispatch('open-modal',{name:'add-client-modal'})"
                                    class="mt-4 flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-primaryDark ">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>@lang('messages.add_client')</span>
                                </button>
                            </div>
                        </div>

                        <div x-data="{ open: false }" class="pb-5 mt-5 space-y-4 border-b border-b-gray-200">
                            <!-- Accordion Toggle -->
                            <button @click="open = !open" type="button"
                                class="flex items-center justify-between w-full text-left">
                                <span class="font-bold text-gray-800">@lang('messages.last_test_result')</span>
                                <svg x-show="!open" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-600"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 15l7-7 7 7" />
                                </svg>
                                <svg x-show="open" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-600"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>

                            <!-- Accordion Content -->
                            @if ($deviceVerifyData->latestTestResult ?? null)
                                <div x-show="open" x-transition class="mt-4 space-y-4">
                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.tested_at'):</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->created_at->format('d/m/Y H:i') ?? 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">GPS:</strong>
                                        <span
                                            class="text-gray-800">{{ isset($deviceVerifyData->latestTestResult->gps_status) ? getGPSStatus($deviceVerifyData->latestTestResult->gps_status) : 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.movement'):</strong>
                                        <span
                                            class="text-gray-800">{{ isset($deviceVerifyData->latestTestResult->movement_status) ? getMovementStatus($deviceVerifyData->latestTestResult->movement_status) : 'N/A' }}</span>
                                    </p>


                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.ignition_status')</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->ignition_status ? __('messages.' . strtolower($deviceVerifyData->latestTestResult->ignition_status)) : 'N/A' }}</span>
                                    </p>
                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.engine_status')</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->engine_status ? __('messages.' . strtolower($deviceVerifyData->latestTestResult->engine_status)) : 'N/A' }}</span>
                                    </p>

                                    @if ($deviceVerifyData->latestTestResult->ignition_tested_at)
                                        <div class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.motor_block_test')</strong>
                                            <div class="flex items-center gap-2 text-gray-800">
                                                <img class="size-5" src="{{ asset('assets/images/check.svg') }}"
                                                    alt="check">

                                                <p class="font-medium text-emerald-600">
                                                    @lang('messages.ignition_tested')
                                                    {{ \Carbon\Carbon::parse($deviceVerifyData->latestTestResult->ignition_tested_at)->format('d-m-Y H:i') }}
                                                </p>

                                            </div>
                                        </div>
                                    @endif

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.battery_voltage'):</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->battery_voltage ? $deviceVerifyData->latestTestResult->battery_voltage . 'V' : 'N/A' }}</span>
                                    </p>
                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.location'):</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->location ?? 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">Lat - Long:</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->latitude ?? 'N/A' }}
                                            -
                                            {{ $deviceVerifyData->latestTestResult->longitude ?? 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.speed'):</strong>
                                        <span
                                            class="text-gray-800">{{ isset($deviceVerifyData->latestTestResult->speed) ? $deviceVerifyData->latestTestResult->speed . ' km/h' : 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.last_update'):</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->last_update ?? 'N/A' }}</span>
                                    </p>
                                </div>
                            @else
                                <p class="mt-3">
                                    @lang('messages.no_test_result')
                                </p>
                            @endif
                        </div>


                        <div class="grid gap-4 md:grid-cols-2">


                            <!-- Clients Dropdown -->
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] mb-3 block">{{ __('messages.client') }}</label>

                                <div>

                                    <livewire:select-dropdown placeholder="Search a Client"
                                        field-name="verification_client" fetch-method="getClients" />
                                </div>


                                @error('verification_client')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.vehicle_type')</label>
                                <select wire:model="verification_vehicle_type" id="renew_verification_vehicle_type"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300">
                                    <option value="car">@lang('messages.car')</option>
                                    <option value="bus">@lang('messages.bus')</option>
                                    <option value="boat">@lang('messages.boat')</option>
                                    <option value="motorcycle">@lang('messages.motorcycle')</option>
                                    <option value="scooter">@lang('messages.scooter')</option>
                                    <option value="truck">@lang('messages.truck')</option>
                                    <option value="van">@lang('messages.van')</option>
                                    <option value="tractor">@lang('messages.tractor')</option>
                                </select>

                                @error('verification_vehicle_type')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.brand')</label>
                                <input type="text" wire:model="vehicle_brand" id="renew_vehicle_brand"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.brand')">


                                @error('vehicle_brand')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.model')</label>
                                <input type="text" wire:model="vehicle_model" id="renew_vehicle_model"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.model')">


                                @error('vehicle_model')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.color')</label>
                                <input type="text" wire:model="vehicle_color" id="renew_vehicle_color"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.color')">


                                @error('vehicle_color')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.frame')</label>
                                <input type="text" wire:model="frame" id="renew_frame"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.frame')">


                                @error('frame')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>


                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.number_plate')</label>
                                <input type="text" wire:model="vehicle_number_plate" id="renew_vehicle_number_plate"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.number_plate')">

                                @error('vehicle_number_plate')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>


                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.registration_date')</label>
                                <input type="date" wire:model="vehicle_registration_date"
                                    id="renew_vehicle_registration_date"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300">
                                @error('vehicle_registration_date')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.travelled_distance') (In Km)</label>
                                <input type="number" wire:model="vehicle_km" id="renew_vehicle_km"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="Enter Kilometers">

                                @error('vehicle_km')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.starter_motor_block')</label>
                                <div class="flex items-center mt-2 space-x-4">
                                    <label class="flex items-center text-sm">
                                        <input type="radio" wire:model="starter_motor_block" value="Yes"
                                            class="text-primary focus:ring-primary h-4 w-4 border-[#D5D7DA] accent-primary">
                                        <span class="ml-2">@lang('messages.yes')</span>
                                    </label>
                                    <label class="flex items-center text-sm">
                                        <input type="radio" wire:model="starter_motor_block" value="No"
                                            class="text-primary focus:ring-primary h-4 w-4 border-[#D5D7DA] accent-primary">
                                        <span class="ml-2">@lang('messages.no')</span>
                                    </label>
                                </div>

                                @error('starter_motor_block')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">{{ __('messages.duration') }}</label>
                                <select wire:model.live="contract_duration"
                                    class="peer
                                    py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                    rounded-lg text-sm focus:border-primary outline-none transition-all
                                    duration-300">
                                    <option value="">@lang('messages.select_duration')</option>
                                    <option value="12">12 @lang('messages.months')</option>
                                    <option value="24">24 @lang('messages.months')</option>
                                    <option value="36">36 @lang('messages.months')</option>
                                    <option value="48">48 @lang('messages.months')</option>
                                    <option value="60">60 @lang('messages.months')</option>
                                    <option value="72">72 @lang('messages.months')</option>
                                </select>

                                @error('contract_duration')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">{{ __('messages.start_date') }}</label>
                                <input type="date" id="contract_start_date" wire:model.live="contract_start_date"
                                    placeholder="{{ __('messages.start_date') }}"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                                    rounded-lg text-sm outline-none transition-all
                                                    duration-300">

                                @error('contract_start_date')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">{{ __('messages.end_date') }}</label>
                                <input type="date" id="contract_end_date" wire:model="contract_end_date" disabled
                                    placeholder="{{ __('messages.end_date') }}"
                                    class="peer
                                                    py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                                    rounded-lg text-sm outline-none transition-all
                                                    duration-300">

                                @error('contract_end_date')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>



                            <div class="col-span-2">
                                <!-- Dealer Confirmation -->
                                <h3 class="mt-8 mb-2 font-medium">@lang('messages.mark_device_purchased')</h3>
                                <textarea wire:model="service_details"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    rows="4" placeholder="@lang('messages.details')"></textarea>


                                @error('service_details')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2">
                                <div class="flex gap-2 text-sm text-gray-500">
                                    <img class="size-5" src="{{ asset('assets/images/info-icon.svg') }}"
                                        alt="icon">
                                    @lang('messages.upload_installation_photo')
                                </div>
                            </div>
                            <div class="col-span-2 md:col-span-1">

                                <label
                                    class="border-2 mt-3 border-primary bg-[#F8F8FD] p-5 rounded-lg border-dashed text-gray-500 text-sm flex flex-col items-center justify-center gap-2 cursor-pointer hover:bg-[#e3e3f3] transition-all duration-300">
                                    <input type="file" wire:model="installation_image" class="hidden">

                                    <img class="size-8" src="{{ asset('assets/images/image.svg') }}" alt="image">
                                    <span wire:loading wire:target="installation_image">
                                        @lang('messages.loading')
                                    </span>
                                    <span wire:loading.remove wire:target="installation_image">
                                        @lang('messages.click_to_upload')
                                    </span>

                                </label>
                            </div>

                            <div class="flex justify-center col-span-2 md:col-span-1">
                                @if ($installation_image)
                                    <img class="mx-auto rounded-md shadow-sm h-36"
                                        src="{{ $installation_image->temporaryUrl() }}" alt="installation image">
                                @elseif($old_installation_image)
                                    <div class="relative w-fit h-fit">
                                        <button wire:click="removeImage" class="absolute -top-3 -right-3"><img
                                                class="size-5" src="{{ asset('assets/images/cancel.svg') }}"
                                                alt="cross"></button>
                                        <img class="mx-auto rounded-md shadow-sm h-36"
                                            src="{{ asset('storage/' . $old_installation_image) }}"
                                            alt="installation image">
                                    </div>
                                @endif
                            </div>


                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.inactive_client_account')</label>
                                <div class="flex items-center mt-2 space-x-4">
                                    <label class="flex items-center text-sm">
                                        <input type="radio" wire:model="inactive_client" value="1"
                                            class="text-primary focus:ring-primary h-4 w-4 border-[#D5D7DA] accent-primary">
                                        <span class="ml-2">@lang('messages.yes')</span>
                                    </label>
                                    <label class="flex items-center text-sm">
                                        <input type="radio" wire:model="inactive_client" value="0"
                                            class="text-primary focus:ring-primary h-4 w-4 border-[#D5D7DA] accent-primary">
                                        <span class="ml-2">@lang('messages.no')</span>
                                    </label>
                                </div>

                                @error('starter_motor_block')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                        </div>
                    </div>


                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false"
                            class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>
                        <button wire:click="verifyDevice" wire:loading.attr="disabled" wire:target="verifyDevice"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">

                            <span wire:loading.remove wire:target="verifyDevice"> {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="verifyDevice">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>


            </div>
        </x-slot:body>
    </x-modal>

    {{-- add client modal --}}
    <x-modal name="add-client-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black">{{ __('messages.add_client') }}</h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.type')</label>

                            <select id="type" wire:model.live="type" placeholder="@lang('messages.type')"
                                class="peer
                        py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                        rounded-lg text-sm focus:border-primary outline-none transition-all
                        duration-300 mt-2">
                                <option value="private">@lang('messages.private')</option>
                                <option value="company">@lang('messages.company')</option>
                            </select>

                            @error('type')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>



                        @if ($type == 'private')
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.surname')</label>
                                <input type="text" id="last_name" wire:model="last_name"
                                    placeholder="@lang('messages.surname')"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 mt-2">

                                @error('last_name')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        @endif

                        <div class="col-span-2 md:col-span-1">
                            @if ($type == 'company')
                                <label class="text-sm text-[#414651]">@lang('messages.company_name')</label>
                            @else
                                <label class="text-sm text-[#414651]">@lang('messages.name')</label>
                            @endif

                            <div class="relative mt-2">
                                <input type="text" wire:model="name" id="name"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@if ($type == 'company') @lang('messages.company_name') @else @lang('messages.name') @endif">
                            </div>

                            @error('name')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>


                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.username')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model.blur="username" id="username"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.username')">
                            </div>

                            @error('username')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">Email</label>

                            <div class="relative mt-2">
                                <input type="email" wire:model.blur="email" id="email"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="Email">

                            </div>

                            @error('email')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.phone_number')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="phone_number" id="phone_number"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.phone_number')">

                            </div>

                            @error('phone_number')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        <!-- Password Field -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.password') }}</label>
                            <div x-data="{
                                showPassword: false,
                                password: '',
                                generatePassword() {
                                    // Function to generate a random password (8 characters with letters and digits)
                                    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                                    let generatedPassword = '';
                                    for (let i = 0; i < 8; i++) {
                                        generatedPassword += chars.charAt(Math.floor(Math.random() * chars.length));
                                    }
                                    this.password = generatedPassword;
                                    // Set the generated password to Livewire model
                                    @this.set('password', generatedPassword);
                                }
                            }" class="relative">

                                <!-- Password Input Field -->
                                <input x-bind:type="showPassword ? 'text' : 'password'" id="password"
                                    x-model="password" wire:model.blur="password"
                                    placeholder="{{ __('messages.password') }}"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                   rounded-lg text-sm focus:border-primary outline-none transition-all
                                   duration-300">

                                <!-- password show hide button -->
                                <div class="absolute z-10 cursor-pointer top-3 right-2"
                                    @click="showPassword = !showPassword">
                                    <img x-show="!showPassword" id="show-icon" class="size-5"
                                        src="{{ asset('assets/images/eye.svg') }}" alt="Show password">
                                    <img x-show="showPassword" id="hide-icon" class="size-5"
                                        src="{{ asset('assets/images/eye-closed.svg') }}" alt="Hide password">
                                </div>

                                <!-- Generate Password Button -->
                                <button type="button" @click="generatePassword"
                                    class="absolute top-0 bottom-0 transition-all duration-300 rounded text-primary right-9">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="1.8" stroke="currentColor" class="size-6">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 0 0-3.7-3.7 48.678 48.678 0 0 0-7.324 0 4.006 4.006 0 0 0-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 0 0 3.7 3.7 48.656 48.656 0 0 0 7.324 0 4.006 4.006 0 0 0 3.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3-3 3" />
                                    </svg>
                                </button>

                            </div>


                            @error('password')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>



                        <!-- Address Field -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.address') }}</label>
                            <input type="text" id="address" wire:model.blur="address"
                                placeholder="{{ __('messages.address') }}"
                                class="peer
                                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm focus:border-primary outline-none transition-all
                                            duration-300">

                            @error('address')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Municipality Field -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.municipality') }}</label>
                            <input type="text" id="municipality" wire:model.blur="municipality"
                                placeholder="{{ __('messages.municipality') }}"
                                class="peer
                                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm focus:border-primary outline-none transition-all
                                            duration-300">

                            @error('municipality')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- ZIP Code Field -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.zip_code') }}</label>
                            <input type="text" id="zip_code" wire:model.blur="zip_code"
                                placeholder="{{ __('messages.zip_code') }}"
                                class="peer
                                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm focus:border-primary outline-none transition-all
                                            duration-300">

                            @error('zip_code')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Province Field -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.province') }}</label>
                            <input type="text" id="province" wire:model.blur="province"
                                placeholder="{{ __('messages.province') }}"
                                class="peer
                                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm focus:border-primary outline-none transition-all
                                            duration-300">

                            @error('province')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Tax Code Field -->
                        <div class="col-span-2 md:col-span-1">
                            @if ($type == 'company')
                                <label class="text-sm text-[#414651]">{{ __('messages.vat_number') }}</label>
                            @else
                                <label class="text-sm text-[#414651]">{{ __('messages.tax_code') }}</label>
                            @endif
                            <input type="text" id="tax_code" wire:model.blur="tax_code"
                                placeholder="@if ($type == 'company') {{ __('messages.vat_number') }}
                        @else {{ __('messages.tax_code') }} @endif"
                                class="peer
                                        py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                        rounded-lg text-sm focus:border-primary outline-none transition-all
                                        duration-300">

                            @error('tax_code')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>


                        <div class="flex items-center col-span-2">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked="" wire:model="is_active" id="active"
                                    class="sr-only peer">
                                <div
                                    class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary">
                                </div>
                            </label>

                            <label for="active" class="ms-2 cursor-pointer select-none text-sm text-[#414651]">
                                @lang('messages.active')
                            </label>
                        </div>
                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false;"
                            class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>

                        <button wire:click="addUpdateClient" wire:loading.attr="disabled"
                            wire:target="addUpdateClient" type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            <span wire:loading.remove wire:target="addUpdateClient"> {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="addUpdateClient">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>


    {{-- add contract modal --}}
    <x-modal name="add-contract-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false; $wire.call('clearRecords')" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black">{{ __('messages.edit_contract') }}</h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">

                        <div class="relative z-50 col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.device') }}</label>

                            <x-dropdown :options="$devices" name="device_id" />


                            @error('device_id')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.dealer') }}</label>

                            <x-dropdown :options="$dealers" name="dealer_id" />


                            @error('dealer_id')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.client') }}</label>

                            <x-dropdown :options="$clients" name="client_id" />


                            @error('client_id')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>


                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.duration') }}</label>
                            <select wire:model.live="duration"
                                class="peer
                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                            rounded-lg text-sm focus:border-primary outline-none transition-all
                            duration-300">
                                <option value=""> @lang('messages.select_duration')</option>
                                <option value="12">12 @lang('messages.months')</option>
                                <option value="24">24 @lang('messages.months')</option>
                                <option value="36">36 @lang('messages.months')</option>
                                <option value="48">48 @lang('messages.months')</option>
                                <option value="60">60 @lang('messages.months')</option>
                                <option value="72">72 @lang('messages.months')</option>
                            </select>

                            @error('duration')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.start_date') }}</label>
                            <input type="date" id="start_date" wire:model.live="start_date"
                                placeholder="{{ __('messages.start_date') }}"
                                class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm outline-none transition-all
                                            duration-300">

                            @error('start_date')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.end_date') }}</label>
                            <input type="date" id="end_date" wire:model="end_date" disabled
                                placeholder="{{ __('messages.end_date') }}"
                                class="peer
                                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm outline-none transition-all
                                            duration-300">

                            @error('end_date')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label
                                class="text-sm
                            text-[#414651]">{{ __('messages.signed') }}</label>
                            <select wire:model.live="signed"
                                class="peer
                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                            rounded-lg text-sm focus:border-primary outline-none transition-all
                            duration-300">
                                <option value="">@lang('messages.select_an_option')</option>
                                <option value="1">@lang('messages.signed')</option>
                                <option value="0">@lang('messages.unsigned')</option>
                            </select>

                            @error('signed')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.vehicle_type')</label>
                            <select wire:model="verification_vehicle_type" id="verification_vehicle_type"
                                class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300">
                                <option value="car">@lang('messages.car')</option>
                                <option value="bus">@lang('messages.bus')</option>
                                <option value="boat">@lang('messages.boat')</option>
                                <option value="motorcycle">@lang('messages.motorcycle')</option>
                                <option value="scooter">@lang('messages.scooter')</option>
                                <option value="truck">@lang('messages.truck')</option>
                                <option value="van">@lang('messages.van')</option>
                                <option value="tractor">@lang('messages.tractor')</option>
                            </select>

                            @error('verification_vehicle_type')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.brand')</label>
                            <input type="text" wire:model="vehicle_brand" id="vehicle_brand"
                                class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                placeholder="@lang('messages.brand')">


                            @error('vehicle_brand')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.model')</label>
                            <input type="text" wire:model="vehicle_model" id="vehicle_model"
                                class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                placeholder="@lang('messages.model')">


                            @error('vehicle_model')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.color')</label>
                            <input type="text" wire:model="vehicle_color" id="vehicle_color"
                                class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                placeholder="@lang('messages.color')">


                            @error('vehicle_color')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.frame')</label>
                            <input type="text" wire:model="frame" id="frame"
                                class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                placeholder="@lang('messages.frame')">


                            @error('frame')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>


                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.number_plate')</label>
                            <input type="text" wire:model="vehicle_number_plate" id="vehicle_number_plate"
                                class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                placeholder="@lang('messages.number_plate')">

                            @error('vehicle_number_plate')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>


                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.registration_date')</label>
                            <input type="date" wire:model="vehicle_registration_date"
                                id="vehicle_registration_date"
                                class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300">
                            @error('vehicle_registration_date')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.travelled_distance')
                                (@lang('messages.in_km'))</label>
                            <input type="number" wire:model="vehicle_km" id="vehicle_km"
                                class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                placeholder="@lang('messages.enter_km')">

                            @error('vehicle_km')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.starter_motor_block')</label>
                            <div class="flex items-center mt-2 space-x-4">
                                <label class="flex items-center text-sm">
                                    <input type="radio" wire:model="starter_motor_block" value="Yes"
                                        class="text-primary focus:ring-primary h-4 w-4 border-[#D5D7DA] accent-primary">
                                    <span class="ml-2">@lang('messages.yes')</span>
                                </label>
                                <label class="flex items-center text-sm">
                                    <input type="radio" wire:model="starter_motor_block" value="No"
                                        class="text-primary focus:ring-primary h-4 w-4 border-[#D5D7DA] accent-primary">
                                    <span class="ml-2">@lang('messages.no')</span>
                                </label>
                            </div>

                            @error('starter_motor_block')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>


                        <div class="col-span-2">
                            <!-- Dealer Confirmation -->
                            <h3 class="mt-8 mb-2 font-medium">@lang('messages.notes')</h3>
                            <textarea wire:model="service_details"
                                class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                rows="4" placeholder="@lang('messages.notes')"></textarea>


                            @error('service_details')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2">
                            <div class="flex gap-2 text-sm text-gray-500">
                                <img class="size-5" src="{{ asset('assets/images/info-icon.svg') }}"
                                    alt="icon">
                                @lang('messages.upload_installation_photo')
                            </div>
                        </div>
                        <div class="col-span-2 md:col-span-1">

                            <label
                                class="border-2 mt-3 border-primary bg-[#F8F8FD] p-5 rounded-lg border-dashed text-gray-500 text-sm flex flex-col items-center justify-center gap-2 cursor-pointer hover:bg-[#e3e3f3] transition-all duration-300">
                                <input type="file" wire:model="installation_image" class="hidden">

                                <img class="size-8" src="{{ asset('assets/images/image.svg') }}" alt="image">
                                <span wire:loading wire:target="installation_image">
                                    @lang('messages.loading')
                                </span>
                                <span wire:loading.remove wire:target="installation_image">
                                    @lang('messages.click_to_upload')
                                </span>

                            </label>
                        </div>

                        <div class="flex justify-center col-span-2 md:col-span-1">
                            <div class="relative w-fit">
                                @if ($installation_image)
                                    <img class="mx-auto rounded-md shadow-sm h-36"
                                        src="{{ $installation_image->temporaryUrl() }}" alt="installation image">
                                @elseif($old_installation_image)
                                    <button wire:click="removeImage" class="absolute top-0 right-0"><img
                                            class="size-5" src="{{ asset('assets/images/cancel.svg') }}"
                                            alt="cross"></button>
                                    <img class="mx-auto rounded-md shadow-sm h-36"
                                        src="{{ asset('storage/' . $old_installation_image) }}"
                                        alt="installation image">
                                @endif
                            </div>
                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label
                                class="text-sm
                            text-[#414651]">{{ __('messages.status') }}</label>
                            <select wire:model.live="contract_status"
                                class="peer
                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                            rounded-lg text-sm focus:border-primary outline-none transition-all
                            duration-300">
                                <option value="">@lang('messages.select_an_option')</option>
                                <option value="pending">@lang('messages.pending')</option>
                                <option value="active">@lang('messages.active')</option>
                                <option value="inactive">@lang('messages.inactive')</option>
                                <option value="expired">@lang('messages.expired')</option>
                            </select>

                            @error('contract_status')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>


                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false; $wire.call('clearRecords')"
                            class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>

                        <button wire:click="addUpdateContract" wire:loading.attr="disabled"
                            wire:target="addUpdateContract" type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            <span wire:loading.remove wire:target="addUpdateContract"> {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="addUpdateContract">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

    {{-- add contract modal --}}
    <x-modal name="view-contract-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false; $wire.call('clearRecords')" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black">@lang('messages.contract_details')</h2>
                </div>

                @if ($selectedContract)
                    <div class="mt-10">
                        <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.contract_number'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->contract_number }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.device'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->device->imei ?? 'N/A' }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.device') @lang('messages.model'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->device->model ?? 'N/A' }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.client'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->client?->user?->name ?? 'N/A' }}
                                    {{ $selectedContract->client?->last_name ?? '' }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.phone_number'):</strong>
                                <span
                                    class="text-gray-800">{{ $selectedContract->client?->phone_number ?? 'N/A' }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.dealer'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->dealer?->user?->name ?? 'N/A' }}
                                    {{ $selectedContract->dealer?->last_name ?? '' }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.duration'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->duration }}
                                    @lang('messages.months')</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.signed'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->signed ? 'Yes' : 'No' }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.start_date'):</strong>
                                <span
                                    class="text-gray-800">{{ $selectedContract->start_date ? \Carbon\Carbon::parse($selectedContract->start_date)->format('d/m/Y') : 'N/A' }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.end_date'):</strong>
                                <span
                                    class="text-gray-800">{{ $selectedContract->end_date ? \Carbon\Carbon::parse($selectedContract->end_date)->format('d/m/Y') : 'N/A' }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.status'):</strong>
                                <span class="text-gray-800">{{ __('messages.' . $selectedContract->status) }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.vehicle_type'):</strong>
                                <span
                                    class="text-gray-800">{{ ucfirst($selectedContract->verification_vehicle_type) }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.vehicle_brand'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->vehicle_brand }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.model'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->vehicle_model }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.color'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->vehicle_color }}</span>
                            </p>

                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.registration_date'):</strong>
                                <span
                                    class="text-gray-800">{{ $selectedContract->vehicle_registration_date }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.number_plate'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->vehicle_number_plate }}</span>
                            </p>

                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.travelled_distance'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->vehicle_km }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.starter_motor_block'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->starter_motor_block }}</span>
                            </p>

                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.client_response_received_at'):</strong>
                                <span
                                    class="text-gray-800">{{ \Carbon\Carbon::parse($selectedContract->responded_at)->format('d/m/Y H:i') }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.created_at'):</strong>
                                <span
                                    class="text-gray-800">{{ $selectedContract->created_at->format('d/m/Y H:i') }}</span>
                            </p>
                            <p class="flex items-center justify-between text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.frame'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->frame }}</span>
                            </p>

                            <p class="flex flex-col col-span-2 text-sm">
                                <strong class="w-48 text-gray-600">@lang('messages.mark_device_purchased'):</strong>
                                <span class="text-gray-800">{{ $selectedContract->service_details }}</span>
                            </p>
                        </div>

                        @if ($selectedContract->installation_image && auth()->user()->role == 'admin')
                            <div class="mt-3">
                                <p class="font-medium text-gray-600">@lang('messages.installation_image')</p>
                                <img class="mt-2" style="max-width: 300px;width:100%"
                                    src="{{ asset('storage/' . $selectedContract->installation_image) }}"
                                    alt="">

                            </div>
                        @endif
                    </div>
                @else
                    <p class="mt-4">@lang('messages.data_not_found')</p>
                @endif
            </div>
        </x-slot:body>
    </x-modal>

    {{-- delete contract modal --}}
    <x-modal name="delete-record-modal">
        <x-slot:body>

            <div class="flex items-center justify-between">
                <img class="size-10" src="{{ asset('assets/images/logout-icon.svg') }}" alt="logout">

                <button @click="show = false" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="1.5" stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>

            </div>

            <div class="mt-4">
                <h3 class="font-medium">
                    {{ __('messages.delete_confirmation') }}
                </h3>

                <p class="text-sm mt-2 text-[#535862]">
                    {{ __('messages.delete_warning') }}
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                        {{ __('messages.cancel') }}
                    </button>
                    <button wire:click="deleteRecord"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        {{ __('messages.delete') }}
                    </button>
                </div>
            </div>


        </x-slot:body>
    </x-modal>

</main>
