@section('devices_active', 'bg-primary/10 text-primary')

<main class="flex-shrink w-full overflow-auto">
    <section class="w-full p-6 mx-auto my-5">

        <div class="w-full p-5 mt-10 mb-20 overflow-hidden bg-white rounded-lg shadow-md md:rounded-2xl md:p-10">
            <div class="flex flex-wrap items-center justify-between flex-grow md:gap-4">
                <h2 class="text-xl font-semibold">@lang('messages.device_history'): {{ $deviceImei ?? 'N/A' }}</h2>


            </div>
            <div class="flex flex-col justify-center gap-4 mx-auto md:flex-row md:items-end">
                <div class="flex items-center gap-2">
                    <div class="w-full">
                        <label for="start-date" class="text-xs">@lang('messages.date')</label>
                        <input type="date" id="start-date" class="w-full px-3 py-2 text-sm bg-gray-100 rounded-lg">
                    </div>
                    {{-- <div class="w-full">
                        <label for="end-date" class="text-xs">@lang('messages.end_date')</label>
                        <input type="date" id="end-date" class="w-full px-3 py-2 text-sm bg-gray-100 rounded-lg">
                    </div> --}}
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-full">
                        <label for="start-time" class="text-xs">@lang('messages.start_time')</label>
                        <input type="time" id="start-time" class="w-full px-3 py-2 text-sm bg-gray-100 rounded-lg">
                    </div>
                    <div class="w-full">
                        <label for="end-time" class="text-xs">@lang('messages.end_time'):</label>
                        <input type="time" id="end-time" class="w-full px-3 py-2 text-sm bg-gray-100 rounded-lg">
                    </div>
                </div>
                <button id="load-data"
                    class="px-4 py-2 text-sm text-white rounded-lg bg-primary">@lang('messages.load_history')</button>
            </div>


            <div class="flex items-center justify-end gap-3 mt-4">
                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">

                    <button id="export-pdf" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                        class="p-2 text-sm text-white transition-all duration-300 bg-red-500 rounded-lg hover:bg-red-600 ">
                        <img src="{{ asset('assets/images/pdf.svg') }}" alt="excel" class="size-5">
                    </button>
                    <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0 transform scale-95"
                        x-transition:enter-end="opacity-100 transform scale-100"
                        x-transition:leave="transition ease-in duration-150"
                        x-transition:leave-start="opacity-100 transform scale-100"
                        x-transition:leave-end="opacity-0 transform scale-95"
                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                        style="display: none;">
                        @lang('messages.export_pdf')
                    </div>
                </div>
                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">

                    <button id="export-excel" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                        class="p-2 text-sm text-white transition-all duration-300 rounded-lg bg-emerald-500 hover:bg-emerald-600">
                        <img src="{{ asset('assets/images/excel.svg') }}" alt="excel" class="size-5">
                    </button>
                    <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0 transform scale-95"
                        x-transition:enter-end="opacity-100 transform scale-100"
                        x-transition:leave="transition ease-in duration-150"
                        x-transition:leave-start="opacity-100 transform scale-100"
                        x-transition:leave-end="opacity-0 transform scale-95"
                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                        style="display: none;">
                        @lang('messages.export_excel')
                    </div>
                </div>

            </div>

            <table id="data-table" class="mt-5 text-sm display">
                <thead>
                    <tr>
                        <th>@lang('messages.coordinates')</th>
                        <th>@lang('messages.gps_date')</th>
                        <th>@lang('messages.speed')</th>
                        <th>@lang('messages.ignition')</th>
                        <th>@lang('messages.movement')</th>
                        <th>@lang('messages.odometer')</th>
                        <th>@lang('messages.address')</th>
                        <th>@lang('messages.created_at')</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>

            <div id="report-template" class="container" style="display: none;">
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 0;
                        font-size: 12px;
                    }

                    .custom-container {
                        max-width: 1000px;
                        margin: 20px auto;
                        padding: 20px;
                        background: #ffffff;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                        border-radius: 10px;
                    }

                    .header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: 2px solid #450099;
                        margin-bottom: 20px;
                        padding-bottom: 10px;
                    }

                    .header .company-info {
                        text-align: right;
                        font-size: 12px;
                        color: #555;
                    }

                    h2 {
                        text-align: center;
                        margin-bottom: 20px;
                        font-size: 22px;
                        color: #450099;
                        font-weight: 700;
                    }

                    .table {
                        width: 100%;
                        border-collapse: collapse;
                        border-radius: 10px;
                        overflow: hidden;
                    }

                    .table th,
                    .table td {
                        border: 1px solid #ddd;
                        padding: 8px 10px;
                        text-align: center;
                        font-size: 12px;
                    }

                    .table th {
                        background-color: #450099;
                        color: #ffffff;
                        font-weight: normal;
                    }

                    .table tr:nth-child(even) {
                        background-color: #f9f9f9;
                    }

                    .table tr:hover {
                        background-color: #f1f1f1;
                    }

                    .table a {
                        color: #450099;
                        text-decoration: none;
                    }

                    .table a:hover {
                        text-decoration: underline;
                    }

                    .info-section {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin: 20px 0;
                        font-size: 14px;
                    }

                    .info-section div {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    }

                    .info-section span.label {
                        color: #450099;
                        font-weight: bold;
                        margin-right: 5px;
                    }

                    .info-section span.value {
                        color: #000;
                    }

                    /* Image section on a separate page */
                    .image-section-page {
                        page-break-before: always;
                        /* Forces the image to a new page */
                        text-align: center;
                        margin-top: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .image-section-page img {
                        width: 100%;
                        /* Adjust to the full width of A4 */
                        max-width: 100%;
                        /* Ensures it's not too large */
                        height: auto;
                        border-radius: 10px;

                    }
                </style>
                <div class="custom-container">
                    <div class="header">
                        <div class="logo">
                            <img src="{{ asset('assets/images/logo.png') }}" alt="MeMove" width="150">
                        </div>
                        <div class="company-info">
                            <p>Alltechnology Srl<br>Via Ogliara, 13<br>84135 Salerno (SA)<br>PI 06225780656</p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a><br>089 9341047</p>
                        </div>
                    </div>
                    <h2>@lang('messages.device_history_report')</h2>
                    <div class="info-section">
                        <div>
                            <span class="label">Targa:</span>
                            <span class="value">{{ $plate }}</span>
                        </div>
                        <div>
                            <span class="label">@lang('messages.exported_date'):</span>
                            <span class="value" id="exportedDate"></span>
                        </div>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>@lang('messages.timestamp')</th>
                                <th>@lang('messages.movement')</th>
                                <th>@lang('messages.ignition')</th>
                                <th>@lang('messages.speed')</th>
                                <th>@lang('messages.street_name')</th>
                            </tr>
                        </thead>
                        <tbody id="table-body">
                            <!-- Rows will be dynamically inserted here -->
                        </tbody>
                    </table>
                    <div class="info-section">
                        <div>
                            <span class="label">@lang('messages.total_distance_covered'):</span>
                            <span class="value" id="odometerValue">0</span>
                        </div>
                    </div>
                    <!-- Image on a separate page -->
                    <div class="image-section-page">
                        <img id="mapImage" alt="Map Snapshot"
                            style="width:100%;max-width: 100%;margin: 20px auto 0px auto" />

                    </div>
                </div>

            </div>

            <link rel="stylesheet" href="https://cdn.datatables.net/2.1.8/css/dataTables.dataTables.min.css">

            <script src="https://cdn.datatables.net/2.1.8/js/dataTables.min.js"></script>

            <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>



            <script>
                var deviceAllData;
                $(document).ready(function() {
                    const table = $('#data-table').DataTable({
                        order: [
                            [7, 'asc']
                        ] // Sort by 8th column (Created At/last_update) in ascending order
                    });

                    $('#load-data').on('click', function() {
                        // update button text
                        $('#load-data').html(`@lang('messages.loading')`);

                        const imei = "{{ $deviceImei }}";
                        let startDate = $('#start-date').val(); // Expected: YYYY-MM-DD
                        const startTime = $('#start-time').val() || "00:00:00";
                        const endTime = $('#end-time').val() || "23:59:59";

                        if (!imei || !startDate) {
                            $('#load-data').html(`@lang('messages.load_history')`);
                            alert("Per favore, inserisci la data di inizio!");
                            return;
                        }

                        // ✅ Convert YYYY-MM-DD → DD/MM/YYYY before parsing
                        const formatToDDMMYYYY = (dateStr) => {
                            const [year, month, day] = dateStr.split('-');
                            return `${day}/${month}/${year}`;
                        };

                        startDate = formatToDDMMYYYY(startDate);

                        // ✅ Parse date in DD/MM/YYYY HH:mm or DD/MM/YYYY HH:mm:ss format
                        const parseDateTime = (dateStr, timeStr = "00:00:00") => {
                            try {
                                const [day, month, year] = dateStr.split('/').map(Number);
                                const timeParts = timeStr.split(':').map(Number);
                                const hours = timeParts[0] || 0;
                                const minutes = timeParts[1] || 0;
                                const seconds = timeParts[2] || 0; // Handle optional seconds
                                return new Date(year, month - 1, day, hours, minutes, seconds);
                            } catch (error) {
                                $('#load-data').html(`@lang('messages.load_history')`);
                                console.error("❌ Error parsing date:", dateStr, timeStr, error);
                                return null;
                            }
                        };

                        // ✅ Convert start and end date-time values
                        const startDateTime = parseDateTime(startDate, startTime);
                        const endDateTime = parseDateTime(startDate, endTime);

                        console.log("✅ Start DateTime:", startDateTime);
                        console.log("✅ End DateTime:", endDateTime);

                        if (isNaN(startDateTime) || isNaN(endDateTime)) {
                            $('#load-data').html(`@lang('messages.load_history')`);
                            alert("Formato data non valido. Per favore inserisci una data valida.");
                            return;
                        }

                        // ✅ Generate file path for the selected day
                        const formattedDate = startDate.replace(/\//g, '-'); // Convert to DD-MM-YYYY
                        const filePath = `/data/history/${imei}/${formattedDate}.json`;

                        console.log("📂 Fetching file:", filePath);

                        // ✅ Fetch data from the file
                        fetch(filePath)
                            .then(res => res.ok ? res.json() : [])
                            .then((allData) => {
                                console.log("📊 Total Data Fetched:", allData.length);

                                if (allData.length === 0) {
                                    $('#load-data').html(`@lang('messages.load_history')`);
                                    alert('Nessun dato trovato per la data selezionata.');
                                    return;
                                }

                                // ✅ Debugging each item's date before filtering
                                allData.forEach(item => {
                                    if (!item.last_update) return;

                                    const [datePart, timePart] = item.last_update.trim().split(" ");
                                    const itemDateTime = parseDateTime(datePart, timePart);

                                    // console.log(
                                    //     `📌 Item DateTime: ${itemDateTime} | Last Update: ${item.last_update}`
                                    // );
                                });

                                // ✅ Filtering Data Based on Last Update
                                const filteredData = allData.filter((item) => {
                                    if (!item.last_update) return false;

                                    const [datePart, timePart] = item.last_update.trim().split(" ");
                                    const itemDateTime = parseDateTime(datePart, timePart);

                                    // 🛠 Debugging issue: Check if itemDateTime is valid
                                    if (isNaN(itemDateTime)) {
                                        console.error("❌ Invalid itemDateTime:", item.last_update);
                                        return false;
                                    }


                                    return itemDateTime >= startDateTime && itemDateTime <= endDateTime;
                                });

                                if (filteredData.length === 0) {
                                    $('#load-data').html(`@lang('messages.load_history')`);
                                    alert('Nessun dato trovato nel range di tempo selezionato.');
                                    return;
                                }

                                // ✅ Prepare Data for Table Display
                                const tableData = filteredData.map((item) => {
                                    if (item.longitude == null || item.latitude == null) return false;
                                    return [
                                        `Long: ${item.longitude}, Lat: ${item.latitude}`,
                                        `Altitude: ${item.altitude}, Angle: ${item.angle}, Satellites: ${item.satelites}`,
                                        item.speed,
                                        item["239"] === 0 ? "@lang('messages.off')" :
                                        "@lang('messages.on')",
                                        (item["240"] === 0 && item["speed"] == 0) ?
                                        "@lang('messages.stopped')" : "@lang('messages.moving')",
                                        item["16"] / 1000, // Convert to km
                                        item?.address || "N/A",
                                        item.last_update,
                                    ];
                                }).filter(Boolean);

                                // ✅ Update Table
                                table.clear();
                                table.rows.add(tableData);
                                table.draw();

                                // ✅ Show Data on Map
                                showDevicePathOnMap(filteredData);
                            })
                            .catch((error) => {
                                $('#load-data').html(`@lang('messages.load_history')`);
                                alert('Nessun dato trovato per la data selezionata.');
                            });
                    });





                    // Function to populate the table with formattedData
                    var tableBody = document.getElementById("table-body");

                    function populateTable(data) {
                        console.log(data);
                        tableBody.innerHTML = ''; // Clear existing rows
                        data.forEach((row) => {
                            const tr = document.createElement("tr");
                            row.forEach((cell) => {
                                const td = document.createElement("td");
                                td.textContent = cell;
                                tr.appendChild(td);
                            });
                            tableBody.appendChild(tr);
                        });
                    }
                    // Populate the table before export

                    // document.getElementById('export-pdf').addEventListener('click', async () => {

                    //     document.getElementById('export-pdf').innerHTML = '@lang('messages.exporting')..';

                    //     exportMapAsImage()


                    //     const {
                    //         jsPDF
                    //     } = window.jspdf;

                    //     const currentPageData = table.rows().data().toArray();


                    //     if (!currentPageData || currentPageData.length <= 0) {
                    //         document.getElementById('export-pdf').innerHTML =
                    //             `<img src="{{ asset('assets/images/pdf.svg') }}" alt="excel" class="size-5">`;
                    //         alert('No data to export');
                    //         return;
                    //     }

                    //     console.log(currentPageData);
                    //     let firstOdometer = null;
                    //     let lastOdometer = null;



                    //     // Format the current page data
                    //     const formattedPageData = await Promise.all(currentPageData.map(async (item, index) => {
                    //         // Extract data from each row in the current page
                    //         const coordinates = item[0]; // Coordinates (Long, Lat)
                    //         const gpsData = item[1]; // GPS data (Altitude, Angle, Satellites)
                    //         const speed = item[2]; // Speed
                    //         const ignition = item[3]; // Ignition (On/Off)
                    //         const movement = item[4]; // Movement (Moving/Stopped)
                    //         const odometer = item[5]; // Odometer (in meters)
                    //         const timestampFormatted = item[6]; // Timestamp (already formatted)

                    //         // Extract latitude and longitude from the "Long:9.0468683, Lat:45.523845" format
                    //         const [longitudeStr, latitudeStr] = coordinates.split(', ').map(
                    //             coord => coord.split(':')[1].trim());
                    //         const lat = parseFloat(latitudeStr);
                    //         const lon = parseFloat(longitudeStr);


                    //         // Set the first odometer value
                    //         if (firstOdometer === null) {
                    //             firstOdometer = odometer;
                    //         }

                    //         // Continuously update the last odometer value
                    //         lastOdometer = odometer;

                    //         // Return the formatted data as a new row
                    //         return [timestampFormatted, movement, ignition, speed];
                    //     }));


                    //     // Calculate total distance covered in kilometers
                    //     let totalDistance = 0;
                    //     if (firstOdometer !== null && lastOdometer !== null) {
                    //         totalDistance = (firstOdometer - lastOdometer) /
                    //             1000; // Convert from meters to kilometers
                    //     }

                    //     document.getElementById('odometerValue').textContent = totalDistance + ' KM';

                    //     // Populate the table with formatted data
                    //     populateTable(formattedPageData);

                    //     // Temporarily show the template for rendering
                    //     const template = document.getElementById('report-template');


                    //     template.style.display = 'block';

                    //     // Use html2canvas to capture the template
                    //     const canvas = await html2canvas(template, {
                    //         scale: 2, // High resolution
                    //         useCORS: false
                    //     });

                    //     // Hide the template again
                    //     template.style.display = 'none';

                    //     // Convert canvas to image
                    //     const imgData = canvas.toDataURL('image/png');

                    //     // Create a PDF document
                    //     const pdf = new jsPDF('p', 'mm', 'a4');

                    //     // Calculate width and height for the image in the PDF
                    //     const pdfWidth = pdf.internal.pageSize.getWidth();
                    //     const pdfHeight = (canvas.height * pdfWidth) / canvas.width;

                    //     // Add the image to the PDF
                    //     pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);

                    //     // Save the PDF
                    //     pdf.save('custom-report.pdf');


                    //     document.getElementById('export-pdf').innerHTML =
                    //         `<img src="{{ asset('assets/images/pdf.svg') }}" alt="excel" class="size-5">`;

                    // });

                    document.getElementById('export-pdf').addEventListener('click', async () => {
                        document.getElementById('export-pdf').innerHTML = 'Exporting...';

                        const mapImage = await exportMapAsImageWeb(); // Wait for the map image




                        const currentPageData = table.rows().data().toArray();
                        if (!currentPageData || currentPageData.length === 0) {
                            document.getElementById('export-pdf').innerHTML = 'Export PDF';
                            alert('No data to export');
                            return;
                        }

                        // Collect form input for filters (start_time & end_time)
                        const startTime = document.getElementById('start-time').value;
                        const endTime = document.getElementById('end-time').value;

                        // Prepare payload
                        const payload = {
                            imei: "{{ $deviceImei }}", // Replace with actual IMEI value
                            map_image: mapImage,
                            data: currentPageData
                        };

                        // Send request to Laravel API
                        try {
                            const response = await fetch('/api/export-history-web', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify(payload)
                            });

                            const blob = await response.blob();
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = 'custom-report.pdf';
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                        } catch (error) {
                            console.error('Error exporting PDF:', error);
                            alert('Failed to export PDF.');
                        }

                        document.getElementById('export-pdf').innerHTML = 'Export PDF';
                    });



                });
            </script>



            <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css" />
            <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js"></script>

            <!-- Leaflet Image Plugin -->
            <script src="https://unpkg.com/leaflet-image@latest/leaflet-image.js"></script>

            {{-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet-easyprint@2.1.9/libs/leaflet.min.css"> --}}
            <script src="https://cdn.jsdelivr.net/npm/leaflet-easyprint@2.1.9/dist/bundle.min.js"></script>


            <!-- map -->
            <section wire:ignore>
                <div class="h-[90vh] w-full relative -z-0">
                    <div id="worldMap" class="w-full h-full"></div>
                </div>
            </section>

            <script src="https://cdnjs.cloudflare.com/ajax/libs/dom-to-image/2.6.0/dom-to-image.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>



            <!-- Your Map Script -->
            <script>
                // Initialize the map
                var map = L.map('worldMap').setView([45.5435883, 9.0935283],
                    2); // Default view at first device location with zoom 3
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

                // Add the easyPrint control
                var printPlugin = L.easyPrint({
                    title: 'Export as PNG', // Tooltip text (this will not be shown in automatic trigger)
                    position: 'topleft', // Button position on the map
                    exportOnly: true, // Only export the map, not print
                    filename: 'map_snapshot', // Set the filename for the export
                    sizeModes: ['A4Landscape'], // Use the current size of the map
                }).addTo(map);

                // Function to show the device path on the map
                function showDevicePathOnMap(deviceData) {
                    deviceData = deviceData.filter(point => point.latitude !== 0 || point.longitude !== 0);

                    // Extract the latitude and longitude from the device data
                    let path = deviceData.map(point => [point.latitude, point.longitude]);

                    // Remove any existing path or marker if already displayed
                    if (window.historyPath) {
                        map.removeLayer(window.historyPath);
                    }
                    if (window.historyMarker) {
                        map.removeLayer(window.historyMarker);
                    }

                    let vehicleType = "{{ $vehicle_type }}";
                    // Dynamic icon URL based on the vehicle type
                    let iconUrl = `{{ asset('assets/images/default/green.svg') }}`;
                    if (vehicleType) {
                        iconUrl = `{{ asset('assets/images/${vehicleType}/green.svg') }}`;
                    }

                    // Create custom icon
                    let customIcon = L.icon({
                        iconUrl: iconUrl,
                        iconSize: [50, 90], // Adjust the size of the icon
                        iconAnchor: [20, 40], // Anchor point of the icon
                        popupAnchor: [0, -40] // Popup position (relative to icon)
                    });

                    // Create the polyline with violet color
                    window.historyPath = L.polyline(path, {
                        color: '#450099', // Violet color for the path
                        weight: 5, // Line thickness
                        opacity: 1 // Line opacity
                    }).addTo(map);

                    // Fit the map view to the bounds of the polyline with extra padding
                    map.fitBounds(window.historyPath.getBounds(), {
                        padding: [50, 50] // Add padding around the path to zoom out slightly
                    });

                    // Place a custom marker at the first point of the path
                    window.historyMarker = L.marker(path[0], {
                        icon: customIcon
                    }).addTo(map);


                    // // Ensure the map is fully loaded before taking the snapshot
                    // map.whenReady(function() {
                    //     // Trigger the export automatically after the map is fully loaded
                    //     var imgData = printPlugin.printMap('A4Landscape page', 'MapExport');

                    //     // Wait for the image data to be generated and set it as the source of the image element
                    //     setTimeout(function() {
                    //         var imgSrc = imgData; // This will be the base64 image data URL
                    //         document.getElementById('mapImage').src = imgSrc; // Set image source
                    //     }, 1000); // Add a small delay to allow the map snapshot to complete
                    // });


                }

                // Add a function to export the map as an image
                function exportMapAsImage() {
                    // Get the map container element
                    var mapContainer = document.getElementById('worldMap');

                    // Use dom-to-image to capture the map
                    domtoimage.toBlob(mapContainer)
                        .then(function(blob) {
                            // Save the image using FileSaver.js
                            // saveAs(blob, 'map_snapshot.png');

                            // Optional: Display the image in an <img> element
                            var imgURL = URL.createObjectURL(blob);
                            document.getElementById('mapImage').src = imgURL;
                        })
                        .catch(function(error) {
                            console.error('Failed to capture map:', error);
                        });
                }

                async function exportMapAsImageWeb() {
                    const mapContainer = document.getElementById('worldMap');

                    try {
                        const dataUrl = await domtoimage.toPng(mapContainer, {
                            quality: 1, // Ensure high quality
                            bgcolor: "white", // Prevent transparency issues
                            useCORS: true // Enable cross-origin resource sharing
                        });

                        return dataUrl; // Base64 image
                    } catch (error) {
                        console.error('Failed to capture map:', error);
                        return null;
                    }
                }
            </script>


            <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

            <script>
                document.getElementById('export-excel').addEventListener('click', function() {
                    let table = $('#data-table').DataTable(); // Get the DataTable instance
                    let data = [];

                    // Add headers
                    let headers = [
                        "@lang('messages.coordinates', [], 'it')",
                        "@lang('messages.gps_date', [], 'it')",
                        "@lang('messages.speed', [], 'it')",
                        "@lang('messages.ignition', [], 'it')",
                        "@lang('messages.movement', [], 'it')",
                        "@lang('messages.odometer', [], 'it')",
                        "@lang('messages.address', [], 'it')",
                        "@lang('messages.created_at', [], 'it')",
                    ];
                    data.push(headers);
                    // Get all DataTable rows (only visible ones with 'page' mode or all with 'all')
                    let tableData = table.rows({
                        search: 'applied'
                    }).data().toArray();

                    tableData.forEach(row => {
                        data.push([
                            row[0], // Coordinates
                            row[1], // GPS Date
                            row[2], // Speed
                            row[3], // Ignition
                            row[4], // Movement
                            row[5], // Odometer
                            row[6], // Address
                            row[7], // Created At
                        ]);
                    });

                    // Convert to worksheet and create Excel file
                    let ws = XLSX.utils.aoa_to_sheet(data);
                    let wb = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(wb, ws, "Report");

                    // Save the file
                    XLSX.writeFile(wb, "history-report.xlsx");
                });
            </script>





        </div>
    </section>

    {{-- modals --}}



</main>
