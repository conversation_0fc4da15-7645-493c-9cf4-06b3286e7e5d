@section('admins_active', 'bg-primary/10 text-primary')

<main class="flex-shrink w-full overflow-auto">
    <section class="w-full p-6 mx-auto my-5">

        <div class="flex justify-end">
            <button @click="$dispatch('open-modal', {name:'add-admin-modal'})"
                class="mt-4 flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-primaryDark ">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>

                <span>{{ __('messages.add_new_record') }}</span>
            </button>
        </div>

        <div class="w-full p-5 mt-10 mb-20 overflow-hidden bg-white rounded-lg shadow-md md:rounded-2xl md:p-10">
            <div class="flex flex-wrap items-center justify-between flex-grow md:gap-4">
                <h2 class="text-xl font-semibold">{{ __('messages.admins') }}</h2>

                <div class="flex flex-col items-center flex-grow gap-4 md:flex-row md:justify-end">

                    <div class="w-full max-w-xs">
                        <div class="flex items-center w-full space-x-5">
                            <div class="flex w-full p-3 space-x-2 text-sm bg-gray-100 rounded-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <input wire:model.live.debounce.500ms="search" class="bg-gray-100 outline-none"
                                    type="search" placeholder="{{ __('messages.search') }}" />
                            </div>
                        </div>
                    </div>

                    <div class="w-fit">
                        <select wire:model.live="status" class="outline-none">
                            <option selected>{{ __('messages.status') }}</option>
                            <option value="1">{{ __('messages.active') }}</option>
                            <option value="0">{{ __('messages.inactive') }}</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="w-full mt-10 mb-5 overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr
                            class="text-xs font-semibold tracking-wide text-left text-gray-900 uppercase border-b border-gray-600">
                            <th class="px-4 py-3 border whitespace-nowrap">{{ __('messages.name') }}</th>
                            <th class="px-4 py-3 border whitespace-nowrap">Email</th>
                            <th class="px-4 py-3 border whitespace-nowrap">{{ __('messages.role') }}</th>
                            <th class="px-4 py-3 border whitespace-nowrap">{{ __('messages.status') }}</th>
                            <th class="px-4 py-3 border whitespace-nowrap">{{ __('messages.added_at') }}</th>
                            <th class="px-4 py-3 border whitespace-nowrap text-end min-w-28">
                                {{ __('messages.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white">
                        @forelse ($admins as $admin)
                            <tr class="text-gray-700">
                                <td class="px-4 py-3">
                                    <div>
                                        <p class="text-sm font-semibold text-black">{{ $admin->name ?? '' }}
                                        </p>
                                        <p class="text-xs text-gray-600">{{ $admin->username ?? 'N/A' }}
                                        </p>
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm font-medium">{{ $admin->email ?? 'N/A' }}</td>

                                <td class="px-4 py-3 text-sm">
                                    @if ($admin->role == 'admin')
                                        @lang('messages.admin')
                                    @elseif($admin->role == 'technician')
                                        @lang('messages.technician')
                                    @elseif($admin->role == 'operator')
                                        @lang('messages.operator')
                                    @elseif($admin->role == 'warehouse_operator')
                                        @lang('messages.warehouse_operator')
                                    @else
                                        N/A
                                    @endif
                                </td>
                                <td class="px-4 py-3 text-xs">
                                    @if ($admin->is_active == 1)
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full text-emerald-700 bg-emerald-100">
                                            {{ __('messages.active') }} </span>
                                    @else
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full text-rose-700 bg-rose-100">
                                            {{ __('messages.inactive') }} </span>
                                    @endif
                                </td>

                                <td class="px-4 py-3 text-sm">{{ $admin->created_at->format('d/m/Y H:i') }}</td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="flex items-center justify-end gap-2">
                                        <!-- Reset Password Button with Tooltip -->
                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="sendPasswordResetLink('{{ $admin->email }}')"
                                                wire:loading.attr="disabled" wire:target="sendPasswordResetLink"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5"
                                                    src="{{ asset('assets/images/reset-password.svg') }}"
                                                    alt="Reset Password">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">

                                                <span wire:loading.remove wire:target="sendPasswordResetLink">
                                                    {{ __('messages.send_password_reset_link') }}
                                                </span>
                                                <span wire:loading wire:target="sendPasswordResetLink">
                                                    {{ __('messages.sending') }}...
                                                </span>
                                            </div>
                                        </div>


                                        <!-- Edit Button with Tooltip -->
                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="editRecord({{ $admin->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5" src="{{ asset('assets/images/edit.svg') }}"
                                                    alt="Edit">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                {{ __('messages.edit') }}
                                            </div>
                                        </div>

                                        <!-- Delete Button with Tooltip -->
                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="deleteRecordConfirmation({{ $admin->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5" src="{{ asset('assets/images/delete.svg') }}"
                                                    alt="Delete">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                {{ __('messages.delete') }}
                                            </div>
                                        </div>
                                    </div>

                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="px-4 py-3 text-center">{{ __('messages.no_record_found') }}
                                </td>
                            </tr>
                        @endforelse

                    </tbody>
                </table>
            </div>

            {{ $admins->links('livewire.custom-pagination') }}

        </div>
    </section>

    {{-- modals --}}

    {{-- add admin modal --}}
    <x-modal name="add-admin-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false; $wire.call('clearRecords')" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black">{{ __('messages.add_admin') }}</h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.name')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model.blur="name" id="name"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.name')">
                            </div>

                            @error('name')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.username')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model.blur="username" id="username"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.username')">
                            </div>

                            @error('username')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">Email</label>

                            <div class="relative mt-2">
                                <input type="email" wire:model.blur="email" id="email"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="Email">

                            </div>

                            @error('email')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        <!-- Password Field -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.password') }}</label>
                            <input type="password" id="password" wire:model.blur="password"
                                placeholder="{{ __('messages.password') }}"
                                class="peer
                                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm focus:border-primary outline-none transition-all
                                            duration-300">

                            @error('password')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2">
                            <label class="text-sm text-[#414651]">{{ __('messages.role') }}</label>
                            <select wire:model.blur="role"
                                class="peer
                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                            rounded-lg text-sm focus:border-primary outline-none transition-all
                            duration-300">
                                <option value="">{{ __('messages.select_a_role') }}</option>
                                <option value="admin">@lang('messages.admin')</option>
                                <option value="technician">@lang('messages.technician')</option>
                                <option value="warehouse_operator">@lang('messages.warehouse_operator')</option>
                                <option value="operator">@lang('messages.operator')</option>
                            </select>

                            @error('role')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>


                        <div class="flex items-center col-span-2">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked="" wire:model="is_active" id="active"
                                    class="sr-only peer">
                                <div
                                    class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary">
                                </div>
                            </label>

                            <label for="active" class="ms-2 cursor-pointer select-none text-sm text-[#414651]">
                                @lang('messages.active')
                            </label>
                        </div>
                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false; $wire.call('clearRecords')"
                            class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>

                        <button wire:click="addUpdateAdmin" wire:loading.attr="disabled" wire:target="addUpdateAdmin"
                            type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            <span wire:loading.remove wire:target="addUpdateAdmin"> {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="addUpdateAdmin">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

    {{-- delete admin modal --}}
    <x-modal name="delete-record-modal">
        <x-slot:body>

            <div class="flex items-center justify-between">
                <img class="size-10" src="{{ asset('assets/images/logout-icon.svg') }}" alt="logout">

                <button @click="show = false" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>

            </div>

            <div class="mt-4">
                <h3 class="font-medium">
                    {{ __('messages.delete_confirmation') }}
                </h3>

                <p class="text-sm mt-2 text-[#535862]">
                    {{ __('messages.delete_warning') }}
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                        {{ __('messages.cancel') }}
                    </button>
                    <button wire:click="deleteRecord"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        {{ __('messages.delete') }}
                    </button>
                </div>
            </div>


        </x-slot:body>
    </x-modal>

</main>
