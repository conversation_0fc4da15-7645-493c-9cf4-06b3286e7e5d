@section('devices_active', 'bg-primary/10 text-primary')
@php
    $user = auth()->user();
@endphp
<main class="flex-shrink w-full overflow-auto">
    <section class="w-full p-6 mx-auto my-5">

        {{-- add device --}}
        {{-- <div class="flex justify-end">
            <button @click="$dispatch('open-modal', {name:'add-device-modal'})"
                class="mt-4 flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-primaryDark ">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>

                <span>{{ __('messages.add_new_record') }}</span>
            </button>
        </div> --}}

        <div class="w-full p-5 mt-10 mb-20 overflow-x-hidden bg-white rounded-lg shadow-md md:rounded-2xl md:p-10">
            <div class="flex flex-wrap items-center justify-between flex-grow md:gap-4">
                <h2 class="text-xl font-semibold">{{ __('messages.devices') }}</h2>

                <div class="flex flex-col items-center flex-grow gap-4 md:flex-row md:justify-end">

                    {{-- export/import buttons --}}
                    <div class="flex items-center ms-auto">
                        @if ($user->role == 'admin')
                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">

                                <button wire:click="exportPDF" @mouseenter="showTooltip = true"
                                    @mouseleave="showTooltip = false"
                                    class="p-2 text-sm text-white transition-all duration-300 bg-red-500 rounded-lg hover:bg-red-600 ">
                                    <img src="{{ asset('assets/images/pdf.svg') }}" alt="excel" class="size-5">
                                </button>
                                <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                    x-transition:enter-start="opacity-0 transform scale-95"
                                    x-transition:enter-end="opacity-100 transform scale-100"
                                    x-transition:leave="transition ease-in duration-150"
                                    x-transition:leave-start="opacity-100 transform scale-100"
                                    x-transition:leave-end="opacity-0 transform scale-95"
                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                    style="display: none;">
                                    @lang('messages.export_pdf')
                                </div>
                            </div>
                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                <button wire:click="exportExcel" @mouseenter="showTooltip = true"
                                    @mouseleave="showTooltip = false"
                                    class="p-2 text-sm text-white transition-all duration-300 rounded-lg ms-2 bg-emerald-500 hover:bg-emerald-600 ">
                                    <img src="{{ asset('assets/images/excel.svg') }}" alt="excel" class="size-5">
                                </button>
                                <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                    x-transition:enter-start="opacity-0 transform scale-95"
                                    x-transition:enter-end="opacity-100 transform scale-100"
                                    x-transition:leave="transition ease-in duration-150"
                                    x-transition:leave-start="opacity-100 transform scale-100"
                                    x-transition:leave-end="opacity-0 transform scale-95"
                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                    style="display: none;">
                                    @lang('messages.export_excel')
                                </div>
                            </div>
                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                <button @click="$dispatch('open-modal',{name:'import-devices'})"
                                    @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                                    class="p-2 text-sm text-white transition-all duration-300 rounded-lg ms-2 bg-sky-500 hover:bg-sky-600 ">
                                    <img src="{{ asset('assets/images/import.svg') }}" alt="excel" class="size-5">
                                </button>
                                <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                    x-transition:enter-start="opacity-0 transform scale-95"
                                    x-transition:enter-end="opacity-100 transform scale-100"
                                    x-transition:leave="transition ease-in duration-150"
                                    x-transition:leave-start="opacity-100 transform scale-100"
                                    x-transition:leave-end="opacity-0 transform scale-95"
                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                    style="display: none;">
                                    @lang('messages.import_devices')
                                </div>
                            </div>
                        @endif
                    </div>
                    {{-- search --}}
                    <div class="w-full max-w-xs">
                        <div class="flex items-center w-full space-x-5">
                            <div class="flex w-full p-3 space-x-2 text-sm bg-gray-100 rounded-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <input wire:model.live.debounce.500ms="search" class="bg-gray-100 outline-none"
                                    type="search" placeholder="{{ __('messages.search') }}" />
                            </div>
                        </div>
                    </div>

                    {{-- filters --}}
                    <div x-data="{ open: false }"
                        class="relative flex items-center justify-end w-fit ms-auto me-3 md:ms-0">
                        <!-- Dropdown button -->
                        <button @click="open = !open" class="text-gray-400">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                class="size-5">
                                <path fill-rule="evenodd"
                                    d="M3.792 2.938A49.069 49.069 0 0 1 12 2.25c2.797 0 5.54.236 8.209.688a1.857 1.857 0 0 1 1.541 1.836v1.044a3 3 0 0 1-.879 2.121l-6.182 6.182a1.5 1.5 0 0 0-.439 1.061v2.927a3 3 0 0 1-1.658 2.684l-1.757.878A.75.75 0 0 1 9.75 21v-5.818a1.5 1.5 0 0 0-.44-1.06L3.13 7.938a3 3 0 0 1-.879-2.121V4.774c0-.897.64-1.683 1.542-1.836Z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>

                        <!-- Dropdown menu -->
                        <div x-show="open" x-transition:enter="transition ease-out duration-200"
                            x-transition:enter-start="opacity-0 transform scale-95"
                            x-transition:enter-end="opacity-100 transform scale-100"
                            x-transition:leave="transition ease-in duration-150"
                            x-transition:leave-start="opacity-100 transform scale-100"
                            x-transition:leave-end="opacity-0 transform scale-95"
                            class="absolute top-0 z-50 p-4 mt-2 space-y-2 bg-white rounded-md shadow-lg right-full md:w-64 w-36"
                            @click.outside="open = false">
                            <select wire:model.live="status"
                                class="block w-full p-2 mt-2 text-sm border border-gray-200 rounded-md outline-none">
                                <option selected>{{ __('messages.status') }}</option>
                                <option value="1">{{ __('messages.working') }}</option>
                                <option value="0">{{ __('messages.not_working') }}</option>
                            </select>
                            <select wire:model.live="tested"
                                class="block w-full p-2 mt-2 text-sm border border-gray-200 rounded-md outline-none">
                                <option selected>@lang('messages.testing')</option>
                                <option value="1">@lang('messages.tested')</option>
                                <option value="0">@lang('messages.not_tested')</option>
                            </select>
                            <select wire:model.live="verified"
                                class="block w-full p-2 mt-2 text-sm border border-gray-200 rounded-md outline-none">
                                <option selected>{{ __('messages.verified') }}</option>
                                <option value="1">{{ __('messages.verified') }}</option>
                                <option value="0">Non {{ __('messages.verified') }}</option>
                            </select>


                            @if ($user->role == 'admin')
                                <select wire:model.live="dealer"
                                    class="block w-full p-2 mt-2 text-sm border border-gray-200 rounded-md outline-none">
                                    <option selected>@lang('messages.dealer')</option>
                                    @foreach ($dealers ?? [] as $id => $dealer)
                                        <option value="{{ $id }}">{{ $dealer }}</option>
                                    @endforeach
                                </select>
                            @endif

                            <select wire:model.live="client"
                                class="block w-full p-2 mt-2 text-sm border border-gray-200 rounded-md outline-none">
                                <option selected>@lang('messages.client')</option>
                                @foreach ($clients ?? [] as $id => $client)
                                    <option value="{{ $id }}">{{ $client }}</option>
                                @endforeach
                            </select>


                        </div>
                    </div>

                </div>
            </div>
            {{-- action buttons --}}
            <div class="mt-10">
                <div class="flex items-center gap-4 ">
                    @if ($user->role == 'admin' || $user->role == 'technician' || $user->role == 'warehouse_operator')

                        @if ($selectedDevices && count($selectedDevices) > 0)
                            <div x-data="{ showTooltip: false }" class="relative z-50 flex-shrink-0">
                                <button @click="$dispatch('open-modal',{name:'assign-dealer'})"
                                    @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                                    class="p-2 text-sm text-white transition-all duration-300 rounded-lg ms-2 bg-primary hover:bg-primaryDark ">
                                    <svg class="size-5" viewBox="0 0 20 20" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M16.25 10.4767L10 16.6667L3.75003 10.4767C3.33778 10.0755 3.01306 9.59334 2.79632 9.06052C2.57957 8.52771 2.4755 7.95579 2.49064 7.38077C2.50579 6.80576 2.63983 6.24011 2.88432 5.71944C3.12882 5.19878 3.47847 4.73437 3.91127 4.35548C4.34406 3.97658 4.85061 3.6914 5.39904 3.5179C5.94746 3.34439 6.52587 3.28631 7.09783 3.34733C7.6698 3.40834 8.22294 3.58713 8.72242 3.87242C9.2219 4.15771 9.6569 4.54333 10 5.00499C10.3446 4.54668 10.7801 4.16443 11.2793 3.88217C11.7784 3.5999 12.3304 3.4237 12.9008 3.36459C13.4712 3.30549 14.0476 3.36474 14.594 3.53865C15.1404 3.71257 15.645 3.99739 16.0763 4.3753C16.5076 4.75321 16.8562 5.21606 17.1004 5.7349C17.3445 6.25374 17.479 6.8174 17.4953 7.39058C17.5116 7.96377 17.4094 8.53416 17.1951 9.06604C16.9809 9.59792 16.6591 10.0798 16.25 10.4817"
                                            stroke="currentColor" stroke-width="1.66667" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path
                                            d="M10 5L7.25583 7.74417C7.0996 7.90044 7.01184 8.11236 7.01184 8.33333C7.01184 8.5543 7.0996 8.76623 7.25583 8.9225L7.70833 9.375C8.28333 9.95 9.21666 9.95 9.79166 9.375L10.625 8.54167C10.8711 8.29524 11.1634 8.09975 11.4851 7.96637C11.8069 7.83299 12.1517 7.76433 12.5 7.76433C12.8483 7.76433 13.1931 7.83299 13.5149 7.96637C13.8366 8.09975 14.1289 8.29524 14.375 8.54167L16.25 10.4167M10.4167 12.9167L12.0833 14.5833M12.5 10.8333L14.1667 12.5"
                                            stroke="currentColor" stroke-width="1.66667" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>
                                </button>

                                <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                    x-transition:enter-start="opacity-0 transform scale-95"
                                    x-transition:enter-end="opacity-100 transform scale-100"
                                    x-transition:leave="transition ease-in duration-150"
                                    x-transition:leave-start="opacity-100 transform scale-100"
                                    x-transition:leave-end="opacity-0 transform scale-95"
                                    class="absolute left-0 z-50 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                    style="display: none;">
                                    @lang('messages.assign_a_dealer')
                                </div>
                            </div>
                            @if ($user->role == 'admin' || $user->role == 'technician' || $user->role == 'warehouse_operator')
                                <div x-data="{ showTooltip: false }" class="relative z-50 flex-shrink-0">
                                    <button wire:click="openReturnDDTModal" @mouseenter="showTooltip = true"
                                        @mouseleave="showTooltip = false"
                                        class="p-2 text-sm transition-all duration-300 border rounded-lg ms-2 border-primary text-primary hover:text-white hover:bg-primaryDark ">
                                        <svg class="size-5" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"
                                            fill="#000000">
                                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round"
                                                stroke-linejoin="round">
                                            </g>
                                            <g id="SVGRepo_iconCarrier">
                                                <defs>
                                                    <style>
                                                        .cls-1 {
                                                            fill: currentColor;
                                                        }

                                                        .cls-2 {
                                                            fill: currentColor;
                                                        }
                                                    </style>
                                                </defs>
                                                <g data-name="9. Return" id="_9._Return">
                                                    <path class="cls-1"
                                                        d="M14,10h4a0,0,0,0,1,0,0v3a1,1,0,0,1-1,1H15a1,1,0,0,1-1-1V10A0,0,0,0,1,14,10Z">
                                                    </path>
                                                    <path class="cls-2"
                                                        d="M20,23H12a3,3,0,0,1-3-3V12a3,3,0,0,1,3-3h8a3,3,0,0,1,3,3v8A3,3,0,0,1,20,23ZM12,11a1,1,0,0,0-1,1v8a1,1,0,0,0,1,1h8a1,1,0,0,0,1-1V12a1,1,0,0,0-1-1Z">
                                                    </path>
                                                    <path class="cls-2" d="M15,19H14a1,1,0,0,1,0-2h1a1,1,0,0,1,0,2Z">
                                                    </path>
                                                    <path class="cls-2"
                                                        d="M30,24.24l-1,4a1,1,0,0,1-.7.72A.84.84,0,0,1,28,29a1,1,0,0,1-.71-.29L26.57,28a15.53,15.53,0,0,1-2.68,1.93l-.51.27A15.85,15.85,0,0,1,16,32,16,16,0,0,1,0,16a15.82,15.82,0,0,1,.44-3.71,1,1,0,0,1,1.94.46A14.16,14.16,0,0,0,2,16,14,14,0,0,0,22.91,28.18l.09-.06a13.31,13.31,0,0,0,2.16-1.54l-.87-.87a1,1,0,0,1-.25-1,1,1,0,0,1,.72-.7l4-1A1,1,0,0,1,30,24.24Z">
                                                    </path>
                                                    <path class="cls-2"
                                                        d="M32,16a15.82,15.82,0,0,1-.44,3.71,1,1,0,0,1-1,.77.85.85,0,0,1-.23,0,1,1,0,0,1-.74-1.2A14.16,14.16,0,0,0,30,16,14,14,0,0,0,9.09,3.82,13.42,13.42,0,0,0,6.84,5.43l.87.86a1,1,0,0,1,.25,1,1,1,0,0,1-.72.7l-4,1L3,9a1,1,0,0,1-.71-.29,1,1,0,0,1-.26-1l1-4A1,1,0,0,1,3.73,3a1,1,0,0,1,1,.25L5.42,4A16.16,16.16,0,0,1,8.11,2.08,16,16,0,0,1,32,16Z">
                                                    </path>
                                                </g>
                                            </g>
                                        </svg>
                                    </button>
                                    <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 transform scale-95"
                                        x-transition:enter-end="opacity-100 transform scale-100"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 transform scale-100"
                                        x-transition:leave-end="opacity-0 transform scale-95"
                                        class="absolute left-0 z-50 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                        style="display: none;">
                                        @lang('messages.return_devices_request')
                                    </div>
                                </div>
                            @endif
                        @endif
                    @endif
                </div>
                @if ($selectedDevices && count($selectedDevices) > 0)
                    <div class="mt-2 text-sm font-medium text-primary">
                        {{ count($selectedDevices) }} @lang('messages.devices_selected')
                    </div>
                @endif
            </div>


            <div class="w-full mt-3 mb-5 overflow-x-auto">
                <table class="w-full mt-3 ">
                    <thead>
                        <tr
                            class="text-xs font-semibold tracking-wide text-left text-gray-900 uppercase border-b border-gray-600 shadow">
                            @if ($user->role == 'admin' || $user->role == 'technician' || $user->role == 'warehouse_operator')
                                <th class="px-4 py-3 border whitespace-nowrap">
                                    <input type="checkbox" wire:model.live="selectAllDevices"
                                        class="border-2 rounded shrink-0 border-primary text-primary focus:ring-primary disabled:opacity-50 disabled:pointer-events-none accent-primary">
                                </th>
                            @endif
                            <th class="px-4 py-3 border text-start whitespace-nowrap">@lang('messages.device')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.number_plate')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.dealer')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.client')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.type')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.test')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">{{ __('messages.verified') }}
                            </th>
                            @if ($user->role != 'dealer')
                                <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.working_heading')</th>
                            @endif
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.maintenance')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.created_at')</th>
                            <th
                                class="w-fit px-4 py-3 border whitespace-nowrap text-end @if ($user->role == 'client') min-w-32 text-start
                                @elseif ($user->role == 'dealer')
                                min-w-40
                                @else
min-w-56 @endif">
                                @lang('messages.actions')</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white">
                        @forelse ($devices as $device)
                            <tr class="text-gray-700 border-x" wire:key="{{ $device->id }}">
                                @if ($user->role == 'admin' || $user->role == 'technician' || $user->role == 'warehouse_operator')
                                    <td class="px-4 py-3">
                                        <input type="checkbox" value="{{ $device->id }}"
                                            wire:model.live="selectedDevices"
                                            class="border-2 rounded shrink-0 border-primary text-primary focus:ring-primary disabled:opacity-50 disabled:pointer-events-none accent-primary"
                                            {{ in_array($device->id, $selectedDevices ?? []) ? 'checked' : '' }}>
                                    </td>
                                @endif

                                <td class="px-4 py-3">
                                    <div class="relative">
                                        <p class="text-sm font-semibold text-black">{{ $device->imei ?? 'N/A' }}
                                        </p>
                                        <p class="text-xs text-gray-600">{{ $device->model ?? 'N/A' }}
                                        </p>

                                        {{-- Under maintenance Tootip if device is in maintenance --}}
                                        @if ($device->in_maintenance == true)
                                            <div x-data="{ showTooltip: false }"
                                                class="absolute flex-shrink-0 -top-3 -right-3">
                                                <button @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="size-4"
                                                        src="{{ asset('assets/images/maintenence.svg') }}"
                                                        alt="check">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    @lang('messages.in_maintenance')
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm font-medium">{{ $device->number_plate ?? 'N/A' }}
                                </td>

                                <td class="px-4 py-3 text-sm font-medium">{{ $device->dealer->user->name ?? 'N/A' }}
                                    {{ $device->dealer->last_name ?? '' }}
                                </td>

                                <td class="px-4 py-3 text-sm font-medium">{{ $device->client?->user?->name ?? 'N/A' }}
                                    {{ $device->client?->last_name ?? '' }}
                                </td>

                                <td class="px-4 py-3 text-sm font-medium">
                                    @if ($device->vehicle_type)
                                        @lang('messages.' . $device->vehicle_type)
                                    @endif
                                </td>

                                <td class="px-4 py-3 text-sm">
                                    @if ($device->is_tested == true)
                                        <img class="size-5" src="{{ asset('assets/images/check.svg') }}"
                                            alt="check">
                                    @else
                                        <img class="size-5" src="{{ asset('assets/images/cancel.svg') }}"
                                            alt="cross">
                                    @endif
                                </td>
                                <td class="px-4 py-3 text-xs">
                                    @if ($device->is_verified == true)
                                        <img class="size-5" src="{{ asset('assets/images/check.svg') }}"
                                            alt="check">
                                    @else
                                        <img class="size-5" src="{{ asset('assets/images/cancel.svg') }}"
                                            alt="cross">
                                    @endif
                                </td>
                                @if ($user->role != 'dealer')
                                    <td class="px-4 py-3 text-xs">
                                        @if ($device->is_active == true)
                                            <img class="size-5" src="{{ asset('assets/images/check.svg') }}"
                                                alt="check">
                                        @else
                                            <img class="size-5" src="{{ asset('assets/images/cancel.svg') }}"
                                                alt="cross">
                                        @endif
                                    </td>
                                @endif
                                <td class="px-4 py-3 text-xs">
                                    @if ($device->in_maintenance == true)
                                        <img class="size-5" src="{{ asset('assets/images/check.svg') }}"
                                            alt="check">
                                    @else
                                        <img class="size-5" src="{{ asset('assets/images/cancel.svg') }}"
                                            alt="cross">
                                    @endif
                                </td>

                                <td class="px-4 py-3 text-sm">
                                    {{ $device->created_at->format('d/m/Y') }}
                                </td>
                                <td class="px-4 py-3 text-sm whitespace-nowrap">
                                    <div
                                        class="flex items-center justify-end flex-shrink-0 w-full gap-2 whitespace-nowrap flex-nowrap">


                                        @if ($user->role != 'dealer')
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <a href="{{ route('dashboard', ['imei' => $device->imei ?? null]) }}"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="-mt-1 size-5"
                                                        src="{{ asset('assets/images/map-icon.svg') }}"
                                                        alt="test">
                                                </a>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    @lang('messages.show_on_map')
                                                </div>
                                            </div>
                                        @endif


                                        @if ($user->role == 'admin' || $user->role == 'operator' || $user->role == 'technician' || $user->role == 'warehouse_operator')
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <a href="{{ route('device-command', $device->imei ?? null) }}"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="-mt-1 size-4"
                                                        src="{{ asset('assets/images/command.svg') }}"
                                                        alt="test">
                                                </a>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    @lang('messages.send_command')
                                                </div>
                                            </div>


                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <a href="{{ route('device_history', $device->imei ?? null) }}"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="-mt-1 size-4"
                                                        src="{{ asset('assets/images/device-history.svg') }}"
                                                        alt="test">
                                                </a>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    @lang('messages.device_history')

                                                </div>
                                            </div>

                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="openDeviceTestModal({{ $device->id }})"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="size-5" src="{{ asset('assets/images/test.svg') }}"
                                                        alt="test">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    @lang('messages.test_device')

                                                </div>
                                            </div>
                                        @endif

                                        @if ($user->role == 'dealer' && $device->is_verified == 0 && empty($device->latestPendingContract))
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="openDeviceTestModal({{ $device->id }})"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="size-5"
                                                        src="{{ asset('assets/images/device-verify.svg') }}"
                                                        alt="test">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    @lang('messages.perform_testing')
                                                </div>
                                            </div>
                                        @endif


                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="showDeviceTestResults({{ $device->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-4"
                                                    src="{{ asset('assets/images/device-test.svg') }}"
                                                    alt="test result">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">

                                                @lang('messages.test_device_results')
                                            </div>
                                        </div>

                                        @if ($user->role == 'admin' || $user->role == 'client')
                                            <!-- Edit Button with Tooltip -->
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="editRecord({{ $device->id }})"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="size-5" src="{{ asset('assets/images/edit.svg') }}"
                                                        alt="Edit">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    {{ __('messages.edit') }}
                                                </div>
                                            </div>

                                            @if ($user->role == 'admin')
                                                <!-- Delete Button with Tooltip -->
                                                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                    <button wire:click="deleteRecordConfirmation({{ $device->id }})"
                                                        @mouseenter="showTooltip = true"
                                                        @mouseleave="showTooltip = false">
                                                        <img class="size-5"
                                                            src="{{ asset('assets/images/delete.svg') }}"
                                                            alt="Delete">
                                                    </button>
                                                    <div x-show="showTooltip"
                                                        x-transition:enter="transition ease-out duration-200"
                                                        x-transition:enter-start="opacity-0 transform scale-95"
                                                        x-transition:enter-end="opacity-100 transform scale-100"
                                                        x-transition:leave="transition ease-in duration-150"
                                                        x-transition:leave-start="opacity-100 transform scale-100"
                                                        x-transition:leave-end="opacity-0 transform scale-95"
                                                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                        style="display: none;">
                                                        {{ __('messages.delete') }}
                                                    </div>
                                                </div>
                                            @endif
                                        @endif
                                    </div>

                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="px-4 py-3 text-center">{{ __('messages.no_record_found') }}
                                </td>
                            </tr>
                        @endforelse

                    </tbody>
                </table>
            </div>

            {{ $devices->links('livewire.custom-pagination') }}

        </div>
    </section>

    {{-- modals --}}

    {{-- add device modal --}}
    <x-modal name="add-device-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black">
                        @if (!$recordId)
                            {{ __('messages.add_device') }}
                        @else
                            {{ __('messages.edit_device') }}
                        @endif

                    </h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">IMEI</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model.blur="imei" id="imei"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="IMEI" disabled>
                            </div>

                            @error('imei')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]"> @lang('messages.model')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model.blur="model" id="model"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.model')" disabled>
                            </div>

                            @error('model')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        @if ($user->role == 'admin')
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">ICCID</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model.blur="iccid" id="iccid"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                        placeholder="ICCID" disabled>
                                </div>

                                @error('iccid')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">IMSI</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model.blur="imsi" id="imsi"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                        placeholder="IMSI" disabled>
                                </div>

                                @error('imsi')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>
                        @endif

                        <div class="col-span-2 md:col-span-1">
                            <label for="vehicleType"
                                class="text-sm text-[#414651]">{{ __('messages.select_vehicle_type') }}</label>
                            <div class="relative mt-2">

                                <select id="vehicleType" wire:model.blur="vehicle_type"
                                    class="peer py-2.5 px-4 ps-11 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300" @if ($user->role != 'admin' || $user->role != 'dealer')
                                        disabled
                                    @endif>
                                    <option value="">@lang('messages.select')</option>
                                    <option value="car">@lang('messages.car')</option>
                                    <option value="bus">@lang('messages.bus')</option>
                                    <option value="boat">@lang('messages.boat')</option>
                                    <option value="motorcycle">@lang('messages.motorcycle')</option>
                                    <option value="scooter">@lang('messages.scooter')</option>
                                    <option value="truck">@lang('messages.truck')</option>
                                    <option value="van">@lang('messages.van')</option>
                                    <option value="tractor">@lang('messages.tractor')</option>
                                </select>

                                <div
                                    class="absolute inset-y-0 flex items-center pointer-events-none start-0 ps-4 peer-disabled:opacity-50 peer-disabled:pointer-events-none">
                                    <img class="shrink-0 size-4" src="{{ asset('assets/images/truck.svg') }}"
                                        alt="device">
                                </div>

                            </div>
                        </div>
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.dealer')</label>

                            <div class="relative mt-2">
                                <select disabled id="select_dealer" wire:model="dealer_id"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 bg-gray-100">
                                    <option value="">@lang('messages.select')</option>
                                    @forelse ($dealers ?? [] as $id => $dealer)
                                        <option value="{{ $id }}">{{ $dealer }}</option>

                                    @empty
                                        <option value="">@lang('messages.no_record_found')</option>
                                    @endforelse
                                </select>

                            </div>

                            @error('dealer')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>
                        @if ($user->role == 'admin')
                            <div class="col-span-2 md:col-span-1">
                                <label for="is_verified"
                                    class="text-sm text-[#414651]">{{ __('messages.verified') }}</label>
                                <div class="relative mt-2">

                                    <select id="is_verified" wire:model="is_verified"
                                        class="peer py-2.5 px-4  block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                                        <option value="0">{{ __('messages.not_verified') }}</option>
                                        <option value="1">{{ __('messages.verified') }}</option>
                                    </select>

                                </div>
                            </div>
                            <div class="col-span-2 md:col-span-1">
                                <label for="is_tested"
                                    class="text-sm text-[#414651]">{{ __('messages.tested') }}</label>
                                <div class="relative mt-2">

                                    <select id="is_tested" wire:model="is_tested"
                                        class="peer py-2.5 px-4  block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                                        <option value="1">{{ __('messages.tested') }}</option>
                                        <option value="0">{{ __('messages.not_tested') }}</option>
                                    </select>

                                </div>
                            </div>
                        @endif

                        <!-- Dealers Dropdown -->
                        {{-- <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.dealer') }}</label>

                            <x-dropdown :options="$dealers" name="dealer_id" />


                            @error('dealer_id')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div> --}}

                        <!-- Clients Dropdown -->
                        {{-- <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.client') }}</label>

                            <x-dropdown wire:click="loadClients" :options="$clients" name="client_id" />


                            @error('client_id')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div> --}}



                        <div class="flex flex-col col-span-2 gap-5">
                            @if ($user->role == 'admin')
                                <div class="flex items-center">
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" checked="" wire:model="is_active" id="active"
                                            class="sr-only peer">
                                        <div
                                            class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary">
                                        </div>
                                    </label>

                                    <label for="active"
                                        class="ms-2 cursor-pointer select-none text-sm text-[#414651]">
                                        @lang('messages.working_heading')
                                    </label>
                                </div>
                            @endif

                            <div class="flex items-center">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked="" wire:model="in_maintenance"
                                        id="in_maintenance" class="sr-only peer">
                                    <div
                                        class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary">
                                    </div>
                                </label>

                                <label for="in_maintenance"
                                    class="ms-2 cursor-pointer select-none text-sm text-[#414651]">
                                    @lang('messages.in_maintenance')
                                </label>
                            </div>
                        </div>

                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false;"
                            class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>

                        <button wire:click="addUpdateDevice" wire:loading.attr="disabled"
                            wire:target="addUpdateDevice" type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            <span wire:loading.remove wire:target="addUpdateDevice"> {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="addUpdateDevice">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

    {{-- assign dealer modal --}}
    <x-modal name="assign-dealer">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black">@lang('messages.assign_devices')</h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">

                        <div class="col-span-2">
                            <label class="text-sm text-[#414651]">{{ __('messages.dealer') }}</label>

                            <x-dropdown :options="$dealers" name="selected_dealer" />


                            @error('selected_dealer')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>


                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.incharge_of_transport') }} <span
                                    class="text-xs text-gray-500">({{ __('messages.optional') }})</span></label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="incharge_of_transport" id="incharge_of_transport"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                            </div>

                            @error('incharge_of_transport')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.transport_reason') }} <span
                                    class="text-xs text-gray-500">({{ __('messages.optional') }})</span></label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="transport_reason" id="transport_reason"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                            </div>

                            @error('transport_reason')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.delivery_terms') }} <span
                                    class="text-xs text-gray-500">({{ __('messages.optional') }})</span></label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="delivery_terms" id="delivery_terms"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                            </div>

                            @error('delivery_terms')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.parcels') }} <span
                                    class="text-xs text-gray-500">({{ __('messages.optional') }})</span></label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="parcels" id="parcels"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                            </div>

                            @error('parcels')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>




                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.weight') }} <span
                                    class="text-xs text-gray-500">({{ __('messages.optional') }})</span></label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="weight" id="weight"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                            </div>

                            @error('weight')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>


                        <!-- File Input for Transport Officer Signature -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.transport_officer_signature') }}
                                <span class="text-xs text-gray-500">({{ __('messages.optional') }})</span>
                            </label>

                            <div class="relative mt-2">
                                <input type="file" wire:model="transportOfficerSignature"
                                    id="transportOfficerSignature"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                            </div>

                            @error('transportOfficerSignature')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- File Input for Recipient Signature -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.recipient_signature') }}
                                <span class="text-xs text-gray-500">({{ __('messages.optional') }})</span>
                            </label>

                            <div class="relative mt-2">
                                <input type="file" wire:model="recipientSignature" id="recipientSignature"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                            </div>

                            @error('recipientSignature')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false;"
                            class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>

                        <button wire:click="assignDealerToSelected" wire:loading.attr="disabled"
                            wire:target="assignDealerToSelected" type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            <span wire:loading.remove wire:target="assignDealerToSelected">
                                {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="assignDealerToSelected">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

    {{-- assign dealer modal --}}
    <x-modal name="return-dealer-devices">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black">@lang('messages.return_ddt')</h2>
                </div>


                <div class="mt-4">
                    @if ($this->returnDevices)
                        <h3 class="mb-1 font-medium underline">@lang('messages.devices')</h3>
                        @foreach ($this->returnDevices as $dealerId => $devices)
                            @php
                                // Get the dealer from the first device in the group
                                $dealer = $devices->first()->dealer;
                            @endphp

                            <h3>@lang('messages.dealer'): {{ $dealer->user->name ?? 'Unknown Dealer' }}</h3>
                            <!-- Show dealer name -->

                            <ul>
                                @foreach ($devices as $device)
                                    <li>
                                        <strong>IMEI:</strong> {{ $device->imei }}
                                        <strong>@lang('messages.client'):</strong>
                                        {{ $device->client ? $device->client->user->name : __('messages.no_associated_client') }}
                                    </li>
                                @endforeach
                            </ul>
                        @endforeach
                    @endif





                    <div class="grid grid-cols-2 gap-4 mt-4">




                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.incharge_of_transport') }} <span
                                    class="text-xs text-gray-500">({{ __('messages.optional') }})</span></label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="incharge_of_transport" id="incharge_of_transport"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                            </div>

                            @error('incharge_of_transport')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.transport_reason') }} <span
                                    class="text-xs text-gray-500">({{ __('messages.optional') }})</span></label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="transport_reason" id="transport_reason"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                            </div>

                            @error('transport_reason')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>




                        <!-- File Input for Transport Officer Signature -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.transport_officer_signature') }}
                                <span class="text-xs text-gray-500">({{ __('messages.optional') }})</span>
                            </label>

                            <div class="relative mt-2">
                                <input type="file" wire:model="transportOfficerSignature"
                                    id="transportOfficerSignature"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                            </div>

                            @error('transportOfficerSignature')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- File Input for Recipient Signature -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.recipient_signature') }}
                                <span class="text-xs text-gray-500">({{ __('messages.optional') }})</span>
                            </label>

                            <div class="relative mt-2">
                                <input type="file" wire:model="recipientSignature" id="recipientSignature"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300">
                            </div>

                            @error('recipientSignature')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2">
                            <label class="text-sm text-[#414651]">{{ __('messages.return_reason') }} <span
                                    class="text-xs text-gray-500">({{ __('messages.optional') }})</span></label>

                            <div class="relative mt-2">
                                <textarea wire:model="return_reason" id="return_reason"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    rows="4"></textarea>
                            </div>

                            @error('return_reason')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>


                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false;"
                            class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>

                        <button wire:click="submitReturnDeviceDDT" wire:loading.attr="disabled"
                            wire:target="submitReturnDeviceDDT" type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            <span wire:loading.remove wire:target="submitReturnDeviceDDT">
                                {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="submitReturnDeviceDDT">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

    {{-- delete device modal --}}
    <x-modal name="delete-record-modal">
        <x-slot:body>

            <div class="flex items-center justify-between">
                <img class="size-10" src="{{ asset('assets/images/logout-icon.svg') }}" alt="logout">

                <button @click="show = false" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>

            </div>

            <div class="mt-4">
                <h3 class="font-medium">
                    {{ __('messages.delete_confirmation') }}
                </h3>

                <p class="text-sm mt-2 text-[#535862]">
                    {{ __('messages.delete_warning') }}
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                        {{ __('messages.cancel') }}
                    </button>
                    <button wire:click="deleteRecord"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        {{ __('messages.delete') }}
                    </button>
                </div>
            </div>


        </x-slot:body>
    </x-modal>


    {{-- test device modal --}}
    <x-modal name="test-device-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black"> @lang('messages.device_details')</h2>
                </div>


                <div class="mt-4">
                    <div class="space-y-4 text-gray-800">
                        <div class="flex">
                            <img class="mt-1 size-5" src="{{ asset('assets/images/device.svg') }}" alt="device">
                            <div class="ms-2">
                                <h3 class="font-medium">
                                    <span class="text-gray-800">{{ $deviceTestData->imei ?? 'N/A' }}</span>

                                </h3>

                                <p class="text-sm text-[#6A6A6A]">
                                    <span class="text-gray-800">{{ $deviceTestData->model ?? 'N/A' }}</span>
                                </p>
                            </div>
                        </div>



                        <!-- Dealer -->
                        <p class="flex items-center text-sm">
                            <strong class="text-gray-800 w-36">{{ __('messages.dealer') }}:</strong>
                            <span class="text-gray-800">{{ $deviceTestData->dealer->user->name ?? 'N/A' }}</span>
                        </p>

                        <!-- Client -->
                        <p class="flex items-center text-sm">
                            <strong class="text-gray-800 w-36">{{ __('messages.client') }}:</strong>
                            <span class="text-gray-800">{{ $deviceTestData->client->user->name ?? 'N/A' }}
                                {{ $deviceTestData->client->last_name ?? '' }}</span>
                        </p>


                        <p class="flex items-center text-sm">
                            <strong class="text-gray-800 w-36">{{ __('messages.vehicle_type') }}:</strong>
                            <span class="text-gray-800">{{ ucfirst($deviceTestData->vehicle_type ?? 'N/A') }}</span>
                        </p>



                        <div class="flex mt-5">
                            <img class="flex-shrink-0 size-5" src="{{ asset('assets/images/info-icon.svg') }}"
                                alt="map">

                            <p class="ms-2 text-sm text-[#8E8E8E] underline">
                                @lang('messages.device_test_instructions')
                            </p>
                        </div>

                    </div>
                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false"
                            class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>
                        <button wire:click="testDevice"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            @lang('messages.test_device')
                        </button>
                    </div>
                </div>


            </div>
        </x-slot:body>
    </x-modal>

    @php
        // Convert GPS status code to a readable format
        function getGPSStatus($status)
        {
            switch ($status) {
                case 0:
                    return __('messages.off');
                case 1:
                    return __('messages.good');
                case 2:
                    return __('messages.weak');
                case 3:
                    return __('messages.inactive');
                default:
                    return __('messages.unknown');
            }
        }

        // Convert movement status code to a readable format
        function getMovementStatus($status)
        {
            return $status === 1 ? __('messages.moving') : __('messages.stopped');
        }

        // Convert movement status code to a readable format
        function getEngineStatus($ignitionStatus, $movement = 0, $speed = 0)
        {
            if ($ignitionStatus == 1 && $speed > 0 && $movement == 1) {
                return __('messages.on');
            } else {
                return __('messages.off');
            }
        }
        function getIgnitionStatus($ignitionStatus)
        {
            if ($ignitionStatus == 1) {
                return __('messages.on');
            } else {
                return __('messages.off');
            }
        }

        // Convert GNSS status code to a readable format
        function getGNSSStatus($status)
        {
            switch ($status) {
                case 0:
                    return 'GNSS OFF';
                case 1:
                    return 'GNSS ON with fix';
                case 2:
                    return 'GNSS ON without fix';
                case 3:
                    return 'GNSS sleep';
                default:
                    return 'Unknown';
            }
        }

    @endphp

    {{-- test result modal for collaudo --}}
    <x-modal name="test-result-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <h3 class="text-lg font-medium"> @lang('messages.test_device_result')
                </h3>

                <div class="mt-4">
                    <div class="space-y-4 text-gray-800">
                        <div class="flex">
                            <img class="mt-1 size-5" src="{{ asset('assets/images/device.svg') }}" alt="device">
                            <div class="ms-2">
                                <h3 class="font-medium">
                                    <span class="text-gray-800">{{ $deviceTestData->imei ?? 'N/A' }}</span>
                                </h3>

                                <p class="text-sm text-[#6A6A6A]">
                                    <span class="text-gray-800">{{ $deviceTestData->model ?? 'N/A' }}</span>
                                </p>
                            </div>
                        </div>

                        <div class="my-5 {{ $testResult ? 'text-teal-500' : 'text-rose-600' }} font-medium text-sm">

                            <p class="flex items-center gap-3">
                                @if (!$testResult)
                                    <img src="{{ asset('assets/images/cancel.svg') }}" alt="test">
                                    @lang('messages.device_data_not_found')
                                @endif
                            </p>
                            @if (isset($testResult['66']) && $testResult['66'] == 0 && isset($testResult['239']) && $testResult['239'] == 0)
                                <p class="flex items-center gap-3 mt-2 text-rose-600">
                                    <img src="{{ asset('assets/images/cancel.svg') }}" alt="test">
                                    @lang('messages.installation_error')
                                </p>
                            @else
                                {{-- @if (isset($testResult['66']) && $testResult['66'] == 0)
                                    <p class="flex items-center gap-3 mt-2 text-rose-600">
                                        <img src="{{ asset('assets/images/cancel.svg') }}" alt="test">
                                        @lang('messages.power_supply')
                                    </p>
                                @endif

                                @if (isset($testResult['239']) && $testResult['239'] == 0)
                                    <p class="flex items-center gap-3 mt-2 text-rose-600">
                                        <img src="{{ asset('assets/images/cancel.svg') }}" alt="test">
                                        @lang('messages.ignition_off')
                                    </p>
                                @endif --}}

                                {{-- @if ((isset($testResult['66']) && $testResult['66'] == 0) || (isset($testResult['239']) && $testResult['239'] == 0))
                                @else --}}
                                <p class="flex items-center gap-3">
                                    <img src="{{ asset('assets/images/check.svg') }}" alt="test">
                                    @lang('messages.gps_tested')
                                </p>
                                {{-- @endif --}}
                            @endif

                        </div>

                        @if ($testResult != null)
                            <p class="flex items-center text-sm">
                                <strong class="w-40 text-gray-800">@lang('messages.gps_signal')</strong>
                                <span
                                    class="text-gray-800">{{ isset($testResult['69']) ? getGPSStatus($testResult['69']) : 'N/A' }}
                                </span>
                                @php
                                    $signalValue = $testResult['21'] ?? 0;
                                @endphp
                                <span style="transform: rotateX(180deg); scale: 0.6;" id="signalStrength"
                                    class="flex ml-2 w-fit">
                                    @for ($i = 1; $i <= 5; $i++)
                                        <span
                                            style="width: 6px; margin: 0 1px; background-color: {{ $i <= $signalValue ? 'green' : 'lightgray' }}; height: {{ $i * 5 }}px;">
                                        </span>
                                    @endfor
                                </span>

                            </p>

                            <p class="flex items-center text-sm">
                                <strong class="w-40 text-gray-800">@lang('messages.movement')</strong>
                                <span
                                    class="text-gray-800">{{ isset($testResult['240']) ? getMovementStatus($testResult['240']) : 'N/A' }}</span>
                            </p>
                            <p class="flex items-center text-sm">
                                <strong class="w-40 text-gray-800">@lang('messages.engine_status')</strong>
                                <span
                                    class="text-gray-800">{{ isset($testResult['239']) ? getEngineStatus($testResult['239'], $testResult['240'], $testResult['speed']) : 'N/A' }}</span>
                            </p>
                            <p class="flex items-center text-sm">
                                <strong class="w-40 text-gray-800">@lang('messages.ignition_status')</strong>
                                <span
                                    class="text-gray-800">{{ isset($testResult['239']) ? getIgnitionStatus($testResult['239']) : 'N/A' }}</span>
                            </p>
                            <p class="flex items-center text-sm">
                                <strong class="w-40 text-gray-800">@lang('messages.battery_voltage')</strong>
                                <span
                                    class="text-gray-800">{{ isset($testResult['66']) ? round($testResult['66'], 2) . 'V' : 'N/A' }}</span>
                            </p>

                            <p class="flex items-center text-sm">
                                <strong class="w-40 text-gray-800">@lang('messages.battery_level')</strong>
                                <span
                                    class="text-gray-800">{{ isset($testResult['113']) ? $testResult['113'] . '%' : 'N/A' }}</span>
                            </p>

                            <p class="flex items-center text-sm">
                                <strong class="w-40 text-gray-800">@lang('messages.location')</strong>
                                <span class="text-gray-800">{{ $testResult['address'] ?? 'N/A' }}</span>
                            </p>
                            <p class="flex items-center text-sm">
                                <strong class="w-40 text-gray-800">Lat - Long:</strong>
                                <span class="text-gray-800">{{ $testResult['latitude'] ?? 'N/A' }} -
                                    {{ $testResult['longitude'] ?? 'N/A' }}</span>
                            </p>

                            <p class="flex items-center text-sm">
                                <strong class="w-40 text-gray-800">@lang('messages.speed')</strong>
                                <span
                                    class="text-gray-800">{{ isset($testResult['speed']) ? $testResult['speed'] . ' km/h' : 'N/A' }}</span>
                            </p>

                            <p class="flex items-center text-sm">
                                <strong class="w-40 text-gray-800">@lang('messages.last_update'):</strong>
                                <span class="text-gray-800">{{ $testResult['last_update'] ?? 'N/A' }}</span>
                            </p>

                            <div
                                class="flex flex-col gap-2 text-sm font-semibold md:flex-row md:flex-col text-primary">
                                <div class="flex items-center gap-1">
                                    <img class="size-4" src="{{ asset('assets/images/engine.svg') }}"
                                        alt="running">
                                    @lang('messages.test_motor_block')
                                </div>

                                <div class="flex items-center gap-2">
                                    <button wire:click="ignitionOn('{{ $deviceTestData->imei }}')"
                                        class="flex items-center justify-center text-white transition-all duration-300 rounded-full bg-emerald-500 size-8 hover:bg-emerald-600 focus:ring focus:ring-emerald-600 ring-offset-1">
                                        <img class="size-4" src="{{ asset('assets/images/play-white.svg') }}"
                                            alt="play">
                                    </button>
                                    <button wire:click="ignitionOff('{{ $deviceTestData->imei }}')"
                                        class="flex items-center justify-center text-white transition-all duration-300 rounded-full bg-rose-500 size-8 hover:bg-rose-600 focus:ring focus:ring-rose-600 ring-offset-1">
                                        <img class="size-4" src="{{ asset('assets/images/pause.svg') }}"
                                            alt="play">
                                    </button>
                                </div>

                                <div>
                                    @if ($commandTestedId)
                                        <button wire:click="loadCommandReult"
                                            class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm mt-2">

                                            <span wire:loading.remove>
                                                @lang('messages.load_result')
                                            </span>

                                            <span wire:loading>
                                                @lang('messages.loading')...
                                            </span>
                                        </button>
                                    @endif

                                    @if ($ignitionCommandResponse)
                                        @if ($ignitionCommandResponse->response)
                                            <div class="mt-2 font-normal">
                                                <p class="text-sm text-[#6A6A6A] mt-1">
                                                    <strong>@lang('messages.response'):</strong>
                                                    <span
                                                        class="font-medium text-gray-800">{{ $ignitionCommandResponse->response }}</span>
                                                </p>
                                                <p class="text-sm text-[#6A6A6A] mt-1">
                                                    <strong>@lang('messages.response_received_at'):</strong>
                                                    <span
                                                        class="font-medium text-gray-800">{{ \Carbon\Carbon::parse($ignitionCommandResponse->response_received_at)->format('d/m/Y H:i') }}</span>
                                                </p>
                                            @else
                                                <p
                                                    class="mt-2 text-sm font-normal text-center text-yellow-600 underline">
                                                    @lang('messages.no_comamnd_response')</p>
                                        @endif
                                    @endif
                                </div>
                            </div>
                        @endif

                    </div>





                    <div
                        class="flex flex-wrap items-center justify-center flex-grow gap-5 mt-8 text-sm md:flex-nowrap">
                        <button wire:click="testDevice('notify')"
                            class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            @lang('messages.retry_test')
                        </button>
                        @if (isset($testResult['66']) && $testResult['66'] == 0 && (isset($testResult['239']) && $testResult['239'] == 0))
                        @else
                            <button wire:click="finishTest"
                                class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                                @if ($user->role == 'admin')
                                    @lang('messages.finish_test')
                                @else
                                    @lang('messages.perform_testing')
                                @endif
                            </button>
                        @endif
                    </div>


                </div>

            </div>
        </x-slot:body>
    </x-modal>


    {{-- test result modal --}}
    <x-modal name="device-test-results-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <h3 class="text-lg font-medium"> @lang('messages.test_device_results')
                </h3>

                @if ($deviceTestResults != null)
                    <div class="mt-4">
                        <div class="space-y-4 text-gray-800">
                            <div class="flex">
                                <img class="mt-1 size-5" src="{{ asset('assets/images/device.svg') }}"
                                    alt="device">
                                <div class="ms-2">
                                    <h3 class="font-medium">
                                        <span class="text-gray-800">{{ $deviceTestResults->imei ?? 'N/A' }}</span>
                                    </h3>

                                    <p class="text-sm text-[#6A6A6A]">
                                        <span class="text-gray-800">{{ $deviceTestResults->model ?? 'N/A' }}</span>
                                    </p>
                                </div>
                            </div>

                            @foreach ($deviceTestResults->testResults as $testResult)
                                <div class="w-full pb-5 mt-5 space-y-4 border-b border-b-gray-200">
                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.tested_at'):</strong>
                                        <span
                                            class="text-gray-800">{{ $testResult->created_at->format('d/m/Y H:i') }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.gps_signal'):</strong>
                                        <span
                                            class="text-gray-800">{{ isset($testResult->gps_status) ? getGPSStatus($testResult->gps_status) : 'N/A' }}</span>

                                        @php
                                            $signalValue = $testResult->signal ?? 0;
                                        @endphp
                                        <span style="transform: rotateX(180deg); scale: 0.6;" id="signalStrength"
                                            class="flex ml-2 w-fit">
                                            @for ($i = 1; $i <= 5; $i++)
                                                <span
                                                    style="width: 6px; margin: 0 1px; background-color: {{ $i <= $signalValue ? 'green' : 'lightgray' }}; height: {{ $i * 5 }}px;">
                                                </span>
                                            @endfor
                                        </span>

                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.movement')</strong>
                                        <span
                                            class="text-gray-800">{{ isset($testResult->movement_status) ? getMovementStatus($testResult->movement_status) : 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.ignition_status')</strong>
                                        <span
                                            class="text-gray-800">{{ $testResult->ignition_status ? __('messages.' . strtolower($testResult->ignition_status)) : 'N/A' }}</span>
                                    </p>
                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.engine_status')</strong>
                                        <span
                                            class="text-gray-800">{{ $testResult->engine_status ? __('messages.' . strtolower($testResult->engine_status)) : 'N/A' }}</span>
                                    </p>

                                    @if ($testResult->ignition_tested_at)
                                        <div class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.motor_block_test')</strong>
                                            <div class="flex items-center gap-2 text-gray-800">
                                                <img class="size-5" src="{{ asset('assets/images/check.svg') }}"
                                                    alt="check">

                                                <p class="font-medium text-emerald-600">
                                                    @lang('messages.ignition_tested')
                                                    {{ \Carbon\Carbon::parse($testResult->ignition_tested_at)->format('d-m-Y H:i') }}
                                                </p>

                                            </div>
                                        </div>
                                    @endif



                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.battery_voltage')</strong>
                                        <span
                                            class="text-gray-800">{{ $testResult->battery_voltage ? $testResult->battery_voltage . 'V' : 'N/A' }}</span>
                                    </p>


                                    <p class="flex items-center text-sm">
                                        <strong class="w-40 text-gray-800">@lang('messages.battery_level')</strong>
                                        <span
                                            class="text-gray-800">{{ isset($testResult->battery_level) ? $testResult->battery_level . '%' : 'N/A' }}</span>
                                    </p>


                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.location')</strong>
                                        <span class="text-gray-800">{{ $testResult->location ?? 'N/A' }}</span>
                                    </p>
                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">Lat - Long:</strong>
                                        <span class="text-gray-800">{{ $testResult->latitude ?? 'N/A' }} -
                                            {{ $testResult->longitude ?? 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.speed'):</strong>
                                        <span
                                            class="text-gray-800">{{ isset($testResult->speed) ? $testResult->speed . ' km/h' : 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.last_update'):</strong>
                                        <span class="text-gray-800">{{ $testResult->last_update ?? 'N/A' }}</span>
                                    </p>
                                </div>
                            @endforeach


                        </div>





                        <div
                            class="flex flex-wrap items-center justify-center flex-grow gap-5 mt-8 text-sm md:flex-nowrap">
                            <button @click="show = false"
                                class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                                @lang('messages.close')
                            </button>
                            {{-- <button
                                @click="$dispatch('close-modal');$dispatch('open-modal',{name: 'test-result-modal'})"
                                class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                                Test Device
                            </button> --}}
                        </div>


                    </div>
                @else
                    <p class="pt-4">@lang('messages.no_test_result')</p>
                @endif


            </div>
        </x-slot:body>
    </x-modal>

    {{-- test result modal --}}
    {{-- <x-modal name="device-test-results-for-verification-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <h3 class="text-lg font-medium"> @lang('messages.test_device_results')
                </h3>

                @if ($deviceTestResults != null)
                    <div class="mt-4">
                        <div class="space-y-4 text-gray-800">
                            <div class="flex">
                                <img class="mt-1 size-5" src="{{ asset('assets/images/device.svg') }}"
                                    alt="device">
                                <div class="ms-2">
                                    <h3 class="font-medium">
                                        <span class="text-gray-800">{{ $deviceTestResults->imei ?? 'N/A' }}</span>
                                    </h3>

                                    <p class="text-sm text-[#6A6A6A]">
                                        <span class="text-gray-800">{{ $deviceTestResults->model ?? 'N/A' }}</span>
                                    </p>
                                </div>
                            </div>

                            <div class="h-full overflow-auto max-h-80">
                                @foreach ($deviceTestResults->testResults as $testResult)
                                    <div class="w-full pb-5 mt-5 space-y-4 border-b border-b-gray-200">
                                        <p class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.tested_at'):</strong>
                                            <span
                                                class="text-gray-800">{{ $testResult->created_at->format('d/m/Y H:i') }}</span>
                                        </p>

                                        <p class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.gps_signal'):</strong>
                                            <span
                                                class="text-gray-800">{{ isset($testResult->gps_status) ? getGPSStatus($testResult->gps_status) : 'N/A' }}</span>

                                            @php
                                                $signalValue = $testResult->signal ?? 0;
                                            @endphp
                                            <span style="transform: rotateX(180deg); scale: 0.6;" id="signalStrength"
                                                class="flex ml-2 w-fit">
                                                @for ($i = 1; $i <= 5; $i++)
                                                    <span
                                                        style="width: 6px; margin: 0 1px; background-color: {{ $i <= $signalValue ? 'green' : 'lightgray' }}; height: {{ $i * 5 }}px;">
                                                    </span>
                                                @endfor
                                            </span>

                                        </p>

                                        <p class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.movement')</strong>
                                            <span
                                                class="text-gray-800">{{ isset($testResult->movement_status) ? getMovementStatus($testResult->movement_status) : 'N/A' }}</span>
                                        </p>

                                        <p class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.ignition_status')</strong>
                                            <span
                                                class="text-gray-800">{{ $testResult->ignition_status ? __('messages.' . strtolower($testResult->ignition_status)) : 'N/A' }}</span>
                                        </p>
                                        <p class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.engine_status')</strong>
                                            <span
                                                class="text-gray-800">{{ $testResult->engine_status ? __('messages.' . strtolower($testResult->engine_status)) : 'N/A' }}</span>
                                        </p>

                                        @if ($testResult->ignition_tested_at)
                                            <div class="flex flex-col gap-2 text-sm md:flex-row">
                                                <strong class="text-gray-800 w-36">@lang('messages.motor_block_test')</strong>
                                                <div class="flex items-center gap-2 text-gray-800">
                                                    <img class="size-5" src="{{ asset('assets/images/check.svg') }}"
                                                        alt="check">

                                                    <p class="font-medium text-emerald-600">
                                                        @lang('messages.ignition_tested')
                                                        {{ \Carbon\Carbon::parse($testResult->ignition_tested_at)->format('d-m-Y H:i') }}
                                                    </p>

                                                </div>
                                            </div>
                                        @endif



                                        <p class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.battery_voltage')</strong>
                                            <span
                                                class="text-gray-800">{{ $testResult->battery_voltage ? $testResult->battery_voltage . 'V' : 'N/A' }}</span>
                                        </p>


                                        <p class="flex items-center text-sm">
                                            <strong class="w-40 text-gray-800">@lang('messages.battery_level')</strong>
                                            <span
                                                class="text-gray-800">{{ isset($testResult->battery_level) ? $testResult->battery_level . '%' : 'N/A' }}</span>
                                        </p>

                                        <p class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.location')</strong>
                                            <span class="text-gray-800">{{ $testResult->location ?? 'N/A' }}</span>
                                        </p>
                                        <p class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">Lat - Long:</strong>
                                            <span class="text-gray-800">{{ $testResult->latitude ?? 'N/A' }} -
                                                {{ $testResult->longitude ?? 'N/A' }}</span>
                                        </p>

                                        <p class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.speed'):</strong>
                                            <span
                                                class="text-gray-800">{{ isset($testResult->speed) ? $testResult->speed . ' km/h' : 'N/A' }}</span>
                                        </p>

                                        <p class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.last_update'):</strong>
                                            <span
                                                class="text-gray-800">{{ $testResult->last_update ?? 'N/A' }}</span>
                                        </p>
                                    </div>
                                @endforeach
                            </div>



                        </div>





                        <div
                            class="flex flex-wrap items-center justify-center flex-grow gap-5 mt-8 text-sm md:flex-nowrap">
                            <button @click="show = false"
                                class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                                @lang('messages.close')
                            </button>
                            <button wire:click="openDeviceVerifyModal({{ $deviceTestResults->id }})"
                                class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                                @lang('messages.perform_testing')
                            </button>
                        </div>


                    </div>
                @else
                    <p class="pt-4">@lang('messages.no_test_result')</p>
                @endif


            </div>
        </x-slot:body>
    </x-modal> --}}

    {{-- verify device modal --}}
    <x-modal name="verify-device-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <h3 class="text-lg font-medium">@lang('messages.perform_testing')</h3>

                <div class="mt-4">
                    <div class="space-y-4 text-gray-800">
                        <div class="justify-between md:flex">
                            <div class="flex">
                                <img class="mt-1 size-5" src="{{ asset('assets/images/device.svg') }}"
                                    alt="device">
                                <div class="ms-2">
                                    <h3 class="font-medium">
                                        <span class="text-gray-800">{{ $deviceVerifyData->imei ?? 'N/A' }}</span>

                                    </h3>

                                    <p class="text-sm text-[#6A6A6A]">
                                        <span class="text-gray-800">{{ $deviceVerifyData->model ?? 'N/A' }}</span>
                                    </p>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button
                                    @click="$dispatch('close-modal');$dispatch('open-modal',{name:'add-client-modal'})"
                                    class="mt-4 flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-primaryDark ">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>@lang('messages.add_client')</span>
                                </button>
                            </div>
                        </div>

                        <div x-data="{ open: false }" class="pb-5 mt-5 space-y-4 border-b border-b-gray-200">
                            <!-- Accordion Toggle -->
                            <button @click="open = !open" type="button"
                                class="flex items-center justify-between w-full text-left">
                                <span class="font-bold text-gray-800">@lang('messages.last_test_result')</span>
                                <svg x-show="!open" xmlns="http://www.w3.org/2000/svg"
                                    class="w-5 h-5 text-gray-600" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 15l7-7 7 7" />
                                </svg>
                                <svg x-show="open" xmlns="http://www.w3.org/2000/svg"
                                    class="w-5 h-5 text-gray-600" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>

                            <!-- Accordion Content -->
                            @if ($deviceVerifyData->latestTestResult ?? null)
                                <div x-show="open" x-transition class="mt-4 space-y-4">
                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.tested_at'):</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->created_at->format('d/m/Y H:i') ?? 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.gps_signal'):</strong>
                                        <span
                                            class="text-gray-800">{{ isset($deviceVerifyData->latestTestResult->gps_status) ? getGPSStatus($deviceVerifyData->latestTestResult->gps_status) : 'N/A' }}</span>

                                        @php
                                            $signalValue = $deviceVerifyData->latestTestResult->signal ?? 0;
                                        @endphp
                                        <span style="transform: rotateX(180deg); scale: 0.6;" id="signalStrength"
                                            class="flex ml-2 w-fit">
                                            @for ($i = 1; $i <= 5; $i++)
                                                <span
                                                    style="width: 6px; margin: 0 1px; background-color: {{ $i <= $signalValue ? 'green' : 'lightgray' }}; height: {{ $i * 5 }}px;">
                                                </span>
                                            @endfor
                                        </span>

                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.movement'):</strong>
                                        <span
                                            class="text-gray-800">{{ isset($deviceVerifyData->latestTestResult->movement_status) ? getMovementStatus($deviceVerifyData->latestTestResult->movement_status) : 'N/A' }}</span>
                                    </p>


                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.ignition_status')</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->ignition_status ? __('messages.' . strtolower($deviceVerifyData->latestTestResult->ignition_status)) : 'N/A' }}</span>
                                    </p>
                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.engine_status')</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->engine_status ? __('messages.' . strtolower($deviceVerifyData->latestTestResult->engine_status)) : 'N/A' }}</span>
                                    </p>

                                    @if ($deviceVerifyData->latestTestResult->ignition_tested_at)
                                        <div class="flex flex-col gap-2 text-sm md:flex-row">
                                            <strong class="text-gray-800 w-36">@lang('messages.motor_block_test')</strong>
                                            <div class="flex items-center gap-2 text-gray-800">
                                                <img class="size-5" src="{{ asset('assets/images/check.svg') }}"
                                                    alt="check">

                                                <p class="font-medium text-emerald-600">
                                                    @lang('messages.ignition_tested')
                                                    {{ \Carbon\Carbon::parse($deviceVerifyData->latestTestResult->ignition_tested_at)->format('d-m-Y H:i') }}
                                                </p>

                                            </div>
                                        </div>
                                    @endif

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.battery_voltage'):</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->battery_voltage ? $deviceVerifyData->latestTestResult->battery_voltage . 'V' : 'N/A' }}</span>
                                    </p>
                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.battery_level'):</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->battery_level ? $deviceVerifyData->latestTestResult->battery_level . '%' : 'N/A' }}</span>
                                    </p>
                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.location'):</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->location ?? 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">Lat - Long:</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->latitude ?? 'N/A' }}
                                            -
                                            {{ $deviceVerifyData->latestTestResult->longitude ?? 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.speed'):</strong>
                                        <span
                                            class="text-gray-800">{{ isset($deviceVerifyData->latestTestResult->speed) ? $deviceVerifyData->latestTestResult->speed . ' km/h' : 'N/A' }}</span>
                                    </p>

                                    <p class="flex flex-col gap-2 text-sm md:flex-row">
                                        <strong class="text-gray-800 w-36">@lang('messages.last_update'):</strong>
                                        <span
                                            class="text-gray-800">{{ $deviceVerifyData->latestTestResult->last_update ?? 'N/A' }}</span>
                                    </p>
                                </div>
                            @else
                                <p class="mt-3">
                                    @lang('messages.no_test_result')
                                </p>
                            @endif
                        </div>


                        <div class="grid gap-4 md:grid-cols-2">


                            <!-- Clients Dropdown -->
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] mb-3 block">{{ __('messages.client') }}</label>

                                <div>
                                    <x-dropdown :options="$clients" name="verification_client" />
                                </div>


                                @error('verification_client')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.vehicle_type')</label>
                                <select wire:model="verification_vehicle_type" id="verification_vehicle_type"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300">
                                    <option value="car">@lang('messages.car')</option>
                                    <option value="bus">@lang('messages.bus')</option>
                                    <option value="boat">@lang('messages.boat')</option>
                                    <option value="motorcycle">@lang('messages.motorcycle')</option>
                                    <option value="scooter">@lang('messages.scooter')</option>
                                    <option value="truck">@lang('messages.truck')</option>
                                    <option value="van">@lang('messages.van')</option>
                                    <option value="tractor">@lang('messages.tractor')</option>
                                </select>

                                @error('verification_vehicle_type')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.brand')</label>
                                <input type="text" wire:model="vehicle_brand" id="vehicle_brand"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.brand')">


                                @error('vehicle_brand')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.model')</label>
                                <input type="text" wire:model="vehicle_model" id="vehicle_model"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.model')">


                                @error('vehicle_model')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.color')</label>
                                <input type="text" wire:model="vehicle_color" id="vehicle_color"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.color')">


                                @error('vehicle_color')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.frame')</label>
                                <input type="text" wire:model="frame" id="frame"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.frame')">


                                @error('frame')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>


                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.number_plate')</label>
                                <input type="text" wire:model="vehicle_number_plate" id="vehicle_number_plate"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.number_plate')">

                                @error('vehicle_number_plate')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>


                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.registration_date')</label>
                                <input type="date" wire:model="vehicle_registration_date"
                                    id="vehicle_registration_date"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300">
                                @error('vehicle_registration_date')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.travelled_distance')
                                    (@lang('messages.in_km'))</label>
                                <input type="number" wire:model="vehicle_km" id="vehicle_km"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.enter_km')">

                                @error('vehicle_km')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.starter_motor_block')</label>
                                <div class="flex items-center mt-2 space-x-4">
                                    <label class="flex items-center text-sm">
                                        <input type="radio" wire:model="starter_motor_block" value="Yes"
                                            class="text-primary focus:ring-primary h-4 w-4 border-[#D5D7DA] accent-primary">
                                        <span class="ml-2">@lang('messages.yes')</span>
                                    </label>
                                    <label class="flex items-center text-sm">
                                        <input type="radio" wire:model="starter_motor_block" value="No"
                                            class="text-primary focus:ring-primary h-4 w-4 border-[#D5D7DA] accent-primary">
                                        <span class="ml-2">@lang('messages.no')</span>
                                    </label>
                                </div>

                                @error('starter_motor_block')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">{{ __('messages.duration') }}</label>
                                <select wire:model.live="duration"
                                    class="peer
                                py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                rounded-lg text-sm focus:border-primary outline-none transition-all
                                duration-300">
                                    <option value="">@lang('messages.select_duration')</option>
                                    <option value="12">12 @lang('messages.months')</option>
                                    <option value="24">24 @lang('messages.months')</option>
                                    <option value="36">36 @lang('messages.months')</option>
                                    <option value="48">48 @lang('messages.months')</option>
                                    <option value="60">60 @lang('messages.months')</option>
                                    <option value="72">72 @lang('messages.months')</option>
                                </select>

                                @error('duration')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">{{ __('messages.start_date') }}</label>
                                <input type="date" id="start_date" wire:model.live="start_date"
                                    placeholder="{{ __('messages.start_date') }}"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                                rounded-lg text-sm outline-none transition-all
                                                duration-300">

                                @error('start_date')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">{{ __('messages.end_date') }}</label>
                                <input type="date" id="end_date" wire:model="end_date" disabled
                                    placeholder="{{ __('messages.end_date') }}"
                                    class="peer
                                                py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                                rounded-lg text-sm outline-none transition-all
                                                duration-300">

                                @error('end_date')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>



                            <div class="col-span-2">
                                <!-- Dealer Confirmation -->
                                <h3 class="mt-8 mb-2 font-medium">@lang('messages.notes')</h3>
                                <textarea wire:model="service_details"
                                    class="w-full mt-2 py-2.5 px-4 border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm text-sm focus:border-primary outline-none transition-all duration-300"
                                    rows="4" placeholder="@lang('messages.notes')"></textarea>


                                @error('service_details')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2">
                                <div class="flex gap-2 text-sm text-gray-500">
                                    <img class="size-5" src="{{ asset('assets/images/info-icon.svg') }}"
                                        alt="icon">
                                    @lang('messages.upload_installation_photo')
                                </div>
                            </div>
                            <div class="col-span-2 md:col-span-1">

                                <label
                                    class="border-2 mt-3 border-primary bg-[#F8F8FD] p-5 rounded-lg border-dashed text-gray-500 text-sm flex flex-col items-center justify-center gap-2 cursor-pointer hover:bg-[#e3e3f3] transition-all duration-300">
                                    <input type="file" wire:model="installation_image" class="hidden">

                                    <img class="size-8" src="{{ asset('assets/images/image.svg') }}"
                                        alt="image">
                                    <span wire:loading wire:target="installation_image">
                                        @lang('messages.loading')
                                    </span>
                                    <span wire:loading.remove wire:target="installation_image">
                                        @lang('messages.click_to_upload')
                                    </span>

                                </label>
                            </div>

                            <div class="flex justify-center col-span-2 md:col-span-1">
                                @if ($installation_image)
                                    <img class="mx-auto rounded-md shadow-sm h-36"
                                        src="{{ $installation_image->temporaryUrl() }}" alt="installation image">
                                @endif
                            </div>

                        </div>
                    </div>


                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false"
                            class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>
                        <button wire:click="verifyDevice" wire:loading.attr="disabled" wire:target="verifyDevice"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">

                            <span wire:loading.remove wire:target="verifyDevice"> {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="verifyDevice">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>


            </div>
        </x-slot:body>
    </x-modal>

    {{-- add client modal --}}
    <x-modal name="add-client-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="1.5" stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black">{{ __('messages.add_client') }}</h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.type')</label>

                            <select id="type" wire:model.live="type" placeholder="@lang('messages.type')"
                                class="peer
                        py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                        rounded-lg text-sm focus:border-primary outline-none transition-all
                        duration-300 mt-2">
                                <option value="private">@lang('messages.private')</option>
                                <option value="company">@lang('messages.company')</option>
                            </select>

                            @error('type')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>



                        @if ($type == 'private')
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651]">@lang('messages.surname')</label>
                                <input type="text" id="last_name" wire:model="last_name"
                                    placeholder="@lang('messages.surname')"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 mt-2">

                                @error('last_name')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        @endif

                        <div class="col-span-2 md:col-span-1">
                            @if ($type == 'company')
                                <label class="text-sm text-[#414651]">@lang('messages.company_name')</label>
                            @else
                                <label class="text-sm text-[#414651]">@lang('messages.name')</label>
                            @endif

                            <div class="relative mt-2">
                                <input type="text" wire:model="name" id="name"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@if ($type == 'company') @lang('messages.company_name') @else @lang('messages.name') @endif">
                            </div>

                            @error('name')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>


                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.username')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model.blur="username" id="username"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.username')">
                            </div>

                            @error('username')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">Email</label>

                            <div class="relative mt-2">
                                <input type="email" wire:model.blur="email" id="email"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="Email">

                            </div>

                            @error('email')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">@lang('messages.phone_number')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="phone_number" id="phone_number"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"
                                    placeholder="@lang('messages.phone_number')">

                            </div>

                            @error('phone_number')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        <!-- Password Field -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.password') }}</label>
                            <div x-data="{
                                showPassword: false,
                                password: '',
                                generatePassword() {
                                    // Function to generate a random password (8 characters with letters and digits)
                                    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                                    let generatedPassword = '';
                                    for (let i = 0; i < 8; i++) {
                                        generatedPassword += chars.charAt(Math.floor(Math.random() * chars.length));
                                    }
                                    this.password = generatedPassword;
                                    // Set the generated password to Livewire model
                                    @this.set('password', generatedPassword);
                                }
                            }" class="relative">

                                <!-- Password Input Field -->
                                <input x-bind:type="showPassword ? 'text' : 'password'" id="password"
                                    x-model="password" wire:model.blur="password"
                                    placeholder="{{ __('messages.password') }}"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                   rounded-lg text-sm focus:border-primary outline-none transition-all
                                   duration-300">

                                <!-- password show hide button -->
                                <div class="absolute z-10 cursor-pointer top-3 right-2"
                                    @click="showPassword = !showPassword">
                                    <img x-show="!showPassword" id="show-icon" class="size-5"
                                        src="{{ asset('assets/images/eye.svg') }}" alt="Show password">
                                    <img x-show="showPassword" id="hide-icon" class="size-5"
                                        src="{{ asset('assets/images/eye-closed.svg') }}" alt="Hide password">
                                </div>

                                <!-- Generate Password Button -->
                                <button type="button" @click="generatePassword"
                                    class="absolute top-0 bottom-0 transition-all duration-300 rounded text-primary right-9">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="1.8" stroke="currentColor" class="size-6">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 0 0-3.7-3.7 48.678 48.678 0 0 0-7.324 0 4.006 4.006 0 0 0-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 0 0 3.7 3.7 48.656 48.656 0 0 0 7.324 0 4.006 4.006 0 0 0 3.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3-3 3" />
                                    </svg>
                                </button>

                            </div>


                            @error('password')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>



                        <!-- Address Field -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.address') }}</label>
                            <input type="text" id="address" wire:model.blur="address"
                                placeholder="{{ __('messages.address') }}"
                                class="peer
                                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm focus:border-primary outline-none transition-all
                                            duration-300">

                            @error('address')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Municipality Field -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.municipality') }}</label>
                            <input type="text" id="municipality" wire:model.blur="municipality"
                                placeholder="{{ __('messages.municipality') }}"
                                class="peer
                                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm focus:border-primary outline-none transition-all
                                            duration-300">

                            @error('municipality')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- ZIP Code Field -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.zip_code') }}</label>
                            <input type="text" id="zip_code" wire:model.blur="zip_code"
                                placeholder="{{ __('messages.zip_code') }}"
                                class="peer
                                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm focus:border-primary outline-none transition-all
                                            duration-300">

                            @error('zip_code')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Province Field -->
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651]">{{ __('messages.province') }}</label>
                            <input type="text" id="province" wire:model.blur="province"
                                placeholder="{{ __('messages.province') }}"
                                class="peer
                                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm focus:border-primary outline-none transition-all
                                            duration-300">

                            @error('province')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Tax Code Field -->
                        <div class="col-span-2 md:col-span-1">
                            @if ($type == 'company')
                                <label class="text-sm text-[#414651]">{{ __('messages.vat_number') }}</label>
                            @else
                                <label class="text-sm text-[#414651]">{{ __('messages.tax_code') }}</label>
                            @endif
                            <input type="text" id="tax_code" wire:model.blur="tax_code"
                                placeholder="@if ($type == 'company') {{ __('messages.vat_number') }}
                        @else {{ __('messages.tax_code') }} @endif"
                                class="peer
                                        py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                        rounded-lg text-sm focus:border-primary outline-none transition-all
                                        duration-300">

                            @error('tax_code')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>


                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false;"
                            class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>

                        <button wire:click="addUpdateClient" wire:loading.attr="disabled"
                            wire:target="addUpdateClient" type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            <span wire:loading.remove wire:target="addUpdateClient"> {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="addUpdateClient">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

    {{-- import device modal --}}
    <x-modal name="import-devices">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="1.5" stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black">@lang('messages.import_devices')</h2>
                </div>


                <div class="mt-4">

                    <label for="file-input" class="sr-only">Choose file</label>
                    <input type="file" wire:model="csvFile" id="file-input"
                        class="block w-full text-sm border border-gray-200 rounded-lg shadow-sm focus:z-10 focus:border-primary focus:ring-primary disabled:opacity-50 disabled:pointer-events-none file:bg-gray-50 file:border-0 file:me-4 file:py-3 file:px-4 ">

                    @error('csvFile')
                        <div class="mt-2 text-sm text-red-500">{{ $message }}</div>
                    @enderror
                    <div class="flex mt-5 ">
                        <img class="flex-shrink-0 mt-1 size-5" src="{{ asset('assets/images/info-icon.svg') }}"
                            alt="map">
                        <p class="text-sm text-gray-600 ms-3">
                            @lang('messages.import_instructions')
                            <br>
                            @lang('messages.import_instructions_details')
                        </p>
                    </div>

                    <div x-data="{ showImage: false }">
                        <button @click="showImage = !showImage"
                            class="text-sm font-semibold text-primary">@lang('messages.show_example')</button>
                        <img x-show="showImage" x-cloak class="w-full mx-auto mt-4"
                            src="{{ asset('assets/images/import-data.png') }}" alt="import-data">
                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false;"
                            class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>

                        <button wire:click="importDevices" wire:loading.attr="disabled"
                            wire:target="importDevices" type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            <span wire:loading.remove wire:target="importDevices"> {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="importDevices">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

</main>
