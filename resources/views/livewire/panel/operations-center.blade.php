@section('device_logs_active', 'bg-primary/10 text-primary')


<main class="flex-shrink w-full overflow-auto">
    <style>
        #worldMap {
            height: 400px;
            width: 100%;
        }
    </style>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css" />
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js"></script>
    <section wire:poll.keep-alive.5s class="w-full p-6 mx-auto my-5">


        <div wire:ignore class="flex flex-wrap justify-end gap-3" x-data="{ text: '@lang('messages.notify_for_alarams')' }">
            <button wire:click="addLog"
                class="mt-4 flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-primaryDark ">
                <svg class="size-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                    <g id="SVGRepo_iconCarrier">
                        <path
                            d="M11 8C11 7.44772 11.4477 7 12 7C12.5523 7 13 7.44772 13 8V11H16C16.5523 11 17 11.4477 17 12C17 12.5523 16.5523 13 16 13H13V16C13 16.5523 12.5523 17 12 17C11.4477 17 11 16.5523 11 16V13H8C7.44771 13 7 12.5523 7 12C7 11.4477 7.44772 11 8 11H11V8Z"
                            fill="#ffffff"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12ZM3.00683 12C3.00683 16.9668 7.03321 20.9932 12 20.9932C16.9668 20.9932 20.9932 16.9668 20.9932 12C20.9932 7.03321 16.9668 3.00683 12 3.00683C7.03321 3.00683 3.00683 7.03321 3.00683 12Z"
                            fill="#ffffff"></path>
                    </g>
                </svg>
                @lang('messages.add')
            </button>
            <button @click="text = '@lang('messages.alaram_sound_enabled')'"
                class="mt-4 flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-primaryDark ">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5 me-1">
                    <path
                        d="M5.85 3.5a.75.75 0 0 0-1.117-1 9.719 9.719 0 0 0-2.348 4.876.75.75 0 0 0 1.479.248A8.219 8.219 0 0 1 5.85 3.5ZM19.267 2.5a.75.75 0 1 0-1.118 1 8.22 8.22 0 0 1 1.987 *********** 0 0 0 1.48-.248A9.72 9.72 0 0 0 19.266 2.5Z" />
                    <path fill-rule="evenodd"
                        d="M12 2.25A6.75 6.75 0 0 0 5.25 9v.75a8.217 8.217 0 0 1-2.119 ********** 0 0 0 .298 1.206c1.544.57 3.16.99 4.831 1.243a3.75 3.75 0 1 0 7.48 0 24.583 24.583 0 0 0 4.83-*********** 0 0 0 .298-1.205 8.217 8.217 0 0 1-2.118-5.52V9A6.75 6.75 0 0 0 12 2.25ZM9.75 18c0-.034 0-.067.002-.1a25.05 25.05 0 0 0 4.496 0l.002.1a2.25 2.25 0 1 1-4.5 0Z"
                        clip-rule="evenodd" />
                </svg>
                <span x-text="text"></span>
            </button>
        </div>

        @if ($startAlarm)

            <div>
                <!-- Alarm UI -->
                <div class="flex flex-col items-center justify-center">
                    <div class="text-center">
                        <img src="{{ asset('assets/images/alarm-buzzle-unscreen.gif') }}" alt="Alarm"
                            class="w-36 h-36 animate-pulse">
                    </div>

                    <button wire:click="stopAlaram"
                        class="px-3 py-1 text-sm font-medium text-white bg-red-500 rounded-lg ">@lang('messages.stop_alaram')</button>

                </div>
                <!-- Hidden Audio Element -->
                <audio wire:ignore id="alarm-sound" autoplay preload>
                    <source src="{{ asset('assets/alarm.mp3') }}" type="audio/mpeg">
                    Your browser does not support the audio element.
                </audio>
            </div>

            <script>
                let audioInitialized = false;

                function initializeAudio() {
                    if (!audioInitialized) {
                        const alarmSound = document.getElementById('alarm-sound');
                        if (alarmSound) {
                            alarmSound.play().catch(() => {
                                // Pause immediately if not allowed, just to prepare the audio for later use.
                                alarmSound.pause();
                            });
                        }
                        audioInitialized = true;
                    }
                }

                playAlarmSound = () => {
                    const alarmSound = document.getElementById('alarm-sound');
                    if (alarmSound) {
                        alarmSound.play().catch((error) => {
                            console.warn("Audio playback failed. Waiting for user interaction.", error);
                        });
                    }
                }

                document.addEventListener('start-alaram', () => {
                    initializeAudio()
                });

                // Prepare audio on the first user interaction
                document.addEventListener('click', initializeAudio, {
                    once: true
                });
                document.addEventListener('touchstart', initializeAudio, {
                    once: true
                });
            </script>


            @script
                <script>
                    setTimeout(() => {
                        $wire.dispatch('start-alaram');
                    }, 1000);
                </script>
            @endscript

        @endif



        <div class="w-full p-5 mt-10 mb-20 overflow-hidden bg-white rounded-lg shadow-md md:rounded-2xl md:p-10">
            <div class="flex flex-wrap items-center justify-between flex-grow md:gap-4">
                <h2 class="text-xl font-semibold">@lang('messages.operations_center')</h2>

                <div wire:ignore class="flex flex-col items-center flex-grow gap-4 md:flex-row md:justify-end">

                    <div class="flex items-center ms-auto">
                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">

                            <button wire:click="exportPDF" @mouseenter="showTooltip = true"
                                @mouseleave="showTooltip = false"
                                class="p-2 text-sm text-white transition-all duration-300 bg-red-500 rounded-lg hover:bg-red-600 ">
                                <img src="{{ asset('assets/images/pdf.svg') }}" alt="excel" class="size-5">
                            </button>
                            <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                x-transition:enter-start="opacity-0 transform scale-95"
                                x-transition:enter-end="opacity-100 transform scale-100"
                                x-transition:leave="transition ease-in duration-150"
                                x-transition:leave-start="opacity-100 transform scale-100"
                                x-transition:leave-end="opacity-0 transform scale-95"
                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                style="display: none;">
                                @lang('messages.export_pdf')
                            </div>
                        </div>

                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                            <button wire:click="exportExcel" @mouseenter="showTooltip = true"
                                @mouseleave="showTooltip = false"
                                class="p-2 text-sm text-white transition-all duration-300 rounded-lg ms-2 bg-emerald-500 hover:bg-emerald-600 ">
                                <img src="{{ asset('assets/images/excel.svg') }}" alt="excel" class="size-5">
                            </button>
                            <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                x-transition:enter-start="opacity-0 transform scale-95"
                                x-transition:enter-end="opacity-100 transform scale-100"
                                x-transition:leave="transition ease-in duration-150"
                                x-transition:leave-start="opacity-100 transform scale-100"
                                x-transition:leave-end="opacity-0 transform scale-95"
                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                style="display: none;">
                                @lang('messages.export_excel')
                            </div>
                        </div>
                    </div>
                    <div class="w-full max-w-xs">
                        <div class="flex items-center w-full space-x-5">
                            <div class="flex w-full p-3 space-x-2 text-sm bg-gray-100 rounded-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <input wire:model.live.debounce.500ms="search" class="bg-gray-100 outline-none"
                                    type="search" placeholder="{{ __('messages.search') }}" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-end w-full text-sm font-medium text-primary">
                    <label class="flex items-center gap-1 cursor-pointer">
                        <input type="checkbox" wire:model.live="showArchived"
                            class="border-2 rounded shrink-0 border-primary text-primary focus:ring-primary disabled:opacity-50 disabled:pointer-events-none accent-primary">
                        <span class="select-none">
                            @lang('messages.show_archived')
                        </span>
                    </label>
                </div>
            </div>
            <div class="w-full mt-10 mb-5 overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr
                            class="text-xs font-semibold tracking-wide text-left text-gray-900 uppercase border-b border-gray-600">
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.device')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.log')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.status')</th>
                            <th class="px-4 py-3 border whitespace-nowrap">@lang('messages.created_at')</th>
                            <th class="px-4 py-3 border whitespace-nowrap text-end min-w-28">@lang('messages.actions')</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white">
                        @forelse ($deviceLogs as $deviceLog)
                            <tr class="text-gray-700">

                                <td class="px-4 py-3 text-sm font-medium">
                                    <div class="relative w-fit">

                                        {{ $deviceLog->device->imei ?? 'N/A' }}

                                        @if ($deviceLog->is_new)
                                            <div
                                                class="absolute bg-red-500 rounded-full -top-3 -right-3 size-3 blink-indicator">
                                            </div>
                                        @endif
                                    </div>


                                </td>

                                <td class="px-4 py-3 text-sm text-wrap">{{ $deviceLog->log ?? 'N/A' }}</td>

                                <td class="px-4 py-3 text-sm text-nowrap">
                                    @if ($deviceLog->status == 'open')
                                        <span
                                            class="inline-flex items-center gap-x-1.5 py-1 px-3 rounded-full text-xs font-medium bg-teal-500 text-white">@lang('messages.open')</span>
                                    @elseif ($deviceLog->status == 'suspended')
                                        <span
                                            class="inline-flex items-center gap-x-1.5 py-1 px-3 rounded-full text-xs font-medium bg-gray-500 text-white">@lang('messages.suspended')</span>
                                    @elseif ($deviceLog->status == 'under_management')
                                        <span
                                            class="inline-flex items-center gap-x-1.5 py-1 px-3 rounded-full text-xs font-medium bg-yellow-500 text-white">@lang('messages.under_management')</span>
                                    @elseif ($deviceLog->status == 'closed')
                                        <span
                                            class="inline-flex items-center gap-x-1.5 py-1 px-3 rounded-full text-xs font-medium bg-red-500 text-white">@lang('messages.closed')</span>
                                    @endif
                                </td>

                                <td class="px-4 py-3 text-sm">{{ $deviceLog->created_at->format('d/m/Y H:i') }}</td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="flex items-center justify-end gap-2">

                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="showDevice({{ $deviceLog->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5" src="{{ asset('assets/images/device.svg') }}"
                                                    alt="Edit">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                {{ __('messages.device') }}
                                            </div>
                                        </div>

                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="editRecord({{ $deviceLog->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5" src="{{ asset('assets/images/edit.svg') }}"
                                                    alt="Edit">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('messages.add_notes')
                                            </div>
                                        </div>


                                    </div>

                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="px-4 py-3 text-center">{{ __('messages.no_record_found') }}
                                </td>
                            </tr>
                        @endforelse

                    </tbody>
                </table>
            </div>

            {{ $deviceLogs->links('livewire.custom-pagination') }}

        </div>
    </section>

    {{-- modals --}}

    {{-- add device logs modal --}}
    <x-modal name="add-device-logs-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false; $wire.call('clearRecords')" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black">@lang('messages.device_log')</h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">


                        <!-- Dealer Dropdown -->
                        @if ($recordId)
                            <div class="col-span-2">
                                <label class="text-sm text-[#414651]">@lang('messages.device')</label>

                                <input type="text" disabled wire:model="device_imei"
                                    class="peer
                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                            rounded-lg text-sm focus:border-primary outline-none transition-all
                            duration-300">

                                @error('device')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        @else
                            <div class="col-span-2">
                                <label class="text-sm text-[#414651]">{{ __('messages.devices') }}</label>

                                <x-dropdown name="device_imei" :options="$devices" />


                                @error('device_imei')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        @endif


                        <div class="col-span-2">
                            <label class="text-sm text-[#414651]">@lang('messages.log')</label>
                            <textarea type="text" id="address" wire:model="log" placeholder="Enter device log"
                                class="peer
                                            py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                                            rounded-lg text-sm focus:border-primary outline-none transition-all
                                            duration-300"></textarea>

                            @error('log')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Radio Buttons -->
                        <div class="col-span-2 text-sm">
                            <label class="text-sm text-[#414651]">@lang('messages.select_action')</label>
                            <div class="grid gap-4 mt-2 md:grid-cols-2">
                                <label class="flex items-center gap-2">
                                    <input class="accent-primary" type="radio" wire:model.live="selectedOption"
                                        value="contacted_owner">

                                    @lang('messages.contact_owner')
                                </label>
                                <label class="flex items-center gap-2">
                                    <input class="accent-primary" type="radio" wire:model.live="selectedOption"
                                        value="contact_the_force_of_order">

                                    @lang('messages.contact_the_force_of_order')

                                </label>
                                <label class="flex items-center gap-2">
                                    <input class="accent-primary" type="radio" wire:model.live="selectedOption"
                                        value="false_alaram">
                                    @lang('messages.false_alaram')

                                </label>
                                <label class="flex items-center gap-2">
                                    <input class="accent-primary" type="radio" wire:model.live="selectedOption"
                                        value="other">
                                    @lang('messages.other')
                                </label>
                            </div>
                            @error('selectedOption')
                                <div class="mt-2 text-xs text-red-500">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Free Notes (conditionally shown) -->
                        @if ($selectedOption === 'other')
                            <div class="col-span-2">
                                <label class="text-sm text-[#414651]"> @lang('messages.notes')
                                </label>
                                <textarea wire:model="free_notes" placeholder="@lang('messages.enter_notes')"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                            rounded-lg text-sm focus:border-primary outline-none transition-all duration-300"></textarea>
                                @error('free_notes')
                                    <div class="mt-2 text-xs text-red-500">{{ $message }}</div>
                                @enderror
                            </div>
                        @endif


                        <div class="col-span-2">
                            <label class="text-sm text-[#414651]">@lang('messages.status')</label>

                            <select wire:model="status"
                                class="peer
                        py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                        rounded-lg text-sm focus:border-primary outline-none transition-all
                        duration-300">
                                <option value="open">@lang('messages.open')</option>
                                <option value="suspended">@lang('messages.suspended')</option>
                                <option value="under_management">@lang('messages.under_management')</option>
                                <option value="closed">@lang('messages.closed')</option>
                            </select>

                            @error('status')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false; $wire.call('clearRecords')"
                            class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            {{ __('messages.cancel') }}
                        </button>

                        <button wire:click="addUpdateDeviceLog" wire:loading.attr="disabled"
                            wire:target="addUpdateDeviceLog" type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            <span wire:loading.remove wire:target="addUpdateDeviceLog"> {{ __('messages.submit') }}
                            </span>
                            <div wire:loading wire:target="addUpdateDeviceLog">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>


    {{-- add device logs modal --}}
    <x-modal name="show-device-details">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-medium text-center text-black">@lang('messages.device_user_details')</h2>
                <button @click="show = false; $wire.call('clearRecords')" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <!-- Content -->
            <div class="w-full mt-4">
                <div class="p-6 bg-white rounded-lg shadow">
                    @if ($selectedDevice)
                        <!-- Device Information -->
                        @if ($selectedDevice->device)
                            <div class="pb-4 mb-4 text-sm border-b">
                                <h3
                                    class="flex items-center gap-2 pb-1 mb-2 text-lg font-semibold border-b-2 border-gray-200 text-primary">
                                    <img class="size-4" src="{{ asset('assets/images/device.svg') }}"
                                        alt="device">
                                    @lang('messages.device_information')
                                </h3>
                                <ul class="pb-3 space-y-2 border-b-2 border-gray-200">
                                    <li><span class="font-medium text-gray-700">IMEI:</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->imei }}</span></li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.model'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->model }}</span></li>
                                    <li><span class="font-medium text-gray-700">ICCID:</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->iccid }}</span></li>
                                    <li><span class="font-medium text-gray-700">IMSI:</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->imsi }}</span></li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.vehicle_type'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->vehicle_type }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.active'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->is_active ? 'Yes' : 'No' }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.tested'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->is_tested ? 'Yes' : 'No' }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.verified'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->is_verified ? 'Yes' : 'No' }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.created_at'):</span> <span
                                            class="text-gray-600">{{ \Carbon\Carbon::parse($selectedDevice->device->created_at)->format('d/m/Y H:i') }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.updated_at'):</span> <span
                                            class="text-gray-600">{{ \Carbon\Carbon::parse($selectedDevice->device->updated_at)->format('d/m/Y H:i') }}</span>
                                    </li>
                                </ul>

                                @if ($deviceContractInfo)
                                    <div class="mt-3 space-y-2">
                                        <h2 class="text-lg font-medium">
                                            @lang('messages.additional_vehicle_info')

                                        </h2>
                                        <p><span class="mt-2 font-medium text-gray-700">@lang('messages.vehicle_verification_type')</span>
                                            {{ $deviceContractInfo->verification_vehicle_type }}</p>
                                        <p><span class="mt-2 font-medium text-gray-700">@lang('messages.brand'):</span>
                                            {{ $deviceContractInfo->vehicle_brand }}</p>
                                        <p><span class="mt-2 font-medium text-gray-700">@lang('messages.model'):</span>
                                            {{ $deviceContractInfo->vehicle_model }}</p>
                                        <p><span class="mt-2 font-medium text-gray-700">@lang('messages.color'):</span>
                                            {{ $deviceContractInfo->vehicle_color }}</p>

                                        <p><span class="mt-2 font-medium text-gray-700">@lang('messages.registration_date'):</span>
                                            {{ $deviceContractInfo->vehicle_registration_date }}</p>
                                        <p><span class="mt-2 font-medium text-gray-700">@lang('messages.number_plate'):</span>
                                            {{ $deviceContractInfo->vehicle_number_plate }}</p>

                                        <p><span class="mt-2 font-medium text-gray-700">@lang('messages.frame'):</span>
                                            {{ $deviceContractInfo->frame }}</p>
                                        <p><span class="mt-2 font-medium text-gray-700">@lang('messages.travelled_distance'):</span>
                                            {{ $deviceContractInfo->vehicle_km }}</p>
                                        <p><span class="mt-2 font-medium text-gray-700">@lang('messages.starter_motor_block'):</span>
                                            {{ $deviceContractInfo->starter_motor_block }}</p>
                                    </div>
                                @endif
                            </div>
                        @endif

                        <!-- Client Information -->
                        @if ($selectedDevice->device && $selectedDevice->device->client)
                            <div class="pb-4 mb-4 border-b">
                                <h3
                                    class="flex items-center gap-2 pb-1 mb-2 text-lg font-semibold border-b-2 border-gray-200 text-emerald-600">

                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                        class="size-4">
                                        <path fill-rule="evenodd"
                                            d="M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z"
                                            clip-rule="evenodd" />
                                    </svg>


                                    @lang('messages.client_information')
                                </h3>
                                <ul class="space-y-2">
                                    <li><span class="font-medium text-gray-700">@lang('messages.passkey'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->client->passkey }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.address'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->client->address }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.municipality'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->client->municipality }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.zip_code'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->client->zip_code }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.province'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->client->province }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.tax_code'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->client->tax_code }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.phone_number'):</span> <span
                                            class="text-gray-600">{{ $selectedDevice->device->client->phone_number }}</span>
                                    </li>
                                </ul>
                            </div>
                            <!-- Client User Information -->
                            @if ($selectedDevice->device->client->user)
                                <div class="pb-4 mb-4 border-b">
                                    <ul class="space-y-2">
                                        @if ($selectedDevice?->device?->client?->type == 'company')
                                            <li><span class="font-medium text-gray-700">@lang('messages.company_name')</span>
                                                <span
                                                    class="text-gray-600">{{ $selectedDevice?->device?->client?->user?->name }}</span>
                                            </li>
                                        @else
                                            <li><span class="font-medium text-gray-700">@lang('messages.name')</span>
                                                <span
                                                    class="text-gray-600">{{ $selectedDevice?->device?->client?->user?->name }}
                                                    {{ $selectedDevice?->device?->client?->last_name }}</span>
                                            </li>
                                        @endif
                                        <li><span class="font-medium text-gray-700">@lang('messages.username'):</span> <span
                                                class="text-gray-600">{{ $selectedDevice->device->client->user->username }}</span>
                                        </li>
                                        <li><span class="font-medium text-gray-700">Email:</span> <span
                                                class="text-gray-600">{{ $selectedDevice->device->client->user->email }}</span>
                                        </li>
                                        <li><span class="font-medium text-gray-700">@lang('messages.active'):</span> <span
                                                class="text-gray-600">{{ $selectedDevice->device->client->user->is_active ? 'Yes' : 'No' }}</span>
                                        </li>
                                        <li><span class="font-medium text-gray-700">@lang('messages.language'):</span> <span
                                                class="text-gray-600">{{ $selectedDevice->device->client->user->language }}</span>
                                        </li>
                                    </ul>
                                </div>
                            @endif
                        @endif



                        <!-- Dealer User Information -->
                        @if ($selectedDevice->device && $selectedDevice->device->dealer)
                            @if ($selectedDevice->device->dealer->user)
                                <div class="pb-4 mb-4 border-b">
                                    <h3
                                        class="flex items-center gap-2 pb-1 mb-2 text-lg font-semibold border-b-2 border-gray-200 text-sky-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                            fill="currentColor" class="size-4">
                                            <path fill-rule="evenodd"
                                                d="M7.5 5.25a3 3 0 0 1 3-3h3a3 3 0 0 1 3 3v.205c.933.085 1.857.197 2.774.334 1.454.218 2.476 1.483 2.476 2.917v3.033c0 1.211-.734 2.352-1.936 2.752A24.726 24.726 0 0 1 12 15.75c-2.73 0-5.357-.442-7.814-1.259-1.202-.4-1.936-1.541-1.936-2.752V8.706c0-1.434 1.022-2.7 2.476-2.917A48.814 48.814 0 0 1 7.5 5.455V5.25Zm7.5 0v.09a49.488 49.488 0 0 0-6 0v-.09a1.5 1.5 0 0 1 1.5-1.5h3a1.5 1.5 0 0 1 1.5 1.5Zm-3 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z"
                                                clip-rule="evenodd" />
                                            <path
                                                d="M3 18.4v-2.796a4.3 4.3 0 0 0 .713.31A26.226 26.226 0 0 0 12 17.25c2.892 0 5.68-.468 8.287-1.335.252-.084.49-.189.713-.311V18.4c0 1.452-1.047 2.728-2.523 2.923-2.12.282-4.282.427-6.477.427a49.19 49.19 0 0 1-6.477-.427C4.047 21.128 3 19.852 3 18.4Z" />
                                        </svg>


                                        @lang('messages.device_dealer_info')
                                    </h3>
                                    <ul class="space-y-2">
                                        <li><span class="font-medium text-gray-700">@lang('messages.name'):</span> <span
                                                class="text-gray-?600">{{ $selectedDevice?->device?->dealer?->user?->name }}
                                                @if ($selectedDevice?->device?->dealer?->type == 'private')
                                                    {{ $selectedDevice?->device?->dealer?->last_name }}
                                                @endif
                                            </span>
                                        </li>
                                        <li><span class="font-medium text-gray-700">@lang('messages.username'):</span> <span
                                                class="text-gray-600">{{ $selectedDevice->device?->dealer?->user?->username }}</span>
                                        </li>
                                        <li><span class="font-medium text-gray-700">Email:</span> <span
                                                class="text-gray-600">{{ $selectedDevice->device?->dealer?->user?->email }}</span>
                                        </li>
                                        <li><span class="font-medium text-gray-700">@lang('messages.language'):</span> <span
                                                class="text-gray-600">{{ $selectedDevice->device?->dealer?->user?->language }}</span>
                                        </li>
                                    </ul>
                                </div>
                            @endif

                            <div class="pb-4 mb-4 border-b">
                                <ul class="space-y-2">
                                    @if ($selectedDevice?->device?->dealer?->type == 'company')
                                        <li><span class="font-medium text-gray-700">@lang('messages.company_name')</span>
                                            <span
                                                class="text-gray-600">{{ $selectedDevice?->device?->dealer?->user?->name }}</span>
                                        </li>
                                    @else
                                        <li><span class="font-medium text-gray-700">@lang('messages.name')</span>
                                            <span
                                                class="text-gray-600">{{ $selectedDevice?->device?->dealer?->user?->name }}
                                                {{ $selectedDevice?->device?->dealer?->last_name }}</span>
                                        </li>
                                    @endif

                                    <li><span class="font-medium text-gray-700">@lang('messages.address')</span> <span
                                            class="text-gray-600">{{ $selectedDevice?->device?->dealer?->address }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.municipality')</span> <span
                                            class="text-gray-600">{{ $selectedDevice?->device?->dealer?->municipality }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.zip_code')</span> <span
                                            class="text-gray-600">{{ $selectedDevice?->device?->dealer?->zip_code }}</span>
                                    </li>
                                    <li><span class="font-medium text-gray-700">@lang('messages.province')</span> <span
                                            class="text-gray-600">{{ $selectedDevice?->device?->dealer?->province }}</span>
                                    </li>
                                </ul>
                            </div>
                        @endif
                    @else
                        <p class="text-gray-600">@lang('messages.no_record_found')</p>
                    @endif

                    <div wire:ignore class="mt-6">
                        <h3
                            class="flex items-center gap-2 pb-1 mb-2 text-lg font-semibold border-b-2 border-gray-200 text-rose-500">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                class="size-4">
                                <path fill-rule="evenodd"
                                    d="M8.161 2.58a1.875 1.875 0 0 1 1.678 0l4.993 2.498c.**************.336 0l3.869-1.935A1.875 1.875 0 0 1 21.75 4.82v12.485c0 .71-.401 1.36-1.037 1.677l-4.875 2.437a1.875 1.875 0 0 1-1.676 0l-4.994-2.497a.375.375 0 0 0-.336 0l-3.868 1.935A1.875 1.875 0 0 1 2.25 19.18V6.695c0-.71.401-1.36 1.036-1.677l4.875-2.437ZM9 6a.75.75 0 0 1 .75.75V15a.75.75 0 0 1-1.5 0V6.75A.75.75 0 0 1 9 6Zm6.75 3a.75.75 0 0 0-1.5 0v8.25a.75.75 0 0 0 1.5 0V9Z"
                                    clip-rule="evenodd" />
                            </svg>

                            @lang('messages.device_geofence')
                        </h3>
                        <div id="worldMap" class="h-64 mt-5 border border-gray-200 rounded"></div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false; $wire.call('clearRecords')"
                        class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                        {{ __('messages.close') }}
                    </button>
                </div>
            </div>
        </x-slot:body>
    </x-modal>
    @script
        <script>
            let map;
            let drawnItems;

            // Initialize map after the DOM is fully loaded
            map = L.map('worldMap').setView([41.8719, 12.5674], 5);
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

            drawnItems = new L.FeatureGroup();
            map.addLayer(drawnItems);

            map.invalidateSize();

            function fetchGeofences(imei) {
                fetch(`/api/get-geofence?imei=${imei}`)
                    .then(response => response.json())
                    .then(geofences => {
                        // drawnItems.clearLayers();
                        console.log(geofences);
                        geofences.forEach(geofence => {
                            const coordinates = JSON.parse(geofence.coordinates);
                            let layer;
                            if (geofence.type === 'polygon' || geofence.type === 'rectangle') {
                                layer = L.polygon(coordinates).addTo(drawnItems);
                            } else if (geofence.type === 'circle') {
                                layer = L.circle(coordinates.slice(0, 2), {
                                    radius: coordinates[2]
                                }).addTo(drawnItems);
                            }
                            if (layer) {
                                layer.bindPopup(`Geofence ID: ${geofence.id}`);
                                layer.on('click', () => {
                                    alert(`Clicked on Geofence ID: ${geofence.id}`);
                                });
                            }
                        });
                    })
                    .catch(error => console.error('Error fetching geofences:', error));
            }

            $wire.on('showGeofence', (event) => {
                drawnItems.clearLayers();

                setTimeout(() => {
                    console.log(event.imei);
                    // Invalidate map size to fix gray areas
                    map.invalidateSize();
                    // Fetch geofences for a specific IMEI
                    fetchGeofences(event.imei);
                }, 500); // Small delay to allow DOM to settle
            });

            let markers = {}; // Object to store markers
            let markerLayer = L.layerGroup().addTo(map); // Layer group to manage markers

            $wire.on('showDeviceMarkers', (deviceData) => {
                // Clear existing markers from the layer group
                markerLayer.clearLayers();
                markers = {}; // Reset the markers object

                console.log(deviceData);

                deviceData.forEach(device => {
                    const {
                        imei,
                        latitude,
                        longitude,
                        vehicle_type,
                        ignition_status,
                        movement_status
                    } = device;

                    // Determine pin color based on ignition and movement statuses
                    let color = getPinColor(ignition_status, movement_status);
                    let iconUrl = `{{ asset('assets/images/${vehicle_type}/${color}.svg') }}`;

                    let icon = L.icon({
                        iconUrl: iconUrl,
                        iconSize: [40, 80], // Adjust the size as needed
                        iconAnchor: [12, 41] // Adjust the anchor point as needed
                    });

                    // Add new marker to the map and layer group
                    markers[imei] = L.marker([latitude, longitude], {
                            icon: icon
                        })
                        .addTo(markerLayer); // Add to layer group

                    // Optional: Fly to the marker
                    map.flyTo([latitude, longitude], 13);
                });
            });


            // Function to determine the pin color
            function getPinColor(ignitionStatus, movementStatus) {
                if (movementStatus == 1) {
                    return "green";
                } else if (ignitionStatus == 1 && movementStatus == 0) {
                    return "yellow";
                } else {
                    return "red";
                }
            }
        </script>
    @endscript



    {{-- delete device logs modal --}}
    <x-modal name="delete-record-modal">
        <x-slot:body>

            <div class="flex items-center justify-between">
                <img class="size-10" src="{{ asset('assets/images/logout-icon.svg') }}" alt="logout">

                <button @click="show = false" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>

            </div>

            <div class="mt-4">
                <h3 class="font-medium">
                    {{ __('messages.delete_confirmation') }}
                </h3>

                <p class="text-sm mt-2 text-[#535862]">
                    {{ __('messages.delete_warning') }}
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                        {{ __('messages.cancel') }}
                    </button>
                    <button wire:click="deleteRecord"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        {{ __('messages.delete') }}
                    </button>
                </div>
            </div>


        </x-slot:body>
    </x-modal>

</main>
