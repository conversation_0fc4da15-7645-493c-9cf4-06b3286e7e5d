<main class="container w-full min-h-screen gap-20 mx-auto font-roboto">

    <!-- right section -->
    <div class="p-10 lg:p-20">

        <div class="absolute flex items-center justify-end md:top-10 md:right-10 top-5 right-5">
            <livewire:language-switcher color="primary" position="top" />
        </div>

        <div class="flex flex-col items-center justify-center text-center">
            <img class="h-12" src="{{ asset('assets/images/logo.svg') }}" alt="logo">

            <h1 class="md:text-3xl text-2xl font-semibold mt-10 text-primary tracking-wide [word-spacing:1px]">
                {{ __('messages.contract_heading') }}
            </h1>

            <p class="mt-4 text-sm font-light text-lightGray">
                {{ __('messages.contract_paragraph') }}

            </p>

            <h2 class="text-lg font-semibold mt-5 text-primary tracking-wide [word-spacing:1px]">
                @lang('messages.contract_number'): {{ $contract->contract_number }}
            </h2>
        </div>


        @if (!$this->contract->responded_at)

            <div class="flex items-center justify-center mt-3 text-center">
                <button wire:click="downloadContract"
                    class="px-4 py-2 text-sm text-white rounded-md bg-primary hover:bg-primaryDark">
                    Download & View Contract
                </button>
            </div>

            <div class="grid gap-5 mt-5 md:grid-cols-2">
                <div>
                    <p class="mt-4 text-sm font-light text-[#465686] tracking-wide">@lang('messages.memove_privacy')</p>
                    <div class="grid gap-2 mt-4 sm:grid-cols-2">
                        <label for="memove_privacy_accept"
                            class="flex w-full p-3 text-sm bg-white border border-gray-200 rounded-lg cursor-pointer focus:border-blue-500 focus:ring-blue-500">
                            <span class="text-sm text-gray-500">@lang('messages.accept')</span>
                            <input type="radio" wire:model="memove_privacy" value="accepted"
                                class="shrink-0 ms-auto mt-0.5 border-gray-200 rounded-full text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none accent-primary"
                                id="memove_privacy_accept">
                        </label>

                        <label for="memove_privacy_reject"
                            class="flex w-full p-3 text-sm bg-white border border-gray-200 rounded-lg cursor-pointer focus:border-blue-500 focus:ring-blue-500">
                            <span class="text-sm text-gray-500">@lang('messages.non_accept')</span>
                            <input type="radio" wire:model="memove_privacy" value="rejected"
                                class="shrink-0 ms-auto mt-0.5 border-gray-200 rounded-full text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none accent-primary"
                                id="memove_privacy_reject" checked="">
                        </label>

                        <div class="col-span-2 mt-2 text-sm text-red-500">
                            @error('memove_privacy')
                                {{ $message }}
                            @enderror
                        </div>
                    </div>
                </div>
                <div>
                    <p class="mt-4 text-sm font-light text-[#465686] tracking-wide">@lang('messages.memove_partners_privacy')</p>
                    <div class="grid gap-2 mt-4 sm:grid-cols-2">
                        <label for="memove_partner_privacy_accept"
                            class="flex w-full p-3 text-sm bg-white border border-gray-200 rounded-lg cursor-pointer focus:border-blue-500 focus:ring-blue-500">
                            <span class="text-sm text-gray-500">@lang('messages.accept')</span>
                            <input type="radio" wire:model="memove_partner_privacy" value="accepted"
                                class="shrink-0 ms-auto mt-0.5 border-gray-200 rounded-full text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none accent-primary"
                                id="memove_partner_privacy_accept">
                        </label>

                        <label for="memove_partner_privacy_reject"
                            class="flex w-full p-3 text-sm bg-white border border-gray-200 rounded-lg cursor-pointer focus:border-blue-500 focus:ring-blue-500">
                            <span class="text-sm text-gray-500">@lang('messages.non_accept')</span>
                            <input type="radio" wire:model="memove_partner_privacy" value="rejected"
                                class="shrink-0 ms-auto mt-0.5 border-gray-200 rounded-full text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none accent-primary"
                                id="memove_partner_privacy_reject" checked="">
                        </label>

                        <div class="col-span-2 mt-2 text-sm text-red-500">
                            @error('memove_partner_privacy')
                                {{ $message }}
                            @enderror
                        </div>
                    </div>
                </div>
            </div>


            <div class="flex items-center justify-center max-w-lg gap-5 mx-auto mt-10">
                <button wire:click="respondContract('reject')" wire:loading.attr="disabled"
                    wire:target="respondContract('reject')" type="button"
                    class="w-full p-3 text-center text-white transition-all duration-300 bg-red-500 rounded-lg ">
                    <span wire:loading.remove wire:target="respondContract('reject')">{{ __('messages.reject') }}</span>
                    <div wire:loading wire:target="respondContract('reject')">
                        <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                        </div>
                    </div>
                </button>

                <button wire:click="respondContract('accept')" wire:loading.attr="disabled"
                    wire:target="respondContract('accept')" type="button"
                    class="w-full p-3 text-center text-white transition-all duration-300 rounded-lg bg-primary hover:bg-primaryDark">
                    <span wire:loading.remove wire:target="respondContract('accept')">{{ __('messages.accept') }}</span>
                    <div wire:loading wire:target="respondContract('accept')">
                        <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                            <div class="dot-spinner__dot"></div>
                        </div>
                    </div>
                </button>
            </div>
        @else
            <div class="flex flex-col items-center w-full max-w-lg mx-auto mt-5 space-y-3">
                @if ($this->contract->status === 'active')
                    <div class="w-full p-3 text-center text-white rounded-lg bg-emerald-500">
                        {{ __('messages.contract_accepted') }}
                    </div>
                @elseif ($this->contract->status === 'rejected')
                    <div class="w-full p-3 text-center text-white bg-red-500 rounded-lg">
                        {{ __('messages.contract_rejected') }}
                    </div>
                @elseif ($this->contract->status === 'expired')
                    <div class="w-full p-3 text-center text-white bg-red-500 rounded-lg">
                        {{ __('messages.expired') }}
                    </div>
                @endif

            </div>
            <div class="flex items-center justify-center mt-5 text-center">
                <a href="{{ route('login') }}"
                    class="px-4 py-2 text-sm text-white rounded-md bg-primary hover:bg-primaryDark">
                    @lang('messages.platform_login')
                </a>
            </div>
        @endif




    </div>


</main>
