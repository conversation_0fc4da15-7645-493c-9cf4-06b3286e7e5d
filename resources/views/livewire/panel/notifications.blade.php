<div wire:ignore.self id="notification-modal" class="fixed top-0 bottom-0 left-0 right-0 z-40 hidden modal bg-black/30 backdrop-blur-sm">

    <div
        class="modal-body absolute top-0 bottom-0 right-0 h-full md:w-96 max-w-[80%] w-full bg-[#F2EEF7] md:p-8 p-6 [box-shadow:-10px_0px_10px_rgba(0,0,0,0.1)] transition-all duration-300">

        <!-- modal close button -->
        <button class="modal-close p-3 absolute top-0 -left-10 bg-[#F2EEF7] [box-shadow:-5px_0px_10px_rgba(0,0,0,0.1)]">
            <img class="size-5" src="{{ asset('assets/images/cross.svg') }}" alt="cross">
        </button>

        <h2 class="text-lg font-medium text-center">
            {{ __('messages.notifications') }}
        </h2>

        <!-- filters -->
        <div class="flex flex-wrap items-center gap-3 mt-4">
            <button wire:click="filterNotifications('today')"
                class="filter-button p-2 rounded-lg text-sm flex-grow @if ($filter == 'today') bg-primary text-white @else bg-[#2B2D31]/10 text-[#20222C] @endif">
                {{ __('messages.today') }}
            </button>
            <button wire:click="filterNotifications"
                class="flex-grow p-2 text-sm rounded-lg filter-button @if ($filter == null) bg-primary text-white @else bg-[#2B2D31]/10 text-[#20222C] @endif">
                {{ __('messages.all') }}
            </button>
            <button wire:click="filterNotifications('yesterday')"
                class="filter-button @if ($filter == 'yesterday') bg-primary text-white @else bg-[#2B2D31]/10 text-[#20222C] @endif p-2 rounded-lg text-sm flex-grow">
                {{ __('messages.yesterday') }}
            </button>
        </div>

        <!-- notifications -->
        <div class="max-h-[60vh] overflow-y-scroll mt-10 notification-container">
            <div class="w-full">
                @forelse ($notifications as $notification)
                    <div wire:key="notification-{{ $notification->id }}"
                        class="relative flex justify-start gap-2 p-3 mt-4 bg-white rounded-md">
                        <img class="md:size-10 size-7" src="{{ asset('assets/images/notification-icon.svg') }}"
                            alt="notification">
                        <div class="ms-3">
                            <h3 class="text-xs font-medium md:text-sm">
                                {{ $notification->event_message }}
                            </h3>
                            <p class="text-xs text-[#898989]">
                                @lang('messages.device') {{ $notification->imei }}
                            </p>
                        </div>

                        <span
                            class="text-[10px] absolute top-1 right-2 text-[#9C9C9C]">{{ $notification->created_at->diffForHumans() }}</span>
                    </div>
                @empty
                    <p class="text-center">@lang('messages.no_notification_found')</p>
                @endforelse
            </div>

        </div>
        <a href="{{ route('events') }}"
            class="flex-grow block p-2 px-4 mx-auto mt-4 text-sm text-white rounded-lg w-fit filter-button bg-primary"
            data-filter="All">
           @lang('messages.view_all')
        </a>
    </div>
</div>
