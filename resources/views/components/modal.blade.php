@props(['name'])
<div x-data="{
    show: false,
    name: '{{ $name }}'
}" x-cloak x-init="() => { show = false }" x-transition
    @open-modal.window="show = ($event.detail.name === name)" @close-modal.window="show = false;"
    @keydown.escape.window="show = false;">
    <div>
        <!-- backdrop -->
        <div x-show="show"
            class="fixed z-[99999] top-0 left-0 rigt-0 w-full h-full bottom-0 backdrop-blur-sm bg-black/20 grid place-items-center min-h-screen overflow-y-auto py-10">
            <!-- dialog -->
            <div x-show="show" x-transition.opacity.300
                class="relative w-11/12 p-5 bg-white shadow rounded-xl sm:w-1/2 sm:h-fit h-fit">
             

                <!-- Modal Body -->
                {{ $body ?? '' }}


            </div>
        </div>
    </div>
</div>
