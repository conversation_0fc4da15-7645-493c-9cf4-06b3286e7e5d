@php
    $user = auth()->user();
@endphp
<div x-data="sidebar()" :class="{ 'md:min-w-60': isOpen }" class="min-w-14">
    <aside :class="{ 'min-w-60 px-5': isOpen }" id="sidebar"
        class="h-[90vh] z-30 [box-shadow:3px_0px_10px_rgba(0,0,0,0.07)] p-3 transition-all duration-300 w-fit fixed top-0 left-0 bottom-0 bg-white">

        <!-- sidebar toggle button -->
        <button @click="toggleSidebar()" :class="{ 'rotate-180': isOpen }" id="toggle-button"
            class="absolute top-0 transition-all duration-300 outline-none -right-7">
            <img class="size-14" src="{{ asset('assets/images/charvon-right.svg') }}" alt="charvon-right">
        </button>



        <!-- logo -->
        <img :class="{ 'hidden': isOpen }" class="mx-auto mt-5 favicon size-10"
            src="{{ asset('assets/images/favicon.png') }}" alt="favicon">
        <img :class="{ 'block': isOpen, 'hidden': !isOpen }" class="hidden w-40 mx-auto mt-5 logo"
            src="{{ asset('assets/images/logo.svg') }}" alt="logo">

        <!-- pages links -->
        <ul :class="{ 'items-start': isOpen }"
            class="flex flex-col items-center h-full mx-auto mt-8 space-y-3 transition-all duration-300 menu 2xl:justify-center 2xl:mt-0 group-active:w-96">
            <!-- dashboard -->
            @if ($user->role != 'dealer' && $user->role != 'warehouse_operator')
                <a href="{{ route('dashboard') }}" :class="{ 'px-5 w-full': isOpen }"
                    class="p-2 flex-shrink-0 rounded-full @yield('dashboard_active', 'text-[#64748B] hover:bg-primary/10 hover:text-primary ') flex items-center translate-all duration-300">
                    <svg class="size-5" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M14.1663 11.6667V16.6667M11.6663 14.1667H16.6663M4.99967 8.33334H6.66634C7.58682 8.33334 8.33301 7.58715 8.33301 6.66668V5.00001C8.33301 4.07954 7.58682 3.33334 6.66634 3.33334H4.99967C4.0792 3.33334 3.33301 4.07954 3.33301 5.00001V6.66668C3.33301 7.58715 4.0792 8.33334 4.99967 8.33334ZM13.333 8.33334H14.9997C15.9201 8.33334 16.6663 7.58715 16.6663 6.66668V5.00001C16.6663 4.07954 15.9201 3.33334 14.9997 3.33334H13.333C12.4125 3.33334 11.6663 4.07954 11.6663 5.00001V6.66668C11.6663 7.58715 12.4125 8.33334 13.333 8.33334ZM4.99967 16.6667H6.66634C7.58682 16.6667 8.33301 15.9205 8.33301 15V13.3333C8.33301 12.4129 7.58682 11.6667 6.66634 11.6667H4.99967C4.0792 11.6667 3.33301 12.4129 3.33301 13.3333V15C3.33301 15.9205 4.0792 16.6667 4.99967 16.6667Z"
                            stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>

                    <span :class="{ 'block': isOpen, 'hidden': !isOpen }"
                        class="transition-opacity duration-300 link-text ms-2 ">@lang('messages.dashboard')</span>
                </a>
            @endif
            <!-- devices -->
            @if ($user->role != 'operator')
                <a href="{{ route('devices') }}" :class="{ 'px-5 w-full': isOpen }"
                    class="p-2 flex-shrink-0 rounded-full @yield('devices_active', 'text-[#64748B] hover:bg-primary/10 hover:text-primary ') flex items-center translate-all duration-300">
                    <svg class="size-5" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M7.49998 15H12.5M15 2.5C15.442 2.5 15.8659 2.67559 16.1785 2.98816C16.4911 3.30072 16.6666 3.72464 16.6666 4.16667V15.8333C16.6666 16.2754 16.4911 16.6993 16.1785 17.0118C15.8659 17.3244 15.442 17.5 15 17.5H4.99998C4.55795 17.5 4.13403 17.3244 3.82147 17.0118C3.50891 16.6993 3.33331 16.2754 3.33331 15.8333V4.16667C3.33331 3.72464 3.50891 3.30072 3.82147 2.98816C4.13403 2.67559 4.55795 2.5 4.99998 2.5H15Z"
                            stroke="currentColor" stroke-width="1.66667" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>


                    <span :class="{ 'block': isOpen, 'hidden': !isOpen }"
                        class="transition-opacity duration-300 link-text ms-2 ">@lang('messages.devices')</span>
                </a>
            @endif

            <!-- clients -->
            @if ($user->role != 'operator' && $user->role != 'client' && $user->role != 'technician' && $user->role != 'warehouse_operator')
                <a href="{{ route('clients') }}" :class="{ 'px-5 w-full': isOpen }"
                    class="p-2 flex-shrink-0 rounded-full @yield('clients_active', 'text-[#64748B] hover:bg-primary/10 hover:text-primary ') flex items-center translate-all duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="none">
                        <path
                            d="M6.8125 11.5C8.96875 11.5 10.6875 9.75 10.6875 7.625C10.6875 5.5 8.9375 3.75 6.8125 3.75C4.6875 3.75 2.9375 5.46875 2.9375 7.625C2.9375 9.78125 4.6875 11.5 6.8125 11.5ZM6.8125 5.125C8.1875 5.125 9.28125 6.25 9.28125 7.59375C9.28125 8.9375 8.15625 10.0625 6.8125 10.0625C5.4375 10.0625 4.34375 8.9375 4.34375 7.59375C4.34375 6.25 5.4375 5.125 6.8125 5.125ZM6.8125 12.4688C4.5625 12.4688 2.40625 13.375 0.75 15.0625C0.46875 15.3438 0.46875 15.7813 0.75 16.0625C0.875 16.1875 1.0625 16.2812 1.25 16.2812C1.4375 16.2812 1.625 16.2188 1.75 16.0625C3.125 14.6562 4.9375 13.875 6.8125 13.875C8.65625 13.875 10.4688 14.6562 11.875 16.0625C12.1562 16.3438 12.5938 16.3438 12.875 16.0625C13.1562 15.7813 13.1562 15.3438 12.875 15.0625C11.2188 13.4062 9.0625 12.4688 6.8125 12.4688ZM14.7812 11.5C16.0312 11.5 17.0625 10.4688 17.0625 9.21875C17.0625 7.96875 16.0312 6.9375 14.7812 6.9375C13.5312 6.9375 12.5 7.96875 12.5 9.21875C12.4687 10.4688 13.5 11.5 14.7812 11.5ZM14.7812 8.3125C15.2812 8.3125 15.6562 8.71875 15.6562 9.1875C15.6562 9.6875 15.25 10.0625 14.7812 10.0625C14.3125 10.0625 13.9062 9.65625 13.9062 9.1875C13.875 8.71875 14.2812 8.3125 14.7812 8.3125ZM19.2187 14.25C17.5625 12.7187 15.2812 12.0938 13 12.6875C12.625 12.7812 12.4062 13.1562 12.5 13.5312C12.5937 13.9062 13 14.125 13.3437 14.0312C15.1562 13.5625 16.9688 14.0312 18.25 15.25C18.375 15.375 18.5625 15.4375 18.7188 15.4375C18.9062 15.4375 19.0937 15.375 19.2187 15.2188C19.5312 14.9688 19.5 14.5312 19.2187 14.25Z"
                            fill="currentColor" />
                    </svg>


                    <span :class="{ 'block': isOpen, 'hidden': !isOpen }"
                        class="transition-opacity duration-300 link-text ms-2 ">@lang('messages.clients')</span>
                </a>
            @endif

            @if ($user->role == 'admin' || $user->role == 'technician' || $user->role == 'warehouse_operator')
                <!-- dealers -->
                <a href="{{ route('dealers') }}" :class="{ 'px-5 w-full': isOpen }"
                    class="p-2 flex-shrink-0 rounded-full @yield('dealers_active', 'text-[#64748B] hover:bg-primary/10 hover:text-primary ') flex items-center translate-all duration-300">
                    <svg class="size-5" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M16.25 10.4767L10 16.6667L3.75003 10.4767C3.33778 10.0755 3.01306 9.59334 2.79632 9.06052C2.57957 8.52771 2.4755 7.95579 2.49064 7.38077C2.50579 6.80576 2.63983 6.24011 2.88432 5.71944C3.12882 5.19878 3.47847 4.73437 3.91127 4.35548C4.34406 3.97658 4.85061 3.6914 5.39904 3.5179C5.94746 3.34439 6.52587 3.28631 7.09783 3.34733C7.6698 3.40834 8.22294 3.58713 8.72242 3.87242C9.2219 4.15771 9.6569 4.54333 10 5.00499C10.3446 4.54668 10.7801 4.16443 11.2793 3.88217C11.7784 3.5999 12.3304 3.4237 12.9008 3.36459C13.4712 3.30549 14.0476 3.36474 14.594 3.53865C15.1404 3.71257 15.645 3.99739 16.0763 4.3753C16.5076 4.75321 16.8562 5.21606 17.1004 5.7349C17.3445 6.25374 17.479 6.8174 17.4953 7.39058C17.5116 7.96377 17.4094 8.53416 17.1951 9.06604C16.9809 9.59792 16.6591 10.0798 16.25 10.4817"
                            stroke="currentColor" stroke-width="1.66667" stroke-linecap="round"
                            stroke-linejoin="round" />
                        <path
                            d="M10 5L7.25583 7.74417C7.0996 7.90044 7.01184 8.11236 7.01184 8.33333C7.01184 8.5543 7.0996 8.76623 7.25583 8.9225L7.70833 9.375C8.28333 9.95 9.21666 9.95 9.79166 9.375L10.625 8.54167C10.8711 8.29524 11.1634 8.09975 11.4851 7.96637C11.8069 7.83299 12.1517 7.76433 12.5 7.76433C12.8483 7.76433 13.1931 7.83299 13.5149 7.96637C13.8366 8.09975 14.1289 8.29524 14.375 8.54167L16.25 10.4167M10.4167 12.9167L12.0833 14.5833M12.5 10.8333L14.1667 12.5"
                            stroke="currentColor" stroke-width="1.66667" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>


                    <span :class="{ 'block': isOpen, 'hidden': !isOpen }"
                        class="transition-opacity duration-300 link-text ms-2 ">@lang('messages.dealers')</span>
                </a>
            @endif


            <!-- contracts -->
            @if ($user->role != 'operator' && $user->role != 'technician' && $user->role != 'warehouse_operator')
                <a href="{{ route('contracts') }}" :class="{ 'px-5 w-full': isOpen }"
                    class="p-2 flex-shrink-0 rounded-full @yield('contracts_active', 'text-[#64748B] hover:bg-primary/10 hover:text-primary ') flex items-center translate-all duration-300">
                    <svg class="size-5" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_96_582)">
                            <mask id="path-1-inside-1_96_582" fill="white">
                                <path
                                    d="M5.40001 8.4H4.80001V9.6H5.40001V8.4ZM12.6 9.6H13.2V8.4H12.6V9.6ZM5.40001 4.8H4.80001V6H5.40001V4.8ZM7.80001 6H8.40001V4.8H7.80001V6ZM12.6 0.6L13.0248 0.1752L12.8484 0H12.6V0.6ZM16.2 4.2H16.8V3.9516L16.6248 3.7752L16.2 4.2ZM9.00001 13.8L9.26881 14.3364L9.31681 14.3124L9.36001 14.28L9.00001 13.8ZM5.40001 9.6H12.6V8.4H5.40001V9.6ZM5.40001 6H7.80001V4.8H5.40001V6ZM15 16.8H3.00001V18H15V16.8ZM2.40001 16.2V1.8H1.20001V16.2H2.40001ZM3.00001 1.2H12.6V0H3.00001V1.2ZM15.6 4.2V16.2H16.8V4.2H15.6ZM12.1752 1.0248L15.7752 4.6248L16.6248 3.7752L13.0248 0.1752L12.1752 1.0248ZM3.00001 16.8C2.84088 16.8 2.68827 16.7368 2.57575 16.6243C2.46323 16.5117 2.40001 16.3591 2.40001 16.2H1.20001C1.20001 16.6774 1.38965 17.1352 1.72722 17.4728C2.06479 17.8104 2.52262 18 3.00001 18V16.8ZM15 18C15.4774 18 15.9352 17.8104 16.2728 17.4728C16.6104 17.1352 16.8 16.6774 16.8 16.2H15.6C15.6 16.3591 15.5368 16.5117 15.4243 16.6243C15.3118 16.7368 15.1591 16.8 15 16.8V18ZM2.40001 1.8C2.40001 1.64087 2.46323 1.48826 2.57575 1.37574C2.68827 1.26321 2.84088 1.2 3.00001 1.2V0C2.52262 0 2.06479 0.189642 1.72722 0.527208C1.38965 0.864773 1.20001 1.32261 1.20001 1.8H2.40001ZM6.56881 13.9896C6.70201 13.59 7.08121 13.2192 7.56481 13.0896C8.01721 12.9684 8.59921 13.0476 9.17521 13.6248L10.0248 12.7752C9.16081 11.9112 8.14201 11.6916 7.25401 11.9304C6.39841 12.1608 5.69761 12.81 5.43001 13.6104L6.56881 13.9896ZM9.17521 13.6248C9.20883 13.6577 9.24049 13.6926 9.27001 13.7292L10.218 12.9924C10.158 12.9166 10.0935 12.8444 10.0248 12.7764L9.17521 13.6248ZM9.27001 13.7292C9.36361 13.8492 9.34201 13.8876 9.34561 13.8612C9.34801 13.8444 9.35281 13.872 9.28081 13.9368C9.17707 14.0235 9.05941 14.0921 8.93281 14.1396C8.78064 14.2006 8.62202 14.2441 8.46001 14.2692C8.36091 14.2857 8.26012 14.2898 8.16001 14.2812C8.13961 14.2764 8.18161 14.2812 8.24401 14.3256C8.32025 14.3849 8.37629 14.4663 8.40443 14.5587C8.43258 14.6511 8.43145 14.7499 8.40121 14.8416C8.39452 14.8615 8.38524 14.8805 8.37361 14.898C8.37121 14.9004 8.39161 14.874 8.46001 14.8176C8.59681 14.7072 8.84881 14.5464 9.26881 14.3376L8.73121 13.2636C8.28001 13.4892 7.93841 13.696 7.70641 13.884C7.5886 13.9764 7.48367 14.0842 7.39441 14.2044C7.28082 14.3557 7.22163 14.5409 7.22641 14.73C7.2316 14.8407 7.26183 14.9487 7.31483 15.046C7.36784 15.1433 7.44223 15.2272 7.53241 15.2916C7.66801 15.3924 7.81801 15.4356 7.92841 15.4584C8.15281 15.5028 8.40841 15.4908 8.64001 15.456C9.09601 15.3864 9.68641 15.1896 10.0884 14.8248C10.2972 14.634 10.4952 14.3604 10.5372 14.0016C10.5804 13.6344 10.4484 13.29 10.2168 12.9936L9.27001 13.7292ZM9.36001 14.28C9.55225 14.1335 9.76084 14.0099 9.98161 13.9116L9.50521 12.81C9.21721 12.934 8.92881 13.104 8.64001 13.32L9.36001 14.28ZM9.98161 13.9116C10.7616 13.5744 11.4588 13.752 12.1728 14.0916C12.3512 14.1788 12.5288 14.2708 12.7056 14.3676C12.8784 14.4612 13.0608 14.5608 13.2336 14.6496C13.5648 14.8164 13.9752 15 14.4 15V13.8C14.2872 13.8 14.1024 13.7436 13.776 13.5792C13.6224 13.5 13.4616 13.4112 13.2768 13.3116C13.0968 13.2144 12.8976 13.1076 12.6888 13.0092C11.8512 12.6096 10.7556 12.27 9.50521 12.81L9.98161 13.9116Z" />
                            </mask>
                            <path
                                d="M5.40001 8.4H4.80001V9.6H5.40001V8.4ZM12.6 9.6H13.2V8.4H12.6V9.6ZM5.40001 4.8H4.80001V6H5.40001V4.8ZM7.80001 6H8.40001V4.8H7.80001V6ZM12.6 0.6L13.0248 0.1752L12.8484 0H12.6V0.6ZM16.2 4.2H16.8V3.9516L16.6248 3.7752L16.2 4.2ZM9.00001 13.8L9.26881 14.3364L9.31681 14.3124L9.36001 14.28L9.00001 13.8ZM5.40001 9.6H12.6V8.4H5.40001V9.6ZM5.40001 6H7.80001V4.8H5.40001V6ZM15 16.8H3.00001V18H15V16.8ZM2.40001 16.2V1.8H1.20001V16.2H2.40001ZM3.00001 1.2H12.6V0H3.00001V1.2ZM15.6 4.2V16.2H16.8V4.2H15.6ZM12.1752 1.0248L15.7752 4.6248L16.6248 3.7752L13.0248 0.1752L12.1752 1.0248ZM3.00001 16.8C2.84088 16.8 2.68827 16.7368 2.57575 16.6243C2.46323 16.5117 2.40001 16.3591 2.40001 16.2H1.20001C1.20001 16.6774 1.38965 17.1352 1.72722 17.4728C2.06479 17.8104 2.52262 18 3.00001 18V16.8ZM15 18C15.4774 18 15.9352 17.8104 16.2728 17.4728C16.6104 17.1352 16.8 16.6774 16.8 16.2H15.6C15.6 16.3591 15.5368 16.5117 15.4243 16.6243C15.3118 16.7368 15.1591 16.8 15 16.8V18ZM2.40001 1.8C2.40001 1.64087 2.46323 1.48826 2.57575 1.37574C2.68827 1.26321 2.84088 1.2 3.00001 1.2V0C2.52262 0 2.06479 0.189642 1.72722 0.527208C1.38965 0.864773 1.20001 1.32261 1.20001 1.8H2.40001ZM6.56881 13.9896C6.70201 13.59 7.08121 13.2192 7.56481 13.0896C8.01721 12.9684 8.59921 13.0476 9.17521 13.6248L10.0248 12.7752C9.16081 11.9112 8.14201 11.6916 7.25401 11.9304C6.39841 12.1608 5.69761 12.81 5.43001 13.6104L6.56881 13.9896ZM9.17521 13.6248C9.20883 13.6577 9.24049 13.6926 9.27001 13.7292L10.218 12.9924C10.158 12.9166 10.0935 12.8444 10.0248 12.7764L9.17521 13.6248ZM9.27001 13.7292C9.36361 13.8492 9.34201 13.8876 9.34561 13.8612C9.34801 13.8444 9.35281 13.872 9.28081 13.9368C9.17707 14.0235 9.05941 14.0921 8.93281 14.1396C8.78064 14.2006 8.62202 14.2441 8.46001 14.2692C8.36091 14.2857 8.26012 14.2898 8.16001 14.2812C8.13961 14.2764 8.18161 14.2812 8.24401 14.3256C8.32025 14.3849 8.37629 14.4663 8.40443 14.5587C8.43258 14.6511 8.43145 14.7499 8.40121 14.8416C8.39452 14.8615 8.38524 14.8805 8.37361 14.898C8.37121 14.9004 8.39161 14.874 8.46001 14.8176C8.59681 14.7072 8.84881 14.5464 9.26881 14.3376L8.73121 13.2636C8.28001 13.4892 7.93841 13.696 7.70641 13.884C7.5886 13.9764 7.48367 14.0842 7.39441 14.2044C7.28082 14.3557 7.22163 14.5409 7.22641 14.73C7.2316 14.8407 7.26183 14.9487 7.31483 15.046C7.36784 15.1433 7.44223 15.2272 7.53241 15.2916C7.66801 15.3924 7.81801 15.4356 7.92841 15.4584C8.15281 15.5028 8.40841 15.4908 8.64001 15.456C9.09601 15.3864 9.68641 15.1896 10.0884 14.8248C10.2972 14.634 10.4952 14.3604 10.5372 14.0016C10.5804 13.6344 10.4484 13.29 10.2168 12.9936L9.27001 13.7292ZM9.36001 14.28C9.55225 14.1335 9.76084 14.0099 9.98161 13.9116L9.50521 12.81C9.21721 12.934 8.92881 13.104 8.64001 13.32L9.36001 14.28ZM9.98161 13.9116C10.7616 13.5744 11.4588 13.752 12.1728 14.0916C12.3512 14.1788 12.5288 14.2708 12.7056 14.3676C12.8784 14.4612 13.0608 14.5608 13.2336 14.6496C13.5648 14.8164 13.9752 15 14.4 15V13.8C14.2872 13.8 14.1024 13.7436 13.776 13.5792C13.6224 13.5 13.4616 13.4112 13.2768 13.3116C13.0968 13.2144 12.8976 13.1076 12.6888 13.0092C11.8512 12.6096 10.7556 12.27 9.50521 12.81L9.98161 13.9116Z"
                                fill="currentColor" />
                            <path
                                d="M6.40001 9.6V8.4H4.40001V9.6H6.40001ZM11.6 8.4V9.6H13.6V8.4H11.6ZM6.40001 6V4.8H4.40001V6H6.40001ZM6.80001 4.8V6H8.80001V4.8H6.80001ZM8.89291 12.4929L8.46811 12.9177L9.88232 14.3319L10.3071 13.9071L8.89291 12.4929ZM2.00001 16.8V18H4.00001V16.8H2.00001ZM16 18V16.8H14V18H16ZM2.40001 0.8H1.20001V2.8H2.40001V0.8ZM1.20001 17.2H2.40001V15.2H1.20001V17.2ZM2.00001 0V1.2H4.00001V0H2.00001ZM15.6 17.2H16.8V15.2H15.6V17.2ZM10.8995 13.5147L10.4231 12.4131L8.58737 13.2069L9.06377 14.3085L10.8995 13.5147ZM4.80001 8.4V6.4H2.80001V8.4H4.80001ZM4.80001 9.6H2.80001V11.6H4.80001V9.6ZM13.2 9.6V11.6H15.2V9.6H13.2ZM13.2 8.4H15.2V6.4H13.2V8.4ZM4.80001 4.8V2.8H2.80001V4.8H4.80001ZM4.80001 6H2.80001V8H4.80001V6ZM8.40001 6V8H10.4V6H8.40001ZM8.40001 4.8H10.4V2.8H8.40001V4.8ZM12.6 0.6H10.6V5.42843L14.0142 2.01421L12.6 0.6ZM13.0248 0.1752L14.439 -1.23902L14.4342 -1.24383L13.0248 0.1752ZM12.8484 0L14.2578 -1.41903L13.6728 -2H12.8484V0ZM16.2 4.2L14.7858 2.78579L11.3716 6.2H16.2V4.2ZM16.8 3.9516H18.8V3.12717L18.219 2.54222L16.8 3.9516ZM16.6248 3.7752L18.0439 2.36581L18.039 2.36099L16.6248 3.7752ZM9.17521 13.6248L7.75953 15.0375L7.76778 15.0458L7.77612 15.054L9.17521 13.6248ZM9.00001 13.8L10.6 12.6L7.21196 14.696L9.00001 13.8ZM9.26881 14.3364L7.48076 15.2324L8.37598 17.0189L10.1632 16.1253L9.26881 14.3364ZM9.31681 14.3124L10.2112 16.1013L10.3725 16.0206L10.5168 15.9124L9.31681 14.3124ZM9.36001 14.28L10.56 15.88L10.5661 15.8755L10.5721 15.8709L9.36001 14.28ZM3.00001 18V20V18ZM15 18V20V18ZM1.20001 1.8H-0.799988H1.20001ZM1.20001 16.2H-0.799988H1.20001ZM3.00001 1.2V-0.8V1.2ZM12.6 1.2V3.2H14.6V1.2H12.6ZM3.00001 0V-2V0ZM15.6 4.2V2.2H13.6V4.2H15.6ZM12.1752 1.0248L10.761 -0.389414L9.34679 1.0248L10.761 2.43901L12.1752 1.0248ZM15.7752 4.6248L14.361 6.03901L15.7752 7.45323L17.1894 6.03901L15.7752 4.6248ZM6.56881 13.9896L5.93696 15.8872L7.83392 16.5188L8.46618 14.6221L6.56881 13.9896ZM7.56481 13.0896L7.04725 11.1577L7.0471 11.1578L7.56481 13.0896ZM10.0248 12.7752L11.439 14.1894L12.8532 12.7752L11.439 11.361L10.0248 12.7752ZM7.25401 11.9304L6.73463 9.99902L6.73397 9.9992L7.25401 11.9304ZM5.43001 13.6104L3.53322 12.9762L2.89828 14.8753L4.79816 15.508L5.43001 13.6104ZM10.218 12.9924L11.4453 14.5715L13.0382 13.3336L11.7865 11.7515L10.218 12.9924ZM10.0248 12.7764L11.4323 11.3555L10.0191 9.95567L8.6116 11.3612L10.0248 12.7764ZM9.34561 13.8612L7.36567 13.5784L7.36395 13.591L9.34561 13.8612ZM9.28081 13.9368L10.5635 15.4713L10.5916 15.4478L10.6187 15.4234L9.28081 13.9368ZM8.93281 14.1396L8.23009 12.2671L8.20927 12.2749L8.18862 12.2832L8.93281 14.1396ZM8.46001 14.2692L8.1536 12.2928L8.14214 12.2946L8.1307 12.2965L8.46001 14.2692ZM8.16001 14.2812L7.70193 16.228L7.84395 16.2615L7.98932 16.2739L8.16001 14.2812ZM8.24401 14.3256L9.47165 12.7467L9.43812 12.7206L9.40352 12.696L8.24401 14.3256ZM8.40121 14.8416L10.2972 15.4782L10.3006 15.4678L8.40121 14.8416ZM8.37361 14.898L9.78783 16.3122L9.92908 16.171L10.0396 16.0046L8.37361 14.898ZM8.46001 14.8176L7.20397 13.2612L7.19578 13.2678L7.18765 13.2745L8.46001 14.8176ZM9.26881 14.3376L10.1591 16.1285L11.9549 15.2357L11.0573 13.4424L9.26881 14.3376ZM8.73121 13.2636L10.5197 12.3684L9.62484 10.5807L7.83679 11.4747L8.73121 13.2636ZM7.70641 13.884L8.94082 15.4576L8.95328 15.4478L8.96558 15.4379L7.70641 13.884ZM7.39441 14.2044L8.9938 15.4053L9.00025 15.3966L7.39441 14.2044ZM7.22641 14.73L5.22705 14.7806L5.2276 14.8021L5.2286 14.8236L7.22641 14.73ZM7.53241 15.2916L8.72558 13.6865L8.70998 13.6749L8.69416 13.6636L7.53241 15.2916ZM7.92841 15.4584L7.52391 17.4171L7.53205 17.4188L7.54022 17.4204L7.92841 15.4584ZM8.64001 15.456L8.9372 17.4338L8.94178 17.4331L8.64001 15.456ZM10.0884 14.8248L11.4324 16.3059L11.4376 16.3012L10.0884 14.8248ZM10.5372 14.0016L8.55091 13.7679L8.55078 13.7691L10.5372 14.0016ZM10.2168 12.9936L11.7928 11.7622L10.5648 10.1906L8.98976 11.4142L10.2168 12.9936ZM9.98161 13.9116L9.18798 12.0758L9.17797 12.0801L9.16801 12.0846L9.98161 13.9116ZM9.50521 12.81L10.2961 14.647L10.2982 14.6461L9.50521 12.81ZM8.64001 13.32L7.44214 11.7184L5.83841 12.9179L7.04001 14.52L8.64001 13.32ZM12.1728 14.0916L13.0511 12.2948L13.0415 12.2901L13.0319 12.2855L12.1728 14.0916ZM12.7056 14.3676L11.7451 16.1219L11.753 16.1262L12.7056 14.3676ZM13.2336 14.6496L12.3195 16.4285L12.3267 16.4322L12.334 16.4359L13.2336 14.6496ZM14.4 15V17H16.4V15H14.4ZM14.4 13.8H16.4V11.8H14.4V13.8ZM13.776 13.5792L12.8594 15.3568L12.8679 15.3612L12.8763 15.3654L13.776 13.5792ZM13.2768 13.3116L12.3265 15.0714L12.3279 15.0722L13.2768 13.3116ZM12.6888 13.0092L11.8276 14.8143L11.8362 14.8184L12.6888 13.0092ZM5.40001 6.4H4.80001V10.4H5.40001V6.4ZM2.80001 8.4V9.6H6.80001V8.4H2.80001ZM4.80001 11.6H5.40001V7.6H4.80001V11.6ZM12.6 11.6H13.2V7.6H12.6V11.6ZM15.2 9.6V8.4H11.2V9.6H15.2ZM13.2 6.4H12.6V10.4H13.2V6.4ZM5.40001 2.8H4.80001V6.8H5.40001V2.8ZM2.80001 4.8V6H6.80001V4.8H2.80001ZM4.80001 8H5.40001V4H4.80001V8ZM7.80001 8H8.40001V4H7.80001V8ZM10.4 6V4.8H6.40001V6H10.4ZM8.40001 2.8H7.80001V6.8H8.40001V2.8ZM14.0142 2.01421L14.439 1.58941L11.6106 -1.23901L11.1858 -0.814214L14.0142 2.01421ZM14.4342 -1.24383L14.2578 -1.41903L11.439 1.41903L11.6154 1.59423L14.4342 -1.24383ZM12.8484 -2H12.6V2H12.8484V-2ZM10.6 0V0.6H14.6V0H10.6ZM16.2 6.2H16.8V2.2H16.2V6.2ZM18.8 4.2V3.9516H14.8V4.2H18.8ZM18.219 2.54222L18.0438 2.36582L15.2058 5.18458L15.381 5.36098L18.219 2.54222ZM15.2106 2.36099L14.7858 2.78579L17.6142 5.61421L18.039 5.18941L15.2106 2.36099ZM7.21196 14.696L7.48076 15.2324L11.0569 13.4404L10.7881 12.904L7.21196 14.696ZM10.1632 16.1253L10.2112 16.1013L8.4224 12.5235L8.3744 12.5475L10.1632 16.1253ZM10.5168 15.9124L10.56 15.88L8.16001 12.68L8.11681 12.7124L10.5168 15.9124ZM10.96 13.08L10.6 12.6L7.40001 15L7.76001 15.48L10.96 13.08ZM5.40001 11.6H12.6V7.6H5.40001V11.6ZM12.6 6.4H5.40001V10.4H12.6V6.4ZM5.40001 8H7.80001V4H5.40001V8ZM7.80001 2.8H5.40001V6.8H7.80001V2.8ZM15 14.8H3.00001V18.8H15V14.8ZM3.00001 20H15V16H3.00001V20ZM4.40001 16.2V1.8H0.400012V16.2H4.40001ZM-0.799988 1.8V16.2H3.20001V1.8H-0.799988ZM3.00001 3.2H12.6V-0.8H3.00001V3.2ZM14.6 1.2V0H10.6V1.2H14.6ZM12.6 -2H3.00001V2H12.6V-2ZM13.6 4.2V16.2H17.6V4.2H13.6ZM18.8 16.2V4.2H14.8V16.2H18.8ZM16.8 2.2H15.6V6.2H16.8V2.2ZM10.761 2.43901L14.361 6.03901L17.1894 3.21059L13.5894 -0.389414L10.761 2.43901ZM17.1894 6.03901L18.039 5.18941L15.2106 2.36099L14.361 3.21059L17.1894 6.03901ZM18.039 2.36099L14.439 -1.23901L11.6106 1.58941L15.2106 5.18941L18.039 2.36099ZM11.6106 -1.23901L10.761 -0.389414L13.5894 2.43901L14.439 1.58941L11.6106 -1.23901ZM3.00001 14.8C3.37131 14.8 3.72741 14.9475 3.98996 15.2101L1.16153 18.0385C1.64913 18.5261 2.31045 18.8 3.00001 18.8V14.8ZM3.98996 15.2101C4.25251 15.4726 4.40001 15.8287 4.40001 16.2H0.400012C0.400012 16.8896 0.67394 17.5509 1.16153 18.0385L3.98996 15.2101ZM-0.799988 16.2C-0.799988 17.2078 -0.399632 18.1744 0.313007 18.887L3.14143 16.0586C3.17894 16.0961 3.20001 16.147 3.20001 16.2H-0.799988ZM0.313007 18.887C1.02565 19.5996 1.99219 20 3.00001 20V16C3.05305 16 3.10393 16.0211 3.14143 16.0586L0.313007 18.887ZM15 20C16.0078 20 16.9744 19.5996 17.687 18.887L14.8586 16.0586C14.8961 16.0211 14.947 16 15 16V20ZM17.687 18.887C18.3997 18.1744 18.8 17.2078 18.8 16.2H14.8C14.8 16.147 14.8211 16.0961 14.8586 16.0586L17.687 18.887ZM13.6 16.2C13.6 15.8287 13.7475 15.4726 14.0101 15.2101L16.8385 18.0385C17.3261 17.5509 17.6 16.8896 17.6 16.2H13.6ZM14.0101 15.2101C14.2726 14.9475 14.6287 14.8 15 14.8V18.8C15.6896 18.8 16.3509 18.5261 16.8385 18.0385L14.0101 15.2101ZM4.40001 1.8C4.40001 2.1713 4.25251 2.5274 3.98996 2.78995L1.16153 -0.0384776C0.673941 0.449116 0.400012 1.11044 0.400012 1.8H4.40001ZM3.98996 2.78995C3.72741 3.0525 3.37132 3.2 3.00001 3.2V-0.8C2.31045 -0.8 1.64913 -0.526072 1.16153 -0.0384776L3.98996 2.78995ZM3.00001 -2C1.99219 -2 1.02564 -1.59964 0.313007 -0.887006L3.14143 1.94142C3.10393 1.97893 3.05306 2 3.00001 2V-2ZM0.313007 -0.887006C-0.399632 -0.174368 -0.799988 0.792177 -0.799988 1.8H3.20001C3.20001 1.85304 3.17894 1.90391 3.14143 1.94142L0.313007 -0.887006ZM8.46618 14.6221C8.41464 14.7767 8.34029 14.8633 8.29277 14.9072C8.24104 14.955 8.17263 14.9973 8.08252 15.0214L7.0471 11.1578C5.94253 11.4538 5.0261 12.2932 4.67145 13.3571L8.46618 14.6221ZM8.08237 15.0215C7.99044 15.0461 7.88272 15.0488 7.78397 15.0211C7.68472 14.9933 7.68172 14.9596 7.75953 15.0375L10.5909 12.2121C9.53359 11.1525 8.22263 10.8428 7.04725 11.1577L8.08237 15.0215ZM10.5894 15.039L11.439 14.1894L8.6106 11.361L7.761 12.2106L10.5894 15.039ZM11.439 11.361C10.0968 10.0187 8.34996 9.56462 6.73463 9.99902L7.7734 13.8618C7.87902 13.8334 7.98931 13.8315 8.10759 13.8647C8.22448 13.8976 8.40072 13.9795 8.6106 14.1894L11.439 11.361ZM6.73397 9.9992C5.26199 10.3956 4.02332 11.5103 3.53322 12.9762L7.32681 14.2446C7.37191 14.1097 7.53484 13.926 7.77406 13.8616L6.73397 9.9992ZM4.79816 15.508L5.93696 15.8872L7.20067 12.092L6.06187 11.7128L4.79816 15.508ZM7.77612 15.054C7.75371 15.032 7.73261 15.0088 7.71292 14.9844L10.8271 12.474C10.7484 12.3763 10.664 12.2834 10.5743 12.1956L7.77612 15.054ZM10.4973 15.3083L11.4453 14.5715L8.99069 11.4133L8.04269 12.1501L10.4973 15.3083ZM11.7865 11.7515C11.6765 11.6125 11.5582 11.4802 11.4323 11.3555L8.61733 14.1973C8.62879 14.2087 8.63954 14.2207 8.64953 14.2333L11.7865 11.7515ZM8.6116 11.3612L7.762 12.2096L10.5884 15.04L11.438 14.1916L8.6116 11.3612ZM7.69301 14.9593C7.6368 14.8872 7.51857 14.718 7.43501 14.4543C7.408 14.3691 7.33615 14.1279 7.34646 13.8048C7.3471 13.7847 7.35008 13.5983 7.40815 13.372C7.42521 13.3055 7.46145 13.1771 7.53213 13.0252C7.58157 12.919 7.75703 12.5531 8.16426 12.2551C8.39602 12.0855 8.72904 11.9201 9.14965 11.8787C9.57673 11.8367 9.95422 11.9366 10.2443 12.0826C10.7491 12.3366 10.9907 12.7252 11.0561 12.8332C11.2181 13.1006 11.2753 13.3432 11.2846 13.3801C11.3064 13.4664 11.3179 13.5368 11.3238 13.5767C11.336 13.6597 11.3406 13.727 11.3426 13.7663C11.3504 13.9202 11.3388 14.0465 11.3273 14.1314L7.36395 13.591C7.35148 13.6825 7.33979 13.8134 7.34776 13.9698C7.34979 14.0097 7.35437 14.0775 7.36667 14.1608C7.37258 14.2009 7.38416 14.2715 7.40596 14.3579C7.41528 14.3949 7.47255 14.6375 7.63449 14.905C7.69993 15.0131 7.94158 15.4017 8.44634 15.6557C8.73647 15.8017 9.11396 15.9015 9.54106 15.8595C9.96167 15.8182 10.2947 15.6527 10.5265 15.4831C10.9337 15.1851 11.1092 14.8192 11.1586 14.713C11.2293 14.5611 11.2656 14.4326 11.2826 14.3661C11.3408 14.1396 11.3438 13.9529 11.3444 13.9324C11.3548 13.6058 11.2819 13.3524 11.2481 13.2459C11.1511 12.9396 10.9968 12.6912 10.847 12.4991L7.69301 14.9593ZM11.3255 14.144C11.3151 14.2166 11.2965 14.3184 11.2604 14.4373C11.2515 14.4667 11.2344 14.5208 11.2084 14.587C11.1968 14.6166 11.1709 14.6809 11.1313 14.7593C11.1121 14.7973 11.0747 14.8684 11.0193 14.9529C10.9899 14.9977 10.8446 15.2241 10.578 15.4324C10.4439 15.5372 10.1302 15.7566 9.66224 15.8313C9.09624 15.9218 8.50435 15.766 8.05002 15.3791C7.6736 15.0585 7.51953 14.6891 7.46015 14.5204C7.39311 14.3299 7.36949 14.1699 7.35959 14.083C7.32713 13.7984 7.36743 13.5692 7.36854 13.5618C7.38597 13.4447 7.41031 13.3555 7.41709 13.3306C7.47831 13.1061 7.56501 12.9442 7.61209 12.8632C7.73202 12.6569 7.86222 12.5228 7.94288 12.4502L10.6187 15.4234C10.7714 15.286 10.9352 15.1057 11.0701 14.8736C11.1247 14.7797 11.2143 14.61 11.2762 14.383C11.283 14.3579 11.3074 14.2684 11.3249 14.1512C11.326 14.1437 11.3663 13.9145 11.3338 13.6298C11.3239 13.5429 11.3003 13.3828 11.2333 13.1924C11.1739 13.0237 11.0198 12.6542 10.6434 12.3337C10.189 11.9468 9.59716 11.791 9.03116 11.8814C8.56315 11.9562 8.2495 12.1756 8.11537 12.2804C7.84877 12.4887 7.70346 12.715 7.67405 12.7599C7.61868 12.8444 7.58126 12.9156 7.56207 12.9535C7.52236 13.032 7.49644 13.0964 7.48477 13.1261C7.45871 13.1925 7.44153 13.2469 7.43253 13.2766C7.39602 13.397 7.37668 13.5016 7.36571 13.5784L11.3255 14.144ZM7.99809 12.4023C8.06726 12.3445 8.1457 12.2988 8.23009 12.2671L9.63553 16.0121C9.97312 15.8854 10.2869 15.7025 10.5635 15.4713L7.99809 12.4023ZM8.18862 12.2832C8.17735 12.2877 8.1656 12.291 8.1536 12.2928L8.76643 16.2456C9.07844 16.1972 9.38393 16.1135 9.67701 15.996L8.18862 12.2832ZM8.1307 12.2965C8.19677 12.2855 8.26396 12.2828 8.3307 12.2885L7.98932 16.2739C8.25628 16.2968 8.52505 16.286 8.78932 16.2419L8.1307 12.2965ZM8.61809 12.3344C8.62745 12.3366 8.7147 12.3564 8.82256 12.3945C8.84213 12.4015 8.87399 12.4131 8.91273 12.4289C8.94026 12.4402 9.02373 12.4747 9.12473 12.5307C9.1709 12.5563 9.27895 12.618 9.40252 12.7167C9.43957 12.7462 9.87247 13.0678 10.0647 13.686C10.1889 14.0857 10.2115 14.618 9.95711 15.1467C9.72436 15.6306 9.36395 15.8925 9.14572 16.017C8.76007 16.237 8.4037 16.265 8.3449 16.2707C8.13517 16.2907 7.96905 16.2712 7.94617 16.2688C7.88833 16.2627 7.84263 16.2553 7.81834 16.2512C7.76779 16.2425 7.72822 16.2337 7.70867 16.2293C7.58326 16.2005 7.49006 16.1661 7.44351 16.148C7.31855 16.0992 7.19758 16.0356 7.0845 15.9552L9.40352 12.696C9.22805 12.5712 9.05487 12.483 8.89842 12.4219C8.83612 12.3976 8.73234 12.3602 8.60284 12.3305C8.58278 12.3259 8.54281 12.317 8.49194 12.3083C8.46751 12.3041 8.42168 12.2967 8.36374 12.2906C8.34075 12.2882 8.17457 12.2687 7.96478 12.2888C7.90596 12.2944 7.54958 12.3225 7.16391 12.5424C6.94567 12.6669 6.58526 12.9289 6.35251 13.4127C6.09814 13.9415 6.12072 14.4738 6.24496 14.8734C6.43715 15.4917 6.87006 15.8132 6.90713 15.8428C7.03072 15.9415 7.13879 16.0033 7.18499 16.0289C7.28605 16.0849 7.36961 16.1194 7.39725 16.1307C7.43611 16.1466 7.46811 16.1583 7.48786 16.1653C7.59707 16.2039 7.68748 16.2246 7.70193 16.228L8.61809 12.3344ZM7.01638 15.9045C6.76681 15.7104 6.58338 15.4439 6.49125 15.1415L10.3176 13.9758C10.1692 13.4886 9.8737 13.0593 9.47165 12.7467L7.01638 15.9045ZM6.49125 15.1415C6.39911 14.8391 6.4028 14.5156 6.50178 14.2154L10.3006 15.4678C10.4601 14.9842 10.466 14.463 10.3176 13.9758L6.49125 15.1415ZM6.50522 14.205C6.55427 14.0589 6.62232 13.9199 6.70762 13.7914L10.0396 16.0046C10.1482 15.8411 10.2348 15.6641 10.2972 15.4782L6.50522 14.205ZM6.9594 13.4838C6.97509 13.4681 7.00413 13.4395 7.04223 13.4056C7.06142 13.3884 7.16188 13.2976 7.30731 13.206C7.35834 13.1738 7.57077 13.0389 7.87875 12.9603C8.10658 12.9021 9.28096 12.6829 10.024 13.7687C10.3117 14.1891 10.364 14.6041 10.3721 14.8248C10.3805 15.0553 10.347 15.2375 10.3249 15.3361C10.281 15.5317 10.217 15.6755 10.1937 15.7268C10.1392 15.8464 10.0847 15.9337 10.0698 15.9576C10.0465 15.9949 10.0265 16.024 10.0148 16.0408C9.9911 16.0749 9.97125 16.1011 9.96115 16.1143C9.92355 16.1633 9.89228 16.1992 9.88106 16.212C9.84937 16.2482 9.82273 16.2758 9.80448 16.2941C9.7663 16.3322 9.74006 16.3543 9.73238 16.3607L7.18765 13.2745C7.11157 13.3373 7.04093 13.4008 6.97604 13.4656C6.94444 13.4972 6.90889 13.5345 6.8719 13.5767C6.85804 13.5925 6.82503 13.6306 6.78651 13.6808C6.77618 13.6943 6.75615 13.7207 6.7323 13.755C6.72054 13.7719 6.70053 13.8011 6.67719 13.8385C6.66223 13.8625 6.60767 13.9498 6.55318 14.0695C6.52985 14.1207 6.46586 14.2646 6.42196 14.4602C6.39985 14.5588 6.36631 14.741 6.37477 14.9715C6.38287 15.1922 6.43516 15.6072 6.72289 16.0276C7.4659 17.1134 8.64028 16.8942 8.86812 16.836C9.17612 16.7574 9.38855 16.6224 9.4396 16.5903C9.58506 16.4986 9.68556 16.4078 9.7048 16.3906C9.74296 16.3566 9.77206 16.328 9.78783 16.3122L6.9594 13.4838ZM9.71605 16.374C9.69387 16.3919 9.71117 16.3754 9.79233 16.3264C9.86941 16.2798 9.98811 16.2135 10.1591 16.1285L8.37848 12.5467C7.90061 12.7843 7.50284 13.02 7.20397 13.2612L9.71605 16.374ZM11.0573 13.4424L10.5197 12.3684L6.94276 14.1588L7.48036 15.2328L11.0573 13.4424ZM7.83679 11.4747C7.33152 11.7274 6.84275 12.0096 6.44725 12.3301L8.96558 15.4379C9.03408 15.3824 9.2285 15.251 9.62564 15.0525L7.83679 11.4747ZM6.472 12.3104C6.21393 12.5128 5.98409 12.7489 5.78858 13.0122L9.00025 15.3966C8.98325 15.4195 8.96326 15.44 8.94082 15.4576L6.472 12.3104ZM5.79505 13.0035C5.41102 13.515 5.21088 14.1412 5.22705 14.7806L9.22577 14.6794C9.23238 14.9406 9.15063 15.1963 8.99377 15.4053L5.79505 13.0035ZM5.2286 14.8236C5.24794 15.2366 5.36078 15.6398 5.55861 16.0029L9.07105 14.0891C9.16287 14.2576 9.21525 14.4447 9.22422 14.6364L5.2286 14.8236ZM5.55861 16.0029C5.75644 16.366 6.0341 16.6794 6.37067 16.9196L8.69416 13.6636C8.85037 13.7751 8.97924 13.9206 9.07105 14.0891L5.55861 16.0029ZM6.33924 16.8967C6.81314 17.249 7.29089 17.3689 7.52391 17.4171L8.33292 13.4997C8.34514 13.5023 8.52288 13.5358 8.72558 13.6865L6.33924 16.8967ZM7.54022 17.4204C8.08029 17.5272 8.59067 17.4859 8.93719 17.4338L8.34283 13.4782C8.32138 13.4814 8.30229 13.4837 8.28598 13.4851C8.26956 13.4866 8.25814 13.487 8.25174 13.4871C8.24517 13.4872 8.24681 13.4869 8.25596 13.4876C8.2649 13.4883 8.28578 13.4903 8.31661 13.4964L7.54022 17.4204ZM8.94178 17.4331C9.61006 17.3311 10.6306 17.0335 11.4324 16.3059L8.74439 13.3437C8.76282 13.327 8.73484 13.3563 8.63191 13.3981C8.5342 13.4378 8.42528 13.4656 8.33824 13.4789L8.94178 17.4331ZM11.4376 16.3012C11.8669 15.9089 12.4077 15.225 12.5236 14.2341L8.55078 13.7691C8.58277 13.4958 8.72751 13.3591 8.73927 13.3484L11.4376 16.3012ZM12.5235 14.2353C12.644 13.211 12.2552 12.3541 11.7928 11.7622L8.64086 14.225C8.64072 14.2248 8.61047 14.1865 8.58337 14.1055C8.55418 14.0183 8.53539 13.8999 8.55091 13.7679L12.5235 14.2353ZM8.98976 11.4142L8.04296 12.1498L10.4971 15.3086L11.4439 14.573L8.98976 11.4142ZM10.5721 15.8709C10.6411 15.8183 10.716 15.7739 10.7952 15.7386L9.16801 12.0846C8.80572 12.2459 8.46341 12.4488 8.14795 12.6891L10.5721 15.8709ZM8.7143 10.973C8.26219 11.1677 7.83848 11.422 7.44214 11.7184L9.83788 14.9216C10.0191 14.786 10.1722 14.7003 10.2961 14.647L8.7143 10.973ZM7.04001 14.52L7.76001 15.48L10.96 13.08L10.24 12.12L7.04001 14.52ZM10.7752 15.7474C10.809 15.7328 10.8233 15.7313 10.8236 15.7313C10.8244 15.7312 10.8327 15.7297 10.8562 15.7326C10.9162 15.7401 11.049 15.7718 11.3138 15.8977L13.0319 12.2855C12.107 11.8456 10.7469 11.4019 9.18798 12.0758L10.7752 15.7474ZM11.2945 15.8884C11.4448 15.9619 11.595 16.0397 11.7451 16.1219L13.6661 12.6133C13.4626 12.5019 13.2576 12.3957 13.0511 12.2948L11.2945 15.8884ZM11.753 16.1262C11.9154 16.2141 12.1222 16.3271 12.3195 16.4285L14.1477 12.8707C13.9994 12.7945 13.8414 12.7083 13.6582 12.609L11.753 16.1262ZM12.334 16.4359C12.6715 16.6058 13.4576 17 14.4 17V13C14.4708 13 14.4859 13.0142 14.4232 12.993C14.3597 12.9715 14.2729 12.9337 14.1332 12.8633L12.334 16.4359ZM16.4 15V13.8H12.4V15H16.4ZM14.4 11.8C14.5356 11.8 14.6403 11.8166 14.7028 11.8294C14.7661 11.8424 14.806 11.8557 14.8165 11.8593C14.8327 11.8648 14.7936 11.8524 14.6757 11.793L12.8763 15.3654C13.1967 15.5268 13.7637 15.8 14.4 15.8V11.8ZM14.6926 11.8016C14.5605 11.7335 14.4358 11.6643 14.2257 11.551L12.3279 15.0722C12.4874 15.1581 12.6843 15.2665 12.8594 15.3568L14.6926 11.8016ZM14.2271 11.5518C14.048 11.4551 13.8053 11.3244 13.5414 11.2L11.8362 14.8184C11.9899 14.8908 12.1456 14.9737 12.3265 15.0714L14.2271 11.5518ZM13.55 11.2041C12.5054 10.7057 10.745 10.096 8.71227 10.9739L10.2982 14.6461C10.5652 14.5308 10.7877 14.5146 11.002 14.5416C11.2413 14.5717 11.5072 14.6614 11.8276 14.8143L13.55 11.2041Z"
                                fill="currentColor" mask="url(#path-1-inside-1_96_582)" />
                        </g>
                        <defs>
                            <clipPath id="clip0_96_582">
                                <rect width="18" height="18" fill="white" />
                            </clipPath>
                        </defs>
                    </svg>

                    <span :class="{ 'block': isOpen, 'hidden': !isOpen }"
                        class="transition-opacity duration-300 link-text ms-2 ">@lang('messages.contracts')</span>
                </a>
            @endif


            <!-- ddts -->
            @if ($user->role != 'operator' && $user->role != 'client')
                <a href="{{ route('ddts') }}" :class="{ 'px-5 w-full': isOpen }"
                    class="p-2 flex-shrink-0 rounded-full @yield('ddts_active', 'text-[#64748B] hover:bg-primary/10 hover:text-primary ') flex items-center translate-all duration-300">
                    <svg class="size-6" viewBox="0 0 24 24" id="Layer_4" version="1.1" xml:space="preserve"
                        xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <style type="text/css">
                                .st0 {
                                    fill: currentColor;
                                }
                            </style>
                            <path class="st0"
                                d="M19.47,14.08H18.9v-0.11c0-0.404-0.147-0.779-0.4-1.064V7.39c0-0.133-0.053-0.26-0.146-0.354l-0.51-0.51 l-2.861-2.861l-0.509-0.509C14.38,3.063,14.253,3.01,14.12,3.01H7.99c-1.373,0-2.49,1.121-2.49,2.5V9c0,0.276,0.224,0.5,0.5,0.5 S6.5,9.276,6.5,9V5.51c0-0.827,0.669-1.5,1.49-1.5h5.663C13.633,4.08,13.62,4.154,13.62,4.23V6.38c0,0.832,0.678,1.51,1.51,1.51 h2.149c0.076,0,0.15-0.013,0.221-0.033v4.603h-2.57c-0.744,0-1.35,0.678-1.35,1.51v0.11h-0.57c-0.832,0-1.51,0.682-1.51,1.52v3.87 c0,0.167,0.033,0.324,0.083,0.475c0.005,0.014,0.003,0.031,0.008,0.046H7.99c-0.821,0-1.49-0.673-1.49-1.5v-7.771 c0-0.276-0.224-0.5-0.5-0.5s-0.5,0.224-0.5,0.5v7.771c0,1.379,1.117,2.5,2.49,2.5h5.02h6.46c0.839,0,1.521-0.682,1.521-1.521V15.6 C20.99,14.762,20.309,14.08,19.47,14.08z M15.13,6.89c-0.281,0-0.51-0.229-0.51-0.51V4.717l2.173,2.173H15.13z M14.58,13.97 c0-0.276,0.16-0.51,0.35-0.51h2.62c0.045,0,0.104,0.024,0.155,0.057c0.118,0.092,0.195,0.267,0.195,0.452v0.11h-3.32V13.97z M19.99,19.47c0,0.282-0.238,0.521-0.521,0.521h-6.46c-0.281,0-0.51-0.233-0.51-0.521V15.6c0-0.286,0.229-0.52,0.51-0.52h1.07h4.32 h1.069c0.282,0,0.521,0.238,0.521,0.52V19.47z">
                            </path>
                            <path class="st0"
                                d="M17.292,17.035h-2.104c-0.276,0-0.5,0.224-0.5,0.5s0.224,0.5,0.5,0.5h2.104c0.276,0,0.5-0.224,0.5-0.5 S17.568,17.035,17.292,17.035z">
                            </path>
                        </g>
                    </svg>

                    <span :class="{ 'block': isOpen, 'hidden': !isOpen }"
                        class="transition-opacity duration-300 link-text ms-2 ">DDT</span>
                </a>
            @endif


            @if ($user->role == 'admin')
                <!-- admins -->
                <a href="{{ route('admins') }}" :class="{ 'px-5 w-full': isOpen }"
                    class="p-2 flex-shrink-0 rounded-full @yield('admins_active', 'text-[#64748B] hover:bg-primary/10 hover:text-primary ') flex items-center translate-all duration-300">

                    <svg class="size-5" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M10 19.1667C5.36919 18.1375 1.66669 13.7683 1.66669 9.16668V4.16668L10 0.833344L18.3334 4.16668V9.16668C18.3334 13.77 14.6309 18.1375 10 19.1667ZM3.33335 5.00001V9.16668C3.38098 11.0934 4.05866 12.9515 5.26267 14.4566C6.46668 15.9616 8.13069 17.0306 10 17.5C11.8693 17.0306 13.5334 15.9616 14.7374 14.4566C15.9414 12.9515 16.6191 11.0934 16.6667 9.16668V5.00001L10 2.50001L3.33335 5.00001Z"
                            fill="currentColor" />
                        <path
                            d="M10 9.16667C11.1506 9.16667 12.0834 8.23393 12.0834 7.08333C12.0834 5.93274 11.1506 5 10 5C8.84943 5 7.91669 5.93274 7.91669 7.08333C7.91669 8.23393 8.84943 9.16667 10 9.16667Z"
                            fill="currentColor" />
                        <path
                            d="M5.83331 12.5C6.24392 13.2486 6.84614 13.8746 7.57829 14.3139C8.31044 14.7532 9.14621 14.99 9.99998 15C10.8537 14.99 11.6895 14.7532 12.4217 14.3139C13.1538 13.8746 13.756 13.2486 14.1666 12.5C14.1458 10.92 11.3816 10 9.99998 10C8.61081 10 5.85415 10.92 5.83331 12.5Z"
                            fill="currentColor" />
                    </svg>


                    <span :class="{ 'block': isOpen, 'hidden': !isOpen }"
                        class="transition-opacity duration-300 link-text ms-2 ">@lang('messages.admin')</span>

                </a>
            @endif

            @if ($user->role == 'operator')
                <!-- operations-center -->
                <a href="{{ route('operations-center') }}" :class="{ 'px-5 w-full': isOpen }"
                    class="p-2 flex-shrink-0 rounded-full @yield('device_logs_active', 'text-[#64748B] hover:bg-primary/10 hover:text-primary ') flex items-center translate-all duration-300">
                    <svg class="size-5" fill="currentColor" viewBox="0 0 1024 1024"
                        xmlns="http://www.w3.org/2000/svg" class="icon" stroke="currentColor">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656zM340 683v77c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-77c-10.1 3.3-20.8 5-32 5s-21.9-1.8-32-5zm64-198V264c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v221c10.1-3.3 20.8-5 32-5s21.9 1.8 32 5zm-64 198c10.1 3.3 20.8 5 32 5s21.9-1.8 32-5c41.8-13.5 72-52.7 72-99s-30.2-85.5-72-99c-10.1-3.3-20.8-5-32-5s-21.9 1.8-32 5c-41.8 13.5-72 52.7-72 99s30.2 85.5 72 99zm.1-115.7c.3-.6.7-1.2 1-1.8v-.1l1.2-1.8c.1-.2.2-.3.3-.5.3-.5.7-.9 1-1.4.1-.1.2-.3.3-.4.5-.6.9-1.1 1.4-1.6l.3-.3 1.2-1.2.4-.4c.5-.5 1-.9 1.6-1.4.6-.5 1.1-.9 1.7-1.3.2-.1.3-.2.5-.3.5-.3.9-.7 1.4-1 .1-.1.3-.2.4-.3.6-.4 1.2-.7 1.9-1.1.1-.1.3-.1.4-.2.5-.3 1-.5 1.6-.8l.6-.3c.7-.3 1.3-.6 2-.8.7-.3 1.4-.5 2.1-.7.2-.1.4-.1.6-.2.6-.2 1.1-.3 1.7-.4.2 0 .3-.1.5-.1.7-.2 1.5-.3 2.2-.4.2 0 .3 0 .5-.1.6-.1 1.2-.1 1.8-.2h.6c.8 0 1.5-.1 2.3-.1s1.5 0 2.3.1h.6c.6 0 1.2.1 1.8.2.2 0 .3 0 .5.1.7.1 1.5.2 2.2.4.2 0 .3.1.5.1.6.1 1.2.3 1.7.4.2.1.4.1.6.2.7.2 1.4.4 2.1.7.7.2 1.3.5 2 .8l.6.3c.5.2 1.1.5 1.6.8.1.1.3.1.4.2.6.3 1.3.7 1.9 1.1.1.1.3.2.4.3.5.3 1 .6 1.4 1 .2.1.3.2.5.3.6.4 1.2.9 1.7 1.3s1.1.9 1.6 1.4l.4.4 1.2 1.2.3.3c.5.5 1 1.1 1.4 1.6.1.1.2.3.3.4.4.4.7.9 1 1.4.1.2.2.3.3.5l1.2 1.8s0 .1.1.1a36.18 36.18 0 0 1 5.1 18.5c0 6-1.5 11.7-4.1 16.7-.3.6-.7 1.2-1 1.8 0 0 0 .1-.1.1l-1.2 1.8c-.1.2-.2.3-.3.5-.3.5-.7.9-1 1.4-.1.1-.2.3-.3.4-.5.6-.9 1.1-1.4 1.6l-.3.3-1.2 1.2-.4.4c-.5.5-1 .9-1.6 1.4-.6.5-1.1.9-1.7 1.3-.2.1-.3.2-.5.3-.5.3-.9.7-1.4 1-.1.1-.3.2-.4.3-.6.4-1.2.7-1.9 1.1-.1.1-.3.1-.4.2-.5.3-1 .5-1.6.8l-.6.3c-.7.3-1.3.6-2 .8-.7.3-1.4.5-2.1.7-.2.1-.4.1-.6.2-.6.2-1.1.3-1.7.4-.2 0-.3.1-.5.1-.7.2-1.5.3-2.2.4-.2 0-.3 0-.5.1-.6.1-1.2.1-1.8.2h-.6c-.8 0-1.5.1-2.3.1s-1.5 0-2.3-.1h-.6c-.6 0-1.2-.1-1.8-.2-.2 0-.3 0-.5-.1-.7-.1-1.5-.2-2.2-.4-.2 0-.3-.1-.5-.1-.6-.1-1.2-.3-1.7-.4-.2-.1-.4-.1-.6-.2-.7-.2-1.4-.4-2.1-.7-.7-.2-1.3-.5-2-.8l-.6-.3c-.5-.2-1.1-.5-1.6-.8-.1-.1-.3-.1-.4-.2-.6-.3-1.3-.7-1.9-1.1-.1-.1-.3-.2-.4-.3-.5-.3-1-.6-1.4-1-.2-.1-.3-.2-.5-.3-.6-.4-1.2-.9-1.7-1.3s-1.1-.9-1.6-1.4l-.4-.4-1.2-1.2-.3-.3c-.5-.5-1-1.1-1.4-1.6-.1-.1-.2-.3-.3-.4-.4-.4-.7-.9-1-1.4-.1-.2-.2-.3-.3-.5l-1.2-1.8v-.1c-.4-.6-.7-1.2-1-1.8-2.6-5-4.1-10.7-4.1-16.7s1.5-11.7 4.1-16.7zM620 539v221c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V539c-10.1 3.3-20.8 5-32 5s-21.9-1.8-32-5zm64-198v-77c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v77c10.1-3.3 20.8-5 32-5s21.9 1.8 32 5zm-64 198c10.1 3.3 20.8 5 32 5s21.9-1.8 32-5c41.8-13.5 72-52.7 72-99s-30.2-85.5-72-99c-10.1-3.3-20.8-5-32-5s-21.9 1.8-32 5c-41.8 13.5-72 52.7-72 99s30.2 85.5 72 99zm.1-115.7c.3-.6.7-1.2 1-1.8v-.1l1.2-1.8c.1-.2.2-.3.3-.5.3-.5.7-.9 1-1.4.1-.1.2-.3.3-.4.5-.6.9-1.1 1.4-1.6l.3-.3 1.2-1.2.4-.4c.5-.5 1-.9 1.6-1.4.6-.5 1.1-.9 1.7-1.3.2-.1.3-.2.5-.3.5-.3.9-.7 1.4-1 .1-.1.3-.2.4-.3.6-.4 1.2-.7 1.9-1.1.1-.1.3-.1.4-.2.5-.3 1-.5 1.6-.8l.6-.3c.7-.3 1.3-.6 2-.8.7-.3 1.4-.5 2.1-.7.2-.1.4-.1.6-.2.6-.2 1.1-.3 1.7-.4.2 0 .3-.1.5-.1.7-.2 1.5-.3 2.2-.4.2 0 .3 0 .5-.1.6-.1 1.2-.1 1.8-.2h.6c.8 0 1.5-.1 2.3-.1s1.5 0 2.3.1h.6c.6 0 1.2.1 1.8.2.2 0 .3 0 .5.1.7.1 1.5.2 2.2.4.2 0 .3.1.5.1.6.1 1.2.3 1.7.4.2.1.4.1.6.2.7.2 1.4.4 2.1.7.7.2 1.3.5 2 .8l.6.3c.5.2 1.1.5 1.6.8.1.1.3.1.4.2.6.3 1.3.7 1.9 1.1.1.1.3.2.4.3.5.3 1 .6 1.4 1 .2.1.3.2.5.3.6.4 1.2.9 1.7 1.3s1.1.9 1.6 1.4l.4.4 1.2 1.2.3.3c.5.5 1 1.1 1.4 1.6.1.1.2.3.3.4.4.4.7.9 1 1.4.1.2.2.3.3.5l1.2 1.8v.1a36.18 36.18 0 0 1 5.1 18.5c0 6-1.5 11.7-4.1 16.7-.3.6-.7 1.2-1 1.8v.1l-1.2 1.8c-.1.2-.2.3-.3.5-.3.5-.7.9-1 1.4-.1.1-.2.3-.3.4-.5.6-.9 1.1-1.4 1.6l-.3.3-1.2 1.2-.4.4c-.5.5-1 .9-1.6 1.4-.6.5-1.1.9-1.7 1.3-.2.1-.3.2-.5.3-.5.3-.9.7-1.4 1-.1.1-.3.2-.4.3-.6.4-1.2.7-1.9 1.1-.1.1-.3.1-.4.2-.5.3-1 .5-1.6.8l-.6.3c-.7.3-1.3.6-2 .8-.7.3-1.4.5-2.1.7-.2.1-.4.1-.6.2-.6.2-1.1.3-1.7.4-.2 0-.3.1-.5.1-.7.2-1.5.3-2.2.4-.2 0-.3 0-.5.1-.6.1-1.2.1-1.8.2h-.6c-.8 0-1.5.1-2.3.1s-1.5 0-2.3-.1h-.6c-.6 0-1.2-.1-1.8-.2-.2 0-.3 0-.5-.1-.7-.1-1.5-.2-2.2-.4-.2 0-.3-.1-.5-.1-.6-.1-1.2-.3-1.7-.4-.2-.1-.4-.1-.6-.2-.7-.2-1.4-.4-2.1-.7-.7-.2-1.3-.5-2-.8l-.6-.3c-.5-.2-1.1-.5-1.6-.8-.1-.1-.3-.1-.4-.2-.6-.3-1.3-.7-1.9-1.1-.1-.1-.3-.2-.4-.3-.5-.3-1-.6-1.4-1-.2-.1-.3-.2-.5-.3-.6-.4-1.2-.9-1.7-1.3s-1.1-.9-1.6-1.4l-.4-.4-1.2-1.2-.3-.3c-.5-.5-1-1.1-1.4-1.6-.1-.1-.2-.3-.3-.4-.4-.4-.7-.9-1-1.4-.1-.2-.2-.3-.3-.5l-1.2-1.8v-.1c-.4-.6-.7-1.2-1-1.8-2.6-5-4.1-10.7-4.1-16.7s1.5-11.7 4.1-16.7z">
                            </path>
                        </g>
                    </svg>


                    <span :class="{ 'block': isOpen, 'hidden': !isOpen }"
                        class="transition-opacity duration-300 link-text ms-2 ">{{ __('messages.operations_center') }}</span>

                </a>
            @endif
            @if ($user->role == 'admin')
                <a href="{{ route('device-command') }}" :class="{ 'px-5 w-full': isOpen }"
                    class="p-2 flex-shrink-0 rounded-full @yield('device_command_active', 'text-[#64748B] hover:bg-primary/10 hover:text-primary ') flex items-center translate-all duration-300">
                    <svg class="size-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M9.3 17.85C9.3 19.5897 7.8897 21 6.15 21C4.4103 21 3 19.5897 3 17.85C3 16.1103 4.4103 14.7 6.15 14.7H17.85C19.5897 14.7 21 16.1103 21 17.85C21 19.5897 19.5897 21 17.85 21C16.1103 21 14.7 19.5897 14.7 17.85V6.15C14.7 4.4103 16.1103 3 17.85 3C19.5897 3 21 4.4103 21 6.15C21 7.8897 19.5897 9.3 17.85 9.3H6.15C4.4103 9.3 3 7.8897 3 6.15C3 4.4103 4.4103 3 6.15 3C7.8897 3 9.3 4.4103 9.3 6.15V17.85Z"
                                stroke="currentColor" stroke-width="2"></path>
                        </g>
                    </svg>


                    <span :class="{ 'block': isOpen, 'hidden': !isOpen }"
                        class="transition-opacity duration-300 link-text ms-2 ">{{ __('messages.command') }}</span>

                </a>
            @endif




            <!-- profile -->
            <!-- <a href="{{ url('/') }}pages/profile.php" class="flex items-center w-full p-2 duration-300 rounded-full translate-all ">
                <svg class="flex-shrink-0 size-8" viewBox="0 0 41 41" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M20.5 3.83333C11.0649 3.83333 3.41669 11.295 3.41669 20.5C3.41669 29.705 11.0649 37.1667 20.5 37.1667C29.9351 37.1667 37.5834 29.705 37.5834 20.5C37.5834 11.295 29.9351 3.83333 20.5 3.83333ZM14.5209 16.3333C14.5209 15.5673 14.6755 14.8087 14.976 14.101C15.2765 13.3933 15.7169 12.7502 16.2721 12.2085C16.8273 11.6669 17.4865 11.2372 18.2119 10.944C18.9373 10.6509 19.7148 10.5 20.5 10.5C21.2852 10.5 22.0627 10.6509 22.7881 10.944C23.5136 11.2372 24.1727 11.6669 24.7279 12.2085C25.2831 12.7502 25.7236 13.3933 26.024 14.101C26.3245 14.8087 26.4792 15.5673 26.4792 16.3333C26.4792 17.8804 25.8492 19.3642 24.7279 20.4581C23.6066 21.5521 22.0858 22.1667 20.5 22.1667C18.9142 22.1667 17.3934 21.5521 16.2721 20.4581C15.1508 19.3642 14.5209 17.8804 14.5209 16.3333ZM31.1908 28.8067C29.9116 30.3756 28.2858 31.6425 26.4346 32.5128C24.5835 33.3832 22.5549 33.8346 20.5 33.8333C18.4451 33.8346 16.4165 33.3832 14.5654 32.5128C12.7143 31.6425 11.0885 30.3756 9.80927 28.8067C12.5785 26.8683 16.3573 25.5 20.5 25.5C24.6427 25.5 28.4216 26.8683 31.1908 28.8067Z"
                        fill="#450099" />
                </svg>


                <div class="flex-shrink-0 text-sm transition-opacity duration-300 link-text ms-2">

                    <div class="text-[#64748B]">Welcome back 👋</div>
                    <div class="font-medium">Andrea :)</div>
                </div>

            </a> -->

        </ul>

    </aside>
</div>

<script>
    function sidebar() {
        return {
            isOpen: JSON.parse(localStorage.getItem('isOpen')) ??
                true, // Initialize from local storage or default to true
            toggleSidebar() {
                this.isOpen = !this.isOpen; // Toggle state
                localStorage.setItem('isOpen', JSON.stringify(this.isOpen)); // Update local storage
            }
        };
    }
</script>
