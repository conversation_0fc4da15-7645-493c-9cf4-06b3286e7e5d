<!-- bottom navigation -->
@use('App\Models\Device')
<nav class="h-[10vh]">
    <div
        class="z-50 fixed bottom-0 w-full left-0 right-0 bg-primary text-white md:px-8 px-6 py-5 flex items-center justify-between [box-shadow:0px_-5px_10px_rgba(0,0,0,0.1)] gap-4">
        <!-- logo -->
        <img class="md:h-9 h-7" src="{{ asset('assets/images/logo-white.svg') }}" alt="logo">

        <!-- search bar -->
        @if (Route::is('dashboard'))
            <div class="relative">
                <label
                    class="flex items-center flex-shrink-0 max-w-xs px-3 py-2 overflow-hidden bg-white rounded-full md:w-full md:size-auto size-8">
                    <img id="search-icon" class="cursor-pointer size-4" src="{{ asset('assets/images/search.svg') }}"
                        alt="search">
                    <input type="search" id="searchInput" name="search" placeholder="{{ __('messages.search') }}"
                        class="hidden w-full text-sm outline-none placeholder:text-lightGray placeholder:text-sm placeholder:font-light ms-3 text-primary md:block">
                </label>

                <ul id="searchResults"
                    class="absolute bottom-[120%] left-0 mt-2 w-full bg-white rounded-lg min-w-max shadow-lg hidden md:block text-primary font-medium text-sm max-h-[15rem] overflow-y-auto">
                    <!-- Search results will be dynamically added here -->
                </ul>
            </div>
            @php
                $userDevices = [];

                if (auth()->user()->role == 'dealer') {
                    $userDevices = Device::whereNotNull('dealer_id')
                        ->where('dealer_id', auth()->user()->dealer->id ?? 0)
                        ->select('imei', 'number_plate')
                        ->get()
                        ->toArray();
                }
                if (auth()->user()->role == 'client') {
                    $userDevices = Device::whereNotNull('client_id')
                        ->where('client_id', auth()->user()->client->id ?? 0)
                        ->select('imei', 'number_plate')
                        ->get()
                        ->toArray();
                }

                if (auth()->user()->role == 'admin' || auth()->user()->role == 'operator') {
                    $userDevices = Device::with(['client.user']) // Load client & user relationships
                        ->select('id', 'imei', 'number_plate', 'client_id') // Select required fields
                        ->get()
                        ->map(function ($device) {
                            return [
                                'imei' => $device->imei,
                                'number_plate' => $device->number_plate,
                                'client_last_name' => $device->client->last_name ?? '',
                                'client_user_name' => $device->client->user->name ?? '',
                            ];
                        })
                        ->toArray();
                }
            @endphp
            <script>
                const userDevices = @json($userDevices ?? []);
                let userRole = `{{ auth()->user()->role }}`;

                let hasPerm = false;

                // Handle search functionality
                const searchInput = document.getElementById('searchInput');
                const searchResults = document.getElementById('searchResults');

                searchInput.addEventListener('focus', handleSearch);
                searchInput.addEventListener('input', handleSearch);

                function handleSearch() {
                    const query = searchInput.value.trim().toLowerCase();
                    if (query.length > 0) {
                        if (userDevices && userDevices.length > 0 || userRole == 'admin' || userRole == 'operator') {
                            if (userRole == 'admin' || userRole == 'operator') {
                                hasPerm = true;
                            }
                            searchMarkers(query);
                        }
                    } else {
                        searchResults.innerHTML = ''; // Clear search results
                        searchResults.classList.add('hidden');
                    }
                }

                // Function to search and display filtered IMEIs
                function searchMarkers(query) {
                    fetch(`{{ url('/') }}/data/live/live_data.json?nocache=${new Date().getTime()}`)
                        .then(response => response.json())
                        .then(data => {
                            // Convert query to lowercase
                            const queryLower = query;

                            // Create a mapping of IMEI to number_plate, client last name, and client user name
                            const imeiMap = {};
                            userDevices.forEach(device => {
                                imeiMap[device.imei] = {
                                    number_plate: device.number_plate || '',
                                    client_last_name: device.client_last_name || '',
                                    client_user_name: device.client_user_name || '',
                                };
                            });

                            // Filter by IMEI, number_plate, client last name, or client user name
                            const filteredDevices = Object.keys(data).filter(imei => {
                                const details = imeiMap[imei] || {};
                                return (
                                    imei.includes(queryLower) ||
                                    details?.number_plate?.toLowerCase()?.includes(queryLower) ||
                                    details?.client_last_name?.toLowerCase()?.includes(queryLower) ||
                                    details?.client_user_name?.toLowerCase()?.includes(queryLower)
                                ) && (hasPerm || userDevices.some(device => device.imei === imei));
                            });

                            if (filteredDevices.length > 0) {
                                searchResults.innerHTML = filteredDevices.map(imei => `
        <li class="px-3 py-2 cursor-pointer search-result" data-imei="${imei}">
            <strong>IMEI:</strong> ${imei} <br>
            <small>@lang('messages.plate'): ${imeiMap[imei]?.number_plate || 'N/A'}</small><br>
            <small>@lang('messages.client'): ${imeiMap[imei]?.client_last_name || ''} ${imeiMap[imei]?.client_user_name || ''}</small>
        </li>
    `).join('');
                                searchResults.classList.remove('hidden');

                                // Click event for selecting device
                                document.querySelectorAll('.search-result').forEach(result => {
                                    result.addEventListener('click', function() {
                                        const imei = result.getAttribute('data-imei');
                                        openPlayer(imei);
                                        searchResults.innerHTML = ''; // Clear results
                                        searchResults.classList.add('hidden');
                                    });
                                });
                            } else {
                                searchResults.innerHTML = '<div>No results found</div>';
                                searchResults.classList.remove('hidden');
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching live data:', error);
                        });
                }

                // Your existing openPlayer function for focusing on the marker
                // function openPlayer(imei) {
                //     selectedDevice = imei;
                //     selectedImei = imei;
                //     document.getElementById('player').style.display = 'flex';
                //     document.getElementById('deviceInfo').innerText = `Device IMEI: ${imei}`;
                //     map.flyTo(markers[imei].getLatLng(), 15); // Zoom in on the marker
                //     updatePlayerInfo();
                //     if (isGeofenceMode) {
                //         fetchGeofences(imei);
                //     }
                //     checkBlockMotorState(imei);
                // }
            </script>

            {{-- <livewire:device-search />

            <script>
                window.addEventListener('selectDevice', imei => {

                    openPlayer(imei);
                });

                function openPlayer(imei) {
                    selectedDevice = imei;
                    selectedImei = imei;
                    document.getElementById('player').style.display = 'flex';
                    document.getElementById('deviceInfo').innerText = `Device IMEI: ${imei}`;
                    map.flyTo(markers[imei].getLatLng(), 15); // Zoom in on the marker
                    updatePlayerInfo();
                }
            </script> --}}
        @endif



        <div class="flex items-center justify-center flex-shrink-0 gap-4">
            <livewire:language-switcher />


            <!-- Notification Button -->
            <button class="relative modal-button" data-target="#notification-modal">
                <img class="size-6" src="{{ asset('assets/images/notification.svg') }}" alt="language">
            </button>

            <button x-data @click="$dispatch('open-modal', { name: 'logout-modal' })">
                <img class="size-6" src="{{ asset('assets/images/logout.svg') }}" alt="language">
            </button>

        </div>
    </div>
</nav>
