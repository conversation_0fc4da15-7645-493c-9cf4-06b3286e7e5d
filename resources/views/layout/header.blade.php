<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{ $title ?? 'Dashboard - MeMove' }}</title>

    <meta name="description"
        content="ME MOVE è il nostro prodotto GPS multifunzionale che propone ai clienti una gamma assortita di soluzioni e servizi di assistenza, sicurezza e controllo per tutti i tipi di veicoli." />

    <meta name="keywords" content="memove, multifunctional GPS product, GPS tracking device" />

    <link rel="canonical" href="https://www.memove.it" />

    <meta property="og:title" content="MeMove">
    <meta property="og:site_name" content="MeMove">
    <meta property="og:url" content="https://www.memove.it">
    <meta property="og:description"
        content="ME MOVE è il nostro prodotto GPS multifunzionale che propone ai clienti una gamma assortita di soluzioni e servizi di assistenza, sicurezza e controllo per tutti i tipi di veicoli.">
    <meta property="og:type" content="business">
    <meta property="og:image" content="{{ asset('assets/images/logo.svg') }}">

    <!-- favicon -->
    <link rel="shortcut icon" href="{{ asset('assets/images/favicon.png') }}" type="image/png">

    <!-- fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
        rel="stylesheet">

    <!-- css -->
    {{-- <link rel="stylesheet" href="{{ asset('assets/css/tailwind.css') }}" /> --}}
    <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/css/tailwind.css') }}?r={{rand(11111,999999)}}" />

    {{-- @vite('resources/css/app.css') --}}



    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <!-- <script>
        (function(w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-5J3LMKC');
    </script> -->


    @stack('styles')
</head>

<body class="font-roboto flex flex-shrink-0 bg-[#F7F8FA] overflow-x-hidden">

    <livewire:notification />
