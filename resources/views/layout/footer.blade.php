<!-- Modal for Updating Profile -->
<x-modal name="logout-modal">
    <x-slot:body>
        <div class="w-full">

            <div class="flex items-center justify-start">
                <img class="size-10" src="{{ asset('assets/images/logout-icon.svg') }}" alt="logout">


            </div>

            <div class="mt-4">
                <h3 class="font-medium">
                    {{ __('messages.logout_confirmation') }}
                </h3>

                <p class="text-sm mt-2 text-[#535862]">
                    {{ __('messages.logout_warning') }}
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="modal-close text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                        {{ __('messages.cancel') }}

                    </button>
                    <a href="{{ route('logout') }}"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        {{ __('messages.logout') }}

                    </a>
                </div>
            </div>
        </div>
    </x-slot:body>
</x-modal>


<!-- notifications modal -->
@if (auth()->check())
    <livewire:panel.notifications />
@endif






<script src="{{ asset('assets/js/script.js') }}"></script>



@stack('scripts')
</body>


</html>
