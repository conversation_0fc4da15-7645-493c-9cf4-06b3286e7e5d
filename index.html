<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document DDT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ccc;
        }

        .header {
            display: flex;
            justify-content: space-between;
        }

        .header .company-info {
            text-align: right;
        }

        .title {
            text-align: center;
            font-weight: bold;
            margin: 20px 0;
        }

        .section {
            margin-top: 20px;
        }

        .purple-box {
            background-color: #f1f1f1;
            padding: 10px;
        }

        .blue-box {
            background-color: #f1f1f1;
            padding: 10px;
        }

        .brown-box {
            background-color: #f1f1f1;
            padding: 10px;
            text-align: center;
        }

        .signature-boxes {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .signature {
            width: 48%;
            text-align: center;
            border-top: 1px solid #000;
            padding-top: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="logo">
                <img src="public/assets/images/logo.svg" alt="Company Logo" width="100">
            </div>
            <div class="company-info">
                <p>Alchimia Broker<br>VAT 14365721001<br>RUI B000578250</p>
                <p>Via San Leonardo, 51<br>84131 - Salerno</p>
                <p><a href="mailto:<EMAIL>"><EMAIL></a><br>0899340656</p>
            </div>
        </div>

        <!-- Document Title -->
        <div class="title" id="documentTitle">
            DOCUMENTO DI TRASPORTO N. ______ DEL __/__/____
        </div>

        <!-- Dealer Information -->
        <div class="section purple-box">
            <strong>Dealer Name:</strong> Blu Center Avellino Spa<br>
            <strong>Dealer Address:</strong> National Street, 209 - 83013 - Mercogliano (AV)
        </div>

        <!-- Device Information -->
        <div class="section blue-box">
            <strong>Devices to Ship:</strong>
            <ul id="deviceList">
                <li>IMEI: 35220131859266</li>
                <li>IMEI: 35220131855906</li>
                <li>IMEI: 35220131860736</li>
            </ul>
        </div>

        <!-- Device Count -->
        <div class="section brown-box" id="deviceCount">
            Total Devices: 3
        </div>

        <!-- Signature Section -->
        <div class="signature-boxes">
            <div class="signature">Transport Officer Signature</div>
            <div class="signature">Recipient Signature</div>
        </div>
    </div>

    <script>
        // Generate dynamic DDT number and date
        function generateDDTNumber() {
            const ddtNumber = Math.floor(Math.random() * 1000000) + 1; // Random DDT number
            const today = new Date();
            const dateString = today.toLocaleDateString('en-GB'); // Format date as DD/MM/YYYY

            document.getElementById('documentTitle').innerText = `DOCUMENTO DI TRASPORTO N. ${ddtNumber} DEL ${dateString}`;
        }

        // Populate Device List and Count
        function populateDeviceInfo() {
            const devices = [
                { imei: '35220131859266' },
                { imei: '35220131855906' },
                { imei: '35220131860736' }
            ];

            const deviceList = document.getElementById('deviceList');
            deviceList.innerHTML = '';

            devices.forEach(device => {
                const li = document.createElement('li');
                li.textContent = `IMEI: ${device.imei}`;
                deviceList.appendChild(li);
            });

            document.getElementById('deviceCount').innerText = `Total Devices: ${devices.length}`;
        }

        // Initialize document with DDT number, date, and device info
        generateDDTNumber();
        populateDeviceInfo();
    </script>
</body>

</html>