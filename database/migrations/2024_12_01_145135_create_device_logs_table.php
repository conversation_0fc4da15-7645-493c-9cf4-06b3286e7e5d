<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('device_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('device_id'); // Foreign key to devices table
            $table->unsignedBigInteger('operator_id')->nullable(); // Foreign key to users table (operator)
            $table->text('log'); // Log details
            $table->boolean('is_new')->default(true);
            $table->string('status')->default('open');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('device_id')->references('id')->on('devices')->cascadeOnDelete()->noActionOnUpdate();
            $table->foreign('operator_id')->references('id')->on('users')->nullOnDelete()->noActionOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('device_logs');
    }
};
