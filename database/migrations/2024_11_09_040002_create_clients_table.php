<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete()->noActionOnUpdate();
            $table->foreignId('dealer_id')->nullable()->constrained('dealers')->cascadeOnDelete()->noActionOnUpdate();
            $table->string('address')->nullable();
            $table->string('municipality')->nullable();
            $table->string('zip_code', 10)->nullable();
            $table->string('province')->nullable();
            $table->string('tax_code')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('passkey')->nullable();
            $table->string('company')->nullable();
            
            $table->string('type')->nullable();
            $table->string('last_name')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
