<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('device_logs', function (Blueprint $table) {
            $table->boolean('contacted_owner')->default(false);
            $table->boolean('contact_the_force_of_order')->default(false);
            $table->boolean('false_alaram')->default(false);
            $table->boolean('other')->default(false);
            $table->text('free_notes')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('device_logs', function (Blueprint $table) {
            $table->dropColumn([
                'contacted_owner',
                'contact_the_force_of_order',
                'false_alaram',
                'other',
                'free_notes',
            ]);
        });
    }
};
