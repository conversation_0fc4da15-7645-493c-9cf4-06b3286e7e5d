<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('device_test_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('devices')->cascadeOnDelete()->noActionOnUpdate();
            $table->string('gps_status')->nullable();
            $table->string('movement_status')->nullable();
            $table->string('ignition_status')->nullable();
            $table->string('engine_status')->nullable();
            $table->string('battery_voltage')->nullable();
            $table->string('ignition_tested_at')->nullable();
            $table->string('location')->nullable();
            $table->string('latitude')->nullable();
            $table->string('longitude')->nullable();
            $table->string('speed')->nullable();
            $table->string('last_update')->nullable();
            $table->string('signal')->nullable();
            $table->string('battery_level')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('device_test_results');
    }
};
