<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ddts', function (Blueprint $table) {
            $table->id();
            $table->string('ddt_path'); // Store the DDT PDF path
            $table->string('ddt_number'); // Store the DDT PDF path
            $table->foreignId('dealer_id')
            ->constrained('dealers') // Assumes a 'dealers' table exists
            ->cascadeOnDelete() // Cascade delete
            ->noActionOnUpdate(); // No action on update
            $table->string('type')->default('assigned'); // Store the DDT PDF path
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ddts');
    }
};
