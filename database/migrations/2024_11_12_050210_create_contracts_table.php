<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('devices')->cascadeOnDelete()->noActionOnUpdate();
            $table->foreignId('client_id')->constrained('clients')->cascadeOnDelete()->noActionOnUpdate();
            $table->foreignId('dealer_id')->constrained('dealers')->cascadeOnDelete()->noActionOnUpdate();
            $table->string('duration')->nullable();
            $table->string('signed')->nullable();
            $table->string('start_date')->nullable();
            $table->string('end_date')->nullable();

            $table->string('verification_vehicle_type')->nullable();
            $table->string('vehicle_brand')->nullable();
            $table->string('vehicle_model')->nullable();
            $table->string('vehicle_color')->nullable();
            $table->string('vehicle_number_plate')->nullable();
            $table->string('vehicle_registration_number')->nullable();
            $table->string('vehicle_registration_date')->nullable();
            $table->string('vehicle_chassis')->nullable();
            $table->string('vehicle_km')->nullable();
            $table->string('frame')->nullable();
            $table->string('starter_motor_block')->nullable();

            $table->string('contract_number')->nullable();
            $table->string('responded_at')->nullable();


            $table->string('status')->nullable();
            $table->text('service_details')->nullable();
            $table->string('installation_image')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};
