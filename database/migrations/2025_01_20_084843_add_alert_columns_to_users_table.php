<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('tampering_alert')->default(true)->after('fcm_token');
            $table->boolean('speed_limit_exceeded')->default(true)->after('tampering_alert');
            $table->boolean('power_off')->default(true)->after('speed_limit_exceeded');
            $table->boolean('geofence_exit')->default(true)->after('power_off');
            $table->boolean('signal_loss')->default(true)->after('geofence_exit');
            $table->boolean('low_battery')->default(true)->after('signal_loss');
            $table->boolean('contract_expiry')->default(true)->after('low_battery');
            $table->boolean('other_alerts')->default(true)->after('contract_expiry');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'tampering_alert',
                'speed_limit_exceeded',
                'power_off',
                'geofence_exit',
                'signal_loss',
                'low_battery',
                'contract_expiry',
                'other_alerts',
            ]);
        });
    }
};
